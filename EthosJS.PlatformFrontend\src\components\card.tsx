import { Card as CardMui, lighten, darken, styled } from '@mui/material';

interface StyledCardProps extends React.ComponentProps<typeof CardMui> {
	emphasis?: 'dark' | 'light';
}

const StyledCard = styled(CardMui, {
	shouldForwardProp: (prop) => prop !== 'emphasis',
	skipSx: false,
})<StyledCardProps>(({ theme }) => ({
	borderRadius: theme.spacing(1.5),
	variants: [
		{
			props: { emphasis: 'light' },
			style: {
				border: `1px solid ${lighten(theme.palette.primary.main, 0.85)}`,
				backgroundColor: lighten(theme.palette.primary.light, 0.95),
				...theme.applyStyles('dark', {
					border: `1px solid ${lighten(theme.palette.primary.main, 0.3)}`,
					backgroundColor: lighten(theme.palette.primary.light, 0.2),
				}),
			},
		},
		{
			props: { emphasis: 'dark' },
			style: {
				border: `1px solid ${darken(theme.palette.primary.main, 0.35)}`,
				boxShadow: `0 3px 10px rgba(0, 0, 0, 0.1)`,
				...theme.applyStyles('dark', {
					border: `1px solid ${darken(theme.palette.primary.main, 0.4)}`,
				}),
			},
		},
	],
}));

export default function Card({ emphasis = 'light', sx, ...props }: StyledCardProps) {
	return (
		<StyledCard
			emphasis={emphasis}
			sx={sx}
			{...props}
		/>
	);
}
