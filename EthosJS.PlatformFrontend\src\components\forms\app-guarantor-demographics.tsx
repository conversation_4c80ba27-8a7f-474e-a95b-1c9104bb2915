
import SectionHeader from "@components/section-header";
import { withForm } from "@hooks/app-form";
import { Autocomplete, Grid2 as Grid, TextField, } from "@mui/material";

const AppGuarantorDemographics = withForm({
   render: function Render({ form }) {
      return (
         <Grid container spacing={2}>
            <Grid size={12} >
               <SectionHeader title='Guarantor Demographics' />
            </Grid>
            <Grid size={{
               xs: 12,
               sm: 6,
            }}>
               <form.AppField name="patientInformation.firstName" children={(field) => <field.AppTextField label="Gender" required />} />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
               <form.AppField name="patientInformation.middleName" children={(field) => <field.AppDateField label="Date of Birth" required />} />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
               <form.AppField name="patientInformation.lastName" children={(field) => <field.AppTextField label="Social Security Number" required />} />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
               <form.AppField name="patientInformation.suffix" children={({ state, handleChange, handleBlur, getMeta }) => {
                  return (
                     <Autocomplete
                        options={['Jr.', 'Sr.', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X']}
                        value={state.value}
                        onChange={(_event, newValue) =>
                           handleChange(newValue ?? '')
                        }
                        onBlur={handleBlur}
                        renderInput={(params) => (
                           <TextField
                              {...params}
                              label="Relationship to Patient"
                              fullWidth
                              required
                           />
                        )}
                     />
                  )
               }
               } />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
               <form.AppField name="patientInformation.suffix" children={({ state, handleChange, handleBlur, getMeta }) => {
                  return (
                     <Autocomplete
                        options={['Jr.', 'Sr.', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X']}
                        value={state.value}
                        onChange={(_event, newValue) =>
                           handleChange(newValue ?? '')
                        }
                        onBlur={handleBlur}
                        renderInput={(params) => (
                           <TextField
                              {...params}
                              label="ID Type"
                              fullWidth
                              required
                           />
                        )}
                     />
                  )
               }
               } />
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
               <form.AppField name="patientInformation.suffix" children={({ state, handleChange, handleBlur, getMeta }) => {
                  return (
                     <Autocomplete
                        options={['Jr.', 'Sr.', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X']}
                        value={state.value}
                        onChange={(_event, newValue) =>
                           handleChange(newValue ?? '')
                        }
                        onBlur={handleBlur}
                        renderInput={(params) => (
                           <TextField
                              {...params}
                              label="ID Number"
                              fullWidth
                              required
                           />
                        )}
                     />
                  )
               }
               } />
            </Grid>
         </Grid>
      )
   }
})

export default AppGuarantorDemographics;
