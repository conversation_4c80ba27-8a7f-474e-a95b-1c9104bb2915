import { styled, Box, lighten } from "@mui/material";

const ContactMethodContainer = styled(Box)(({ theme }) => ({
    padding: theme.spacing(2),
    display: 'flex',
    justifyContent: 'flex-start',
    gap: theme.spacing(1),
    backgroundColor: lighten(theme.palette.primary.light, 0.85),
    color: theme.palette.text.primary,
    '&:not(:last-child)': {
        borderBottom: `1px solid ${lighten(theme.palette.primary.main, 0.85)}`,
    },
}));

export default ContactMethodContainer;