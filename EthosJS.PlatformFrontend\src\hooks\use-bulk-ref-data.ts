import { useQuery } from '@tanstack/react-query';
import { getApiReferenceSetsKeysOptions } from '@client/refdata/@tanstack/react-query.gen';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { ReferenceDataSet, ReferenceDataSetKeyValueDto } from '@client/refdata';

type RefDataResponse = {
  set: ReferenceDataSet;
  value: ReferenceDataSetKeyValueDto;
};

interface RefDataValue {
  id: number;
  title: string;
  description: string;
}

export function useBulkRefData({ ids }: { ids: Array<number> }) {
  const uniqueIds = Array.from(new Set(ids.filter(id => id && id > 0)));

  const { data, isFetching, error } = useQuery({
    ...getApiReferenceSetsKeysOptions({
      scopes: [PatientCreate.value, PatientRead.value],
      responseType: 'json',
      query: {
        ids: uniqueIds,
      },
    }),
    enabled: uniqueIds.length > 0,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    select: (data) => {
      if (!data) return new Map<number, RefDataValue>();

      const { items } = data as { items: RefDataResponse[] };
      const refDataMap = new Map<number, RefDataValue>();

      items.forEach((item) => {

        const { value } = item;
        const { id, values } = value;
        const { code, name } = (values as { code: string; name: string }) ?? {};

        if (id) {
          const refDataValue = {
            id,
            title: name ?? '',
            description: code ?? '',
          };
          refDataMap.set(id, refDataValue);
        }
      });
      return refDataMap;
    },
  });

  return {
    refDataMap: data || new Map<number, RefDataValue>(),
    isFetching,
    error,
  };
}

// Helper function to extract all reference data IDs from patient data
export function extractRefDataIds(patients: any[]): number[] {
  const ids: number[] = [];
  
  patients.forEach(patient => {
    // Demographics
    if (patient.demographics?.gender) ids.push(patient.demographics.gender);
    if (patient.demographics?.birthSex) ids.push(patient.demographics.birthSex);
    if (patient.demographics?.maritalStatus) ids.push(patient.demographics.maritalStatus);
    
    // Contact Information - Addresses
    patient.contactInformation?.addresses?.forEach((address: any) => {
      if (address.address?.state) ids.push(address.address.state);
      if (address.address?.country) ids.push(address.address.country);
    });
  });
  
  return ids;
}

// Helper function to resolve reference data values
export function resolveRefDataValue(id: number | null | undefined, refDataMap: Map<number, RefDataValue>): string {
  if (!id || id <= 0) return '';

  const refData = refDataMap.get(id);
  
  const result = refData?.title || "";

  return result;
}
