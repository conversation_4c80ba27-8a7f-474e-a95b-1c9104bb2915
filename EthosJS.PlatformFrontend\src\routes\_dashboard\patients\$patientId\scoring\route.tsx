import Card from '@components/card'
import ContenrArea from '@features/scoring/components/content-area'
import ScoringMenu from '@features/scoring/components/menu'
import { Box } from '@mui/material'
import { createFileRoute, useLocation } from '@tanstack/react-router'

export const Route = createFileRoute('/_dashboard/patients/$patientId/scoring')(
   {
      component: RouteComponent,
   },
)

function RouteComponent() {

   const { patientId } = Route.useParams();
   const { orderId, studyId } = Route.useSearch();
   const navigate = Route.useNavigate();
   const { pathname } = useLocation();

   return (
      <Card
         sx={{
            flex: 1,
            minHeight: 0,
            display: 'flex',
            gap: 2,
            p: 2,
            pb: 0,
            borderRadius: 2,
            borderBottomLeftRadius: 0,
            borderBottomRightRadius: 0,
         }}>
         <ScoringMenu
            patientId={patientId}
            orderId={orderId!}
            studyId={studyId!}
            activePath={pathname}
            onClick={(path) => {
               navigate({
                  to: `/patients/$patientId/schedule${path}`,
                  params: { patientId },
                  search: { orderId, studyId },
               });
            }}
            onSelect={({ orderId, studyId }) => {
               navigate({
                  to: '/patients/$patientId/schedule/appointment-creation/dashboard',
                  params: { patientId },
                  search: { orderId, studyId },
               });
            }}
         />
         <Card
            color="primary"
            sx={{
               flex: 1,
               minHeight: 0,
               position: 'relative',
               height: '100%',
               borderBottomLeftRadius: 0,
               borderBottomRightRadius: 0,
            }}>
            <Box
               sx={{
                  flex: 1,
                  overflow: 'auto',
                  height: 'calc(100% - 67px)',
                  pb: 0,
               }}>
               <ContenrArea />
            </Box>
         </Card>

      </Card>
   )
}
