import { Status } from "@config/status"
import { CheckCircle, WarningAmber } from "@mui/icons-material"
import { Box, lighten, PaletteColor, Stack, Theme, Typography, TypographyProps, useTheme } from "@mui/material"
import { ReactNode, useMemo } from "react"

type ThemeProperties = {
   root: {
      borderColor: string
      bgcolor: string
   }
   title: {
      foregroundColor: string
   }
   subTite: {
      foregroundColor: string
   }
} & { statusColor: PaletteColor }

export interface IStatusBanner {
   status: Status
   title: string
   titleProp?: TypographyProps
   subTitle?: string
   renderStatusIcon?: (themeProperties: ThemeProperties) => ReactNode
   renderContent?: (themeProperties: ThemeProperties) => ReactNode
   showIcon?: boolean
}

const getThemeProperties = (status: Status) => (theme: Theme) => {
   {
      if (status === Status.Process) {
         return {
            root: {
               borderColor: theme.palette.primary.dark,
               bgcolor: lighten(theme.palette.primary.light, .7)
            },
            title: {
               foregroundColor: theme.palette.primary.dark
            },
            subTite: {
               foregroundColor: theme.palette.primary.dark
            }
         }
      }

      if (status === Status.Success) {
         return {
            root: {
               borderColor: theme.palette.success.dark,
               bgcolor: lighten(theme.palette.success.light, .7)
            },
            title: {
               foregroundColor: theme.palette.success.dark
            },
            subTite: {
               foregroundColor: theme.palette.success.dark
            }
         }
      }

      if (status === Status.Warning) {
         return {
            root: {
               borderColor: theme.palette.warning.dark,
               bgcolor: lighten(theme.palette.warning.light, .7)
            },
            title: {
               foregroundColor: theme.palette.warning.dark
            },
            subTite: {
               foregroundColor: theme.palette.warning.dark
            }
         }
      }

      if (status === Status.Error) {
         return {
            root: {
               borderColor: theme.palette.error.dark,
               bgcolor: lighten(theme.palette.error.light, .7)
            },
            title: {
               foregroundColor: theme.palette.error.dark
            },
            subTite: {
               foregroundColor: theme.palette.error.dark
            }
         }
      }

      return {
         root: {
            borderColor: 'warning.dark',
            bgcolor: 'warning.light'
         },
         title: {
            foregroundColor: theme.palette.success.dark
         },
         subTite: {
            foregroundColor: theme.palette.success.dark
         }
      }
   }
}

const StatusBanner = ({
   title,
   titleProp,
   subTitle,
   status = Status.Default,
   renderContent,
   renderStatusIcon,
   showIcon = true,
}: IStatusBanner) => {
   const theme = useTheme();

   const satusTheme = getThemeProperties(status)(theme);

   const { root, title: titleTheme, subTite: subTiteTheme } = satusTheme;

   const getStatusColor = (status: Status) => {
      if (status === Status.Success) return theme.palette.success;
      if (status === Status.Warning) return theme.palette.warning;
      if (status === Status.Process) return theme.palette.primary;
      return theme.palette.info;
   }

   const statusColor = getStatusColor(status);
   const themeProperties = { ...satusTheme, statusColor };

   const statusIcon = useMemo(() => {

      if (typeof renderStatusIcon === 'function') {
         return renderStatusIcon(themeProperties)
      }

      if (status === Status.Success) {
         return <CheckCircle style={{ fill: statusColor.dark, fontSize: 24 }} />
      }

      if (status === Status.Warning) {
         return <WarningAmber style={{ fill: statusColor.dark, fontSize: 24 }} />
      }

      return null
   }, [status, themeProperties])

   return (
      <Box
         borderColor={root.borderColor}
         bgcolor={root.bgcolor}
         sx={{
            p: 1.5,
            borderRadius: 1,
            borderWidth: 1,
            borderStyle: 'solid',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
         }}
      >
         <Stack gap={.5}>
            <Typography
               color={titleTheme?.foregroundColor}
               {...titleProp}
               sx={{ fontWeight: 600, ...titleProp?.sx }}
            >
               {title}
            </Typography>
            {subTitle && (
               <Typography color={subTiteTheme?.foregroundColor} sx={{ fontWeight: 600 }} variant="body2">
                  {subTitle}
               </Typography>
            )}
            {renderContent?.(themeProperties)}
         </Stack>
         {showIcon && statusIcon}
      </Box>
   )
}

export default StatusBanner;