import { ReferenceDataSet, ReferenceDataSetKeyValueDto } from '@client/refdata';
import { getApiReferenceSetsKeysByIdOptions } from '@client/refdata/@tanstack/react-query.gen';
import { useQuery } from '@tanstack/react-query';

type RefDataResponse = {
	set: ReferenceDataSet;
	value: ReferenceDataSetKeyValueDto;
};

export function useRefDataValue({ id }: { id: number | null }) {
	const { data, isFetching, error } = useQuery({
		...getApiReferenceSetsKeysByIdOptions({
			responseType: 'json',
			path: { id: id! },
		}),
		enabled: !!id,
		select: (data) => {
			if (!data) return undefined;
			const { value } = data as RefDataResponse;
			const { values } = value;
			const { name, code } = (values as { name: string; code: string }) ?? {};
			return {
				title: name ?? '',
				description: code ?? '',
			};
		},
	});

	return {
		value: data,
		isFetching,
		error,
	};
}
