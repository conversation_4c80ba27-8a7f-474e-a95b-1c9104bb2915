import ChipSummary from "@components/chip-summary";
import CollapsibleMainCardContainer from "@components/collapsible-main-card-container";
import MainCardContainer from "@components/main-container/main-card-container";
import { MonitorHeartOutlined } from "@mui/icons-material";
import { Stack } from "@mui/material";
import { Monitor } from "lucide-react";
import { useState } from "react";

const StudySummary = () => {
   const [isExpanded, setIsExpanded] = useState<boolean>(false);

   return (
      <CollapsibleMainCardContainer
         mainContainerProps={{
            title: 'Study Summary',
            icon: <MonitorHeartOutlined />,
            emphasis: isExpanded ? 'high' : 'low',
            footerProps: {
               secondaryButton1: {
                  label: 'Lunch Study in Remote Desktop',
                  color: 'warning',
                  variant: 'contained',
                  startIcon: <Monitor />
               }
            }
         }}
         onToggleCollapse={setIsExpanded}
         collapse={isExpanded}
      >
         <Stack gap={2}>
            <MainCardContainer title='Study Information' color='gray'>
               <ChipSummary
                  items={[
                     {
                        label: 'Order ID',
                        value: 'ORDER-2025-001'
                     },
                     {
                        label: 'Patient',
                        value: '<PERSON>'
                     },
                     {
                        label: 'Study Type',
                        value: 'PSG'
                     },
                     {
                        label: 'Date',
                        value: '01/12/2025'
                     },
                     {
                        label: 'Duration',
                        value: '7h 30m'
                     },
                     {
                        label: 'Room',
                        value: 'Room 101'
                     },
                     {
                        label: 'Technician',
                        value: 'John Scott'
                     },
                     {
                        label: 'Location',
                        value: 'Northwest Sleep Center'
                     },
                     {
                        label: 'Ordering Physician',
                        value: 'Dr. Sarah Brown'
                     }
                  ]}
               />
            </MainCardContainer>
         </Stack>
      </CollapsibleMainCardContainer>
   )
}

export default StudySummary;