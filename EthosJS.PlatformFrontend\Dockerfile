# Stage 1: Build the app using Node.js
FROM node:23-slim AS builder
WORKDIR /app

# Declare build argument at the top level
ARG ENV_NAME
ARG VITE_APP_VERSION
ARG VITE_TFVC_CHANGESET


# Install pnpm globally
RUN npm install -g pnpm

# Copy dependency files and install dependencies
COPY package.json pnpm-lock.yaml ./
RUN pnpm install

# Copy all source files and build the app (ensure SWC is configured in your Vite setup)
COPY . .

# Use ENV_NAME in a way that affects the cache
RUN echo "Building with ENV_NAME=${ENV_NAME}" && \
    echo "LOG ENV_NAME VAR" && \
    echo "VITE_APP_VERSION=${VITE_APP_VERSION}" && \
    echo "VITE_TFVC_CHANGESET=${VITE_TFVC_CHANGESET}" && \
    pnpm run build --mode ${ENV_NAME}

# Stage 2: Serve the built app using the 'serve' package
FROM node:23-slim
WORKDIR /app

# Set VITE_APP_VERSION and VITE_TFVC_CHANGESET as environment variables for runtime
ARG VITE_APP_VERSION
ARG VITE_TFVC_CHANGESET
ENV VITE_APP_VERSION=${VITE_APP_VERSION}
ENV VITE_TFVC_CHANGESET=${VITE_TFVC_CHANGESET}


# Copy the production build from the builder stage
COPY --from=builder /app/dist ./dist

# Install serve globally to serve the static files
RUN npm install -g serve

# Expose the default port (serve usually listens on port 5000)
EXPOSE 5000

# Start the server with serve
CMD [ "serve", "-s", "dist" ]
