// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from '@hey-api/client-axios';
import type { GetApiRolesData, PostApiRolesData, GetApiRolesBuiltinData, DeleteApiRolesByRoleIdData, GetApiRolesByRoleIdData, PutApiRolesByRoleIdData, DeleteApiRolesByRoleIdAssignByUserIdData, PostApiRolesByRoleIdAssignByUserIdData, GetApiRolesAssignmentsByUserIdData, DeleteApiRolesScopesData, GetApiRolesScopesData, PostApiRolesScopesData, PutApiRolesScopesByScopeData, DeleteApiRolesByRoleIdScopesData, PostApiRolesByRoleIdScopesData } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

export const getApiRoles = <ThrowOnError extends boolean = false>(options?: Options<GetApiRolesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles',
        ...options
    });
};

export const postApiRoles = <ThrowOnError extends boolean = false>(options?: Options<PostApiRolesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const getApiRolesBuiltin = <ThrowOnError extends boolean = false>(options?: Options<GetApiRolesBuiltinData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles/builtin',
        ...options
    });
};

export const deleteApiRolesByRoleId = <ThrowOnError extends boolean = false>(options: Options<DeleteApiRolesByRoleIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles/{roleId}',
        ...options
    });
};

export const getApiRolesByRoleId = <ThrowOnError extends boolean = false>(options: Options<GetApiRolesByRoleIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles/{roleId}',
        ...options
    });
};

export const putApiRolesByRoleId = <ThrowOnError extends boolean = false>(options: Options<PutApiRolesByRoleIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles/{roleId}',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const deleteApiRolesByRoleIdAssignByUserId = <ThrowOnError extends boolean = false>(options: Options<DeleteApiRolesByRoleIdAssignByUserIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles/{roleId}/assign/{userId}',
        ...options
    });
};

export const postApiRolesByRoleIdAssignByUserId = <ThrowOnError extends boolean = false>(options: Options<PostApiRolesByRoleIdAssignByUserIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles/{roleId}/assign/{userId}',
        ...options
    });
};

export const getApiRolesAssignmentsByUserId = <ThrowOnError extends boolean = false>(options: Options<GetApiRolesAssignmentsByUserIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles/assignments/{userId}',
        ...options
    });
};

export const deleteApiRolesScopes = <ThrowOnError extends boolean = false>(options?: Options<DeleteApiRolesScopesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).delete<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles/scopes',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const getApiRolesScopes = <ThrowOnError extends boolean = false>(options?: Options<GetApiRolesScopesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles/scopes',
        ...options
    });
};

export const postApiRolesScopes = <ThrowOnError extends boolean = false>(options?: Options<PostApiRolesScopesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles/scopes',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const putApiRolesScopesByScope = <ThrowOnError extends boolean = false>(options: Options<PutApiRolesScopesByScopeData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles/scopes/{scope}',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const deleteApiRolesByRoleIdScopes = <ThrowOnError extends boolean = false>(options: Options<DeleteApiRolesByRoleIdScopesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles/{roleId}/scopes',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};

export const postApiRolesByRoleIdScopes = <ThrowOnError extends boolean = false>(options: Options<PostApiRolesByRoleIdScopesData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<unknown, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/roles/{roleId}/scopes',
        ...options,
        headers: {
            'Content-Type': 'application/json-patch+json',
            ...options?.headers
        }
    });
};