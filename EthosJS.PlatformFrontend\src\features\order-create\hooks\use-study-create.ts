import { PatientCreate, PatientRead } from '@auth/scopes';
import { EthosWorkflowsApiCreateStudyDto, SystemTextJsonNodesJsonNode } from '@client/workflows';
import { postApiStudyDraftMutation } from '@client/workflows/@tanstack/react-query.gen';
import { useMutation } from '@tanstack/react-query';
import { useCallback } from 'react';

export default function useStudyCreate() {
	const {
		mutate: createStudyMutation,
		isPending: isCreatingStudy,
		data: createStudyData,
		reset: resetCreateStudyMutation,
		error: createStudyError,
	} = useMutation({
		...postApiStudyDraftMutation({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		}),
		onError(error) {
			console.error(error);
		},
	});

	const createStudy = useCallback(
		async (
			data: Partial<EthosWorkflowsApiCreateStudyDto>,
			successCallback?: (studyId?: string) => void
		) => {
			createStudyMutation(
				{
					body: data as unknown as { [key: string]: SystemTextJsonNodesJsonNode },
				},
				{
					onSuccess(mutationData) {
						const { entityId } = mutationData;
						if (successCallback) {
							successCallback(entityId);
						}
					},
				}
			);
		},
		[createStudyMutation]
	);

	return {
		createStudy,
		isCreatingStudy,
		createStudyData,
		resetCreateStudyMutation,
		createStudyError,
	};
}
