import { Box, Button, ButtonProps, SxProps } from "@mui/material";
import CardFooter from "./card-footer";

interface FormFooterProps {
    onDelete?: ButtonProps['onClick'];
    onCancel?: ButtonProps['onClick'];
    onSubmit?: () => void;
    isUpdate?: boolean;
    isDirty?: boolean;
    sx?: SxProps;
}

export default function FormFooter({
    onDelete,
    onCancel,
    onSubmit,
    isUpdate = false,
    isDirty = false,
    sx
}: FormFooterProps) {
    return (
        <CardFooter sx={{ justifyContent: 'flex-end', ...sx }}>
            {onDelete && (
                <Button variant="outlined" color="error" onClick={onDelete}>
                    Delete
                </Button>
            )}
            <Box sx={{ flexGrow: 1 }}></Box>
            <Button
                variant="outlined"
                onClick={onCancel}
            >
                Cancel
            </Button>
            <Button
                disabled={!isDirty}
                variant="contained"
                onClick={onSubmit}
            >
                {isUpdate ? 'Update' : 'Add'}
            </Button>
        </CardFooter>
    );
}

export type {FormFooterProps}