import { getApiReferenceSetsKeysOptions } from '@client/refdata/@tanstack/react-query.gen';
import ChipSummary from '@components/chip-summary';
import MainCardContainer from '@components/main-container/main-card-container';
import { useQuery } from '@tanstack/react-query';
import { map } from 'lodash';
import { useRefDataValue } from '../../../hooks/use-ref-data-value';
import { ReferenceDataSet, ReferenceDataSetKeyValueDto } from '@client/refdata';

type RefDataResponse = {
	set: ReferenceDataSet;
	value: ReferenceDataSetKeyValueDto;
};

interface StudyAttributesProps {
	encounterTypeId: number | null;
	studyTypeId: number | null;
	studyAttributes: Array<number>;
}

export default function StudyAttributes({
	encounterTypeId,
	studyTypeId,
	studyAttributes,
}: StudyAttributesProps) {
	const { value: studyType, isFetching: isFetchingStudyType } = useRefDataValue({
		id: studyTypeId,
	});
	const { value: encounterType, isFetching: isFetchingEncounterType } = useRefDataValue({
		id: encounterTypeId,
	});

	const { data: studyAttributesData } = useQuery({
		...getApiReferenceSetsKeysOptions({
			responseType: 'json',
			query: {
				ids: studyAttributes,
			},
		}),
		enabled: !!studyAttributes.length,
		select: (data) => {
			if (!data) return [];
			const { items } = data as { items: RefDataResponse[] };
			return items;
		},
	});

	if (isFetchingStudyType) {
		return <MainCardContainer title="Loading..." />;
	}

	return (
		<MainCardContainer title={studyType?.title ?? ''}>
			<ChipSummary
				variant="outlined"
				items={[
					{
						label: 'Encounter Type',
						value: isFetchingEncounterType ? 'Loading...' : (encounterType?.title ?? ''),
					},

					...map(studyAttributesData ?? [], (item) => ({
						label: '',
						//@ts-expect-error Type Mismatch
						value: item?.values?.name ?? '',
					})),
				]}
			/>
		</MainCardContainer>
	);
}
