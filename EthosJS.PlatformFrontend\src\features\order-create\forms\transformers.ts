import { EthosWorkflowsApiCreateStudyDto } from '@client/workflows';
import { entries, reduce, map } from 'lodash';
import z from 'zod';

const studyFormDataTransformer = z.any().transform((raw: EthosWorkflowsApiCreateStudyDto) => {
	return {
		...raw,
		encounterType: raw.encounterType ? Number(raw.encounterType) : null,
		studyType: raw.studyType ? Number(raw.studyType) : null,
		studyAttributes: Array.isArray(raw.studyAttributes)
			? reduce(
					raw.studyAttributes,
					(acc, curr) => {
						acc[curr.key] = curr.value;
						return acc;
					},
					{} as { [key: string]: number }
				)
			: {},
		insurances: raw.insurances ?? [],
	};
});

const studyPreferencesTransformer = z.any().transform((raw: EthosWorkflowsApiCreateStudyDto) => {
	return {
		orderId: raw.orderId,
		encounterType: raw.encounterType ? raw.encounterType.toString() : null,
		studyType: raw.studyType ? raw.studyType.toString() : null,
		studyAttributes: raw.studyAttributes
			? map(entries(raw.studyAttributes), ([key, value]) => ({ key, value }))
			: [],
		insurances: raw.insurances ?? [],
	};
});

function studyFormDataToDto(data: EthosWorkflowsApiCreateStudyDto) {
	return studyFormDataTransformer.parse(data);
}

function studyDtoToFormData(data?: Omit<EthosWorkflowsApiCreateStudyDto, 'orderId'>) {
	if (!data) return {};
	return studyPreferencesTransformer.parse(data);
}

export { studyFormDataToDto, studyDtoToFormData };
