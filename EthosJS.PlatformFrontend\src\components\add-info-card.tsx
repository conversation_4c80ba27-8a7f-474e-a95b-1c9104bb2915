import { <PERSON><PERSON>ontent, Icon<PERSON>utton, lighten, SxProps } from "@mui/material";
import React, { PropsWithChildren, use, useState } from "react";
import { Add, PhoneCallbackOutlined, } from "@mui/icons-material";
import InfoSummary from "./info-summary";
import Card from "./card";
import CardHeader from "./card-header";

interface AddInfoCardProps extends PropsWithChildren {
    onClick: () => void; title: string, showHeader: boolean
    icon?: React.ReactNode;
    actionIcon?: React.ReactNode
    sx?: SxProps
    bodyPadding?: any
}

export default function AddInfoCard({ title, children, onClick, showHeader, icon, actionIcon, sx, bodyPadding }: AddInfoCardProps) {

    if (showHeader) {
        return (
            <Card>
                <CardHeader
                    title={title}
                    avatar={icon}
                    slotProps={{
                        title: { variant: 'h6' },
                    }}
                    action={
                        <IconButton onClick={onClick} color="inherit" sx={{ mr: 1, mt: -1 }}>
                            <Add fontSize="large" />
                        </IconButton>
                    } />
            </Card>
        )
    }

    return (
        <Card emphasis="dark" sx={{ mb: 2, ...sx }}>
            <CardHeader emphasis="dark" title={title} avatar={icon} action={
                <IconButton onClick={onClick} color="inherit" sx={{ mr: 1, mt: -1 }}>
                    {actionIcon ?? <Add />}
                </IconButton>
            } />
            <CardContent sx={(theme) => {
                return ({
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 2,
                    backgroundColor: lighten(theme.palette.primary.light, 0.95),
                    p: bodyPadding
                })
            }}
            >
                {children}
            </CardContent>
        </Card>
    );
}