import AddInfoCard from '@components/add-info-card';
import ArrayFieldContainer from '@components/array-field-container';
import { withForm } from '@hooks/app-form';
import { Phone } from '@mui/icons-material';
import PhoneNumberForm, {
	defaultValues as phoneNumberFormDefaultValues,
	PhoneNumberData,
} from '../app-phone-number-form/app-phone-number.form';
import { formatPhoneNumberSummary } from '../create-patient/utils';

interface FormValues {
	phoneNumbers: PhoneNumberData[];
}

const PhoneNumber = withForm<FormValues>({
	defaultValues: {
		phoneNumbers: [],
	},
	render: function Render({ form }) {
		return (
			<>
				<form.AppField
					name="phoneNumbers"
					mode="array">
					{({ pushValue, removeValue, state, replaceValue }) => (
						<AddInfoCard
							title="Phone Numbers"
							icon={<Phone />}
							onClick={() => pushValue(phoneNumberFormDefaultValues)}
							showHeader={state.value.length === 0}>
							{state.value.map((phoneNumber, i) => (
								<ArrayFieldContainer
									key={i}
									initialEditState={true}
									items={formatPhoneNumberSummary(state.value[i])}
									title="Phone Number">
									{({ setEdit }) => (
										<PhoneNumberForm
											formValues={state.value[i]}
											onAdd={(data) => {
												replaceValue(i, data);
												setEdit(false);
											}}
											onCancel={() => {
												removeValue(i);
												setEdit(false);
											}}
										/>
									)}
								</ArrayFieldContainer>
							))}
						</AddInfoCard>
					)}
				</form.AppField>
			</>
		);
	},
});

export default PhoneNumber;
