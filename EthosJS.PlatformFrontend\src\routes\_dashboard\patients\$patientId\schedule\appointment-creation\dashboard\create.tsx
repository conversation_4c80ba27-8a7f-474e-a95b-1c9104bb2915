import { createFileRoute } from '@tanstack/react-router';
import { z } from 'zod';
import CreateStep from '@features/scheduling/components/create/create-step';

const validateSearch = z.object({
	orderId: z.string(),
	studyId: z.string().optional(),
});

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create'
)({
	component: RouteComponent,
	validateSearch,
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const navigate = Route.useNavigate();

	const onSuccessCallback = (appointmentId: string) => {
		navigate({
			to: '/patients/$patientId/schedule/appointment-creation/dashboard/preview',
			params: { patientId },
			search: { orderId, studyId },
		});
	};

	const onCancelCreate = () => {
		navigate({
			to: '/patients/$patientId/schedule/appointment-creation/dashboard',
			params: { patientId },
			search: { orderId, studyId },
			replace: true,
		});
	};

	return (
		<CreateStep
			patientId={patientId}
			studyId={studyId!}
			orderId={orderId}
			onSuccessCallback={onSuccessCallback}
			onCancelCreate={onCancelCreate}
		/>
	);
}
