import { PublicClientApplication } from '@azure/msal-browser';
import { PatientCreate, PatientRead } from './scopes';

async function requireAuth(msalInstance: PublicClientApplication) {
	// Get all currently signed-in accounts
	await msalInstance.handleRedirectPromise();
	const accounts = msalInstance.getAllAccounts();
	if (!accounts || accounts.length === 0) {
		// This triggers the redirect login flow
		await msalInstance.loginRedirect();
		// Throwing an error here stops the route from loading further
		throw new Error('User not authenticated, redirecting to login.');
	}
}

async function acquireToken(
	msalInstance: PublicClientApplication,
	scopes: string[] = [PatientRead.value, PatientCreate.value]
) {
	const request = {
		scopes,
	};
	try {
		const response = await msalInstance.acquireTokenSilent(request);
		return response.accessToken;
	} catch (error) {
		console.error(error);
		// Fallback: prompt the user interactively if silent acquisition fails
		// This triggers the redirect login flow
		await msalInstance.acquireTokenRedirect(request);
	}
}

export { requireAuth, acquireToken };
