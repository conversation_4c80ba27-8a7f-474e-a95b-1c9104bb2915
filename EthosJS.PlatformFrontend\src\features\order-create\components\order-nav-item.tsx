import { Patient<PERSON><PERSON>, PatientRead } from '@auth/scopes';
import { EthosWorkflowsWorkflowAddNewOrderAddNewOrderState } from '@client/workflows';
import {
	postApiAddNewOrderListOptions,
	getApiAddNewOrderStateByIdOptions,
} from '@client/workflows/@tanstack/react-query.gen';
import LeftMenu, { Status } from '@components/left-menu';
import { BadgeOutlined } from '@mui/icons-material';
import { UseQueryResult, useQuery, useQueries } from '@tanstack/react-query';
import { every, find } from 'lodash';
import { useMemo } from 'react';

const combineResults = (
	results: UseQueryResult<EthosWorkflowsWorkflowAddNewOrderAddNewOrderState>[]
) => {
	return {
		data: results.map((result) => result.data),
		isLoading: results.some((result) => result.isLoading),
		isFetching: results.some((result) => result.isFetching),
		isError: results.some((result) => result.isError),
		error: results.find((result) => result.isError)?.error,
	};
};

/**
 * @deprecated This component is deprecated. Use the new OrderNavigation component instead.
 */
// @ts-expect-error deprecated
export function OrderNavItem({
	patientId,
	onClick,
	selected,
}: {
	patientId: string;
	onClick: (orderWfId: string, orderId: string) => void;
	selected: boolean;
}) {
	const { data: orderWorkflowIds } = useQuery({
		...postApiAddNewOrderListOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			body: {
				Patient: patientId,
			},
		}),
		enabled: !!patientId,
		initialData: [],
	});

	const { data: orderData } = useQueries({
		queries: orderWorkflowIds?.length
			? orderWorkflowIds.map((id) => ({
					...getApiAddNewOrderStateByIdOptions({
						path: { id },
						scopes: [PatientCreate.value, PatientRead.value],
						responseType: 'json',
					}),
					enabled: !!id,
				}))
			: [],
		combine: combineResults,
	});

	const { status, orderToNavigateTo, name } = useMemo(() => {
		if (!orderData) {
			return {
				status: Status.None,
				orderToNavigateTo: null,
				name: 'Create Order',
			};
		}
		const completed = every(orderData, (state) => {
			const stateData = state && 'stateData' in state ? state.stateData : { $type: '' };
			const { $type } = stateData;
			return $type === 'OrderSubmitted';
		});
		const orderToNavigateTo = find(orderData, (state) => {
			const stateData = state && 'stateData' in state ? state.stateData : { $type: '' };
			const { $type } = stateData;
			return $type !== 'OrderSubmitted';
		});
		return {
			status: completed ? Status.Completed : Status.InProgress,
			orderToNavigateTo: orderToNavigateTo ?? orderData[0],
			name: completed ? 'Order Information' : 'Create Order',
		};
	}, [orderData]);

	return (
		<LeftMenu
			name={name}
			status={status}
			selected={selected}
			stepNumber={2}
			onClick={() => {
				if (orderToNavigateTo) {
					const orderWfId = orderToNavigateTo.id;
					const entityLinks =
						orderToNavigateTo && 'entityLinks' in orderToNavigateTo
							? orderToNavigateTo.entityLinks
							: { Order: '' };
					const orderId = entityLinks.Order;
					onClick(orderWfId, orderId);
				}
			}}
			icon={BadgeOutlined}
			disabled={status === Status.None}
		/>
	);
}
