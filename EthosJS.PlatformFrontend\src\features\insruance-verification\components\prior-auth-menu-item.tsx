import MenuItem from '@components/menu/menu-item';
import { ScrollText } from 'lucide-react';
import useInsuranceVerification from '../hooks/use-insurance-verification';
import { Status } from '@config/status';

export default function PriorAuthMenuItem({
	studyId,
	selected,
	onClick,
}: {
	studyId: string;
	selected: boolean;
	onClick: () => void;
}) {
	const { verificationStatus } = useInsuranceVerification({
		studyId,
	});

	const getStatus = () => {
		if (verificationStatus?.currentCoarseState === 'VerificationSuccessfulAuthRequired') {
			return Status.Warning;
		}
		if (verificationStatus?.currentCoarseState === 'VerificationSuccessful') {
			return Status.Success;
		}
		return Status.NotStarted;
	};

	return (
		<MenuItem
			title="Prior Authorization"
			value="prior-authorization"
			icon={ScrollText}
			size="medium"
			status={getStatus()}
			selected={selected}
			onClick={onClick}
		/>
	);
}
