import {
	EthosWorkflowsApiCreatePatientInputDto,
	EthosWorkflowsApiDemographicsDto,
	EthosWorkflowsApiPatientInformationDto,
} from '@client/workflows';
import NotificationBanner from '@components/notification-banner';
import NotificationSnackbar, { NotificationState } from '@components/notification-snackbar';
import { addressesWithUseTransformer } from '@features/patient-create/forms';
import InsurancesForm from '@features/patient-create/forms/insurances.form';
import usePatient from '@features/patient-create/hooks/use-patient';
import usePatientValidation from '@features/patient-create/hooks/use-patient-validation';
import { InsurancesData, PatientState, StepPropsNew } from '@features/patient-create/types';
import { getNewPatientState } from '@features/patient-create/utils';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';

export default function InsurancesStep({ patientId, successCallback }: StepPropsNew) {
	const { patientData, updatePatient, updatePatientError, resetUpdatePatientMutation, saveDraft } =
		usePatient({
			patientId,
		});

	const { validateFormatted } = usePatientValidation();

	const { data } = patientData ?? {};

	const [notification, setNotification] = useState<NotificationState>({
		message: '',
		severity: 'info',
	});

	const resetToast = () => {
		setNotification({
			message: '',
			severity: 'info',
		});
	};

	const patientState = (data?._state as unknown as PatientState) ?? {};
	const patientInformation = data?.patientInformation;
	const patientDemographics = data?.demographics;
	const insurances = data?.insurances;

	const savedData = useMemo(() => {
		return {
			insurances,
		} as InsurancesData;
	}, [insurances]);

	const stepStatus = patientState.stepState?.Insurances ?? 'NotStarted';

	const { patientName, dateOfBirth } = useMemo(() => {
		if (!patientInformation || !patientDemographics) {
			return {
				patientName: '',
				dateOfBirth: '',
			};
		}
		const { firstName, lastName } = patientInformation as EthosWorkflowsApiPatientInformationDto;
		const patientName = `${firstName} ${lastName}`.trim();
		const { dateOfBirth = '' } = patientDemographics as EthosWorkflowsApiDemographicsDto;
		return {
			patientName,
			dateOfBirth,
		};
	}, [patientInformation, patientDemographics]);

	return (
		<>
			<NotificationSnackbar
				notification={notification}
				onCloseToast={resetToast}
			/>
			<NotificationBanner
				message={updatePatientError?.message}
				severity="error"
				scrollIntoView
				onClose={resetUpdatePatientMutation}
			/>
			<InsurancesForm
				savedData={savedData}
				patientName={patientName}
				dateOfBirth={dateOfBirth as string}
				onSubmit={(newData) => {
					updatePatient(
						{
							...data,
							insurances: newData.insurances?.map((insurance) => ({
								...insurance,
								address: insurance.address
									? addressesWithUseTransformer(insurance.address)
									: undefined,
							})) as EthosWorkflowsApiCreatePatientInputDto['insurances'],
						},
						getNewPatientState(patientState, 'Insurances', 'Guardians'),
						successCallback
					);
				}}
				onSaveDraft={(newData) => {
					saveDraft(
						{
							...data,
							insurances: newData.insurances?.map((insurance) => ({
								...insurance,
								address: insurance.address
									? addressesWithUseTransformer(insurance.address)
									: undefined,
							})) as EthosWorkflowsApiCreatePatientInputDto['insurances'],
						},
						{
							flowState: {
								...patientState.flowState,
								lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
							},
							stepState: {
								...patientState.stepState,
							},
						},
						() => {
							setNotification({
								message: 'Draft saved successfully',
								severity: 'success',
							});
						}
					);
				}}
				onValidate={validateFormatted}
				isUpdate={stepStatus === 'Complete'}
			/>
		</>
	);
}
