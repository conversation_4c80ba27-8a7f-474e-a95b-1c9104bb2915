import { Store } from '@tanstack/react-store';
import { Flow } from './Flow';
import { ITypeDef, IConfig } from './TypeDef';

interface IWorkflowState {
  selectedFlow: Flow | null;
}

export type GetDataStructure = (key: string) => ITypeDef | undefined;

export class Workflow {
  flows: Map<string, Flow>;
  types: Map<string, ITypeDef>;

  workflowStore = new Store<IWorkflowState>({
    selectedFlow: null
  });

  constructor({ flows, types }: IConfig) {
    this.flows = new Map(
      Object.entries(flows).map(([name, flowConfig]) => [name, new Flow(flowConfig, types)])
    );
    this.types = new Map(Object.entries(types));
    this.workflowStore.setState((state) => ({
      ...state,
      selectedFlow: this.flows.size > 0 ? Array.from(this.flows.values())[0] : null
    }));
  }

  setSelectedFlow(flow: Flow) {
    this.workflowStore.setState((state) => ({
      ...state,
      selectedFlow: flow
    }));
  }

  getNextFlow() {
    if (this.workflowStore.state.selectedFlow) {
      const flows = Array.from(this.flows.values());
      const index = flows.indexOf(this.workflowStore.state.selectedFlow);
      return flows[index + 1] ?? null;
    }
  }

  getStore() {
    return this.workflowStore;
  }
  getFlow(name: string) {
    return this.flows.get(name);
  }
  getType(key: string) {
    return this.types.get(key);
  }

  getDataStructure: GetDataStructure = (key: string) => {
    return this.types.get(key);
  };
}
