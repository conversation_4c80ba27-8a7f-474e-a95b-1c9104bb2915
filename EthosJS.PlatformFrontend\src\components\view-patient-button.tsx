import { Button } from '@mui/material';
import { useMutation } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { Visibility } from '@mui/icons-material';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { postApiAddNewPatientList } from '@client/workflows';

interface ViewPatientButtonProps {
    patientId?: string;
}

export default function ViewPatientButton({ patientId }: ViewPatientButtonProps) {
    const navigate = useNavigate();


    const { mutate: getExistingWorkflow, isPending: isFetching, } = useMutation({
        mutationFn: async () => {
            return await postApiAddNewPatientList({
                body: { PatientId: patientId as string },
                scopes: [PatientCreate.value, PatientRead.value],
                responseType: 'json'
            });
        },
        onSuccess: (response) => {
            const { data } = response;
            if (data) {
                if (!data.length) return;
                navigateToWorkflow(data[0]);
            }
        },
    });

    const navigateToWorkflow = (workflowId: string) => {
        navigate({
            to: '/patients/$patientId/patient-information',
            params: { patientId: patientId as string },
            search: {
                patientWfId: workflowId,
            }
        });
    };

    return (
        <Button
            startIcon={<Visibility />}
            onClick={() => getExistingWorkflow()}
            disabled={isFetching || !patientId}
            variant="contained"
            size="small"
        >
            View Patient
        </Button>
    );
}
