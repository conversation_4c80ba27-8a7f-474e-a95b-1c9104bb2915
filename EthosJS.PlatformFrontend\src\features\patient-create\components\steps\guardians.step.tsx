import { EthosWorkflowsApiDemographicsDto } from '@client/workflows';
import NotificationBanner from '@components/notification-banner';
import NotificationSnackbar, { NotificationState } from '@components/notification-snackbar';
import GuardiansForm from '@features/patient-create/forms/guardians.form';
import usePatient from '@features/patient-create/hooks/use-patient';
import usePatientValidation from '@features/patient-create/hooks/use-patient-validation';
import { GuardiansData, PatientState, StepPropsNew } from '@features/patient-create/types';
import { getNewPatientState } from '@features/patient-create/utils';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';

export default function GuardiansStep({ patientId, successCallback }: StepPropsNew) {
	const { patientData, updatePatient, updatePatientError, resetUpdatePatientMutation, saveDraft } =
		usePatient({
			patientId,
		});

	const { validateFormatted } = usePatientValidation();

	const [notification, setNotification] = useState<NotificationState>({
		message: '',
		severity: 'info',
	});

	const resetToast = () => {
		setNotification({
			message: '',
			severity: 'info',
		});
	};

	const { data } = patientData ?? {};
	const patientState = (data?._state as unknown as PatientState) ?? {};
	const patientDemographics = data?.demographics;

	const patientDob = useMemo(() => {
		if (!patientDemographics) {
			return '';
		}
		const { dateOfBirth } = patientDemographics as EthosWorkflowsApiDemographicsDto;
		return dateOfBirth ?? '';
	}, [patientDemographics]);

	const savedData = useMemo(() => {
		return {
			guardians: data?.guardians ?? [],
		} as GuardiansData;
	}, [data?.guardians]);

	return (
		<>
			<NotificationSnackbar
				notification={notification}
				onCloseToast={resetToast}
			/>
			<NotificationBanner
				message={updatePatientError?.message}
				severity="error"
				scrollIntoView
				onClose={resetUpdatePatientMutation}
			/>
			<GuardiansForm
				savedData={savedData}
				patientDob={patientDob}
				onSubmit={(newData) => {
					updatePatient(
						{
							...data,
							guardians: newData.guardians,
						},
						getNewPatientState(patientState, 'Guardians', 'ClinicalConsiderations'),
						successCallback
					);
				}}
				onSaveDraft={(data) => {
					saveDraft(
						{
							...data,
							guardians: data.guardians,
						},
						{
							flowState: {
								...patientState.flowState,
								lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
							},
							stepState: {
								...patientState.stepState,
							},
						},
						() => {
							setNotification({
								message: 'Draft saved successfully',
								severity: 'success',
							});
						}
					);
				}}
				onValidate={validateFormatted}
			/>
		</>
	);
}
