import { GetApiInsuranceVerificationStatusByJobIdResponse } from '@client/workflows';
import MainCardContainer from '@components/main-container/main-card-container';
import { Chip } from '@mui/material';
import { Clock } from 'lucide-react';

export default function ProcessingStatus({
	verificationStatus: _verificationStatus,
}: {
	verificationStatus: GetApiInsuranceVerificationStatusByJobIdResponse;
}) {
	return (
		<MainCardContainer
			title="Insurance Verification"
			headerSize="medium"
			icon={<Clock />}
			color="primary"
			emphasis="high"
			descriptionSubheader="Verifying Insurance with Carrier... This may take a few moments."
			customAction={
				<Chip
					color="primary"
					label="Processing"
					sx={{ borderRadius: 2 }}
				/>
			}
		/>
	);
}
