// src/msalConfig.js

import { LogLevel, PublicClientApplication } from '@azure/msal-browser';

const importMeta = import.meta.env;

const system = import.meta.env.DEV
  ? {
      allowNativeBroker: false,
      loggerOptions: {
        loggerCallback: (level: LogLevel, message: string, containsPii: boolean) => {
          if (containsPii) {
            return;
          }
          switch (level) {
            case LogLevel.Error:
              console.error(message);
              return;
            case LogLevel.Info:
              console.info(message);
              return;
            case LogLevel.Verbose:
              console.debug(message);
              return;
            case LogLevel.Warning:
              console.warn(message);
              return;
          }
        }
      },
      logLevel: LogLevel.Error
    }
  : undefined;

const msalConfig = {
  auth: {
    clientId: importMeta.VITE_MSAL_CLIENT_ID, // Your Application (client) ID
    authority: `https://${importMeta.VITE_MSAL_TENANT_NAME}.b2clogin.com/${importMeta.VITE_MSAL_TENANT_NAME}.onmicrosoft.com/${importMeta.VITE_MSAL_FLOW}`, // Your tenant ID
    knownAuthorities: [`${importMeta.VITE_MSAL_TENANT_NAME}.b2clogin.com`],
    redirectUri: importMeta.VITE_MSAL_DEFAULT_REDIRECT_URI, // The URI where your app is served
    postLogoutRedirectUri: importMeta.VITE_MSAL_POST_LOGOUT_REDIRECT_URI,
    navigateToLoginRequestUrl: false
  }
  //system
};

const loginRequest = {
  scopes: ['openid', 'profile', 'email'] // Basic scopes; add extra if needed
};
const msalInstance = new PublicClientApplication(msalConfig);

async function initializeMSAL() {
  try {
    // Await the initialization before calling other APIs
    await msalInstance.initialize();
    // Now you can safely use other MSAL methods.
    const response = await msalInstance.handleRedirectPromise();
    if (response && response.account) {
      msalInstance.setActiveAccount(response.account);
    } else {
      const accounts = msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        msalInstance.setActiveAccount(accounts[0]);
      }
    }
  } catch (error) {
    console.error('Error during MSAL initialization', error);
  }
}

// Call the initialization function early in your app lifecycle.
initializeMSAL().catch(console.error);

export { msalInstance, loginRequest };
