import { Grid, MenuItem, TextField } from '@mui/material';

import { EthosWorkflowsApiCareLocationDto } from '@client/workflows';
import { DatePicker } from '@mui/x-date-pickers-pro';
import dayjs from 'dayjs';

type FilterValuesTypes = {
	location: string | null;
	startDate: dayjs.Dayjs | null;
	endDate: dayjs.Dayjs | null;
};
interface Filters {
	careLocationDetails: EthosWorkflowsApiCareLocationDto;
	onFieldChange: (fieldName: string, fieldValue: dayjs.Dayjs) => void;
	filterValues: FilterValuesTypes;
}

const Filters = ({
	careLocationDetails,
	onFieldChange: onFieldChangeProp,
	filterValues,
}: Filters) => {
	const onFieldChange = (fieldName: string, fieldValue: dayjs.Dayjs) => {
		onFieldChangeProp(fieldName, fieldValue);
	};

	return (
		<Grid
			container
			spacing={2}>
			<Grid
				item
				xs={12}>
				<TextField
					select={true}
					fullWidth
					variant="outlined"
					label={'Location'}
					value={filterValues.location || ''}
					disabled>
					{[{ label: careLocationDetails?.name, value: careLocationDetails?.id }].map((option) => (
						<MenuItem
							key={option.value?.toString()}
							value={option.value?.toString()}>
							{option.label}
						</MenuItem>
					))}
				</TextField>
			</Grid>
			<Grid
				item
				xs={6}>
				<DatePicker
					label={'Start Date'}
					slotProps={{
						textField: {
							fullWidth: true,
						},
					}}
					onAccept={(e) => onFieldChange('startDate', dayjs(e))}
					value={filterValues.startDate}
				/>
			</Grid>
			<Grid
				item
				xs={6}>
				<DatePicker
					label={'End Date'}
					slotProps={{
						textField: {
							fullWidth: true,
						},
					}}
					onChange={(e) => onFieldChange('endDate', dayjs(e))}
					value={filterValues.endDate}
				/>
			</Grid>
		</Grid>
	);
};

export default Filters;
