import { ReactNode } from 'react';
import { useAuth } from '@contexts/auth-context';

interface AuthorizedProps {
	roles?: string[];
	scopes?: string[];
	requireAll?: boolean;
	fallback?: ReactNode;
	children: ReactNode;
}

export const Authorized = ({
	roles = [],
	scopes = [],
	requireAll = false,
	fallback = null,
	children,
}: AuthorizedProps) => {
	const { hasRole, hasScope, hasAnyRole, hasAnyScope } = useAuth();

	// No restrictions case
	if (roles.length === 0 && scopes.length === 0) {
		return <>{children}</>;
	}

	// Check authorization based on requireAll flag
	let isAuthorized = true;

	if (requireAll) {
		// User must have ALL specified roles AND scopes
		isAuthorized = roles.every((role) => hasRole(role)) && scopes.every((scope) => hasScope(scope));
	} else {
		// User must have ANY of the specified roles OR scopes
		isAuthorized =
			(roles.length === 0 || hasAnyRole(roles)) && (scopes.length === 0 || hasAnyScope(scopes));
	}

	return isAuthorized ? <>{children}</> : <>{fallback}</>;
};
