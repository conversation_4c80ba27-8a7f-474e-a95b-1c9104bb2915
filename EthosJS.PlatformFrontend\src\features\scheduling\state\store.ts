import { Store } from "@tanstack/store";

const OPTIONS: Array<OptionTypes> = [
   { label: 'Confirmed - Patient will attend', value: '60036a49-fb2b-4bac-8994-ef19fe122a75', key: 'confirmed' },
   { label: 'Rescheduled - New date/time (Patient requested)', value: '60036a49-fb2b-4bac-8994-ef19fe122a72', key: 're-scheduled' },
   { label: 'Cancelled — Will not attend', value: '60036a49-fb2b-4bac-8994-ef19fe122a71', key: 'cancelled' },
   { label: 'Unable to Reach Patient', value: '60036a49-fb2b-4bac-8994-ef19fe122a70', key: 'unable-to-reach' },
];

type StatusType = 'confirmed' | 're-scheduled' | 'cancelled' | 'unable-to-reach' | 'left-voice-message' | 'none';

type OptionTypes = { label: string, value: string, key?: string }

type FollowUpState = {
   isStarted: boolean
   selectedConfirmationStatus: OptionTypes | null
   selectedReason: OptionTypes | null
   appointmentStatusDetails: {
      id: string | null;
      status: StatusType
   },
   statusOptions: Array<OptionTypes>
   isConfirmed: boolean
};

const defaultState: FollowUpState = {
   isStarted: false,
   selectedConfirmationStatus: null,
   selectedReason: null,
   appointmentStatusDetails: {
      status: 'none',
      id: null
   },
   statusOptions: OPTIONS,
   isConfirmed: false
}

const store = new Store<FollowUpState>(defaultState);

const getStatusDetailsById = (id: string) => {
   return OPTIONS.find((i) => i?.value === id)
}

const actions = {
   onStartFollowUp: (isStarted: boolean) => {
      store.setState((prevState) => ({ ...prevState, isStarted }));
   },
   onSelectConfirmationStatus: (selectedConfirmationStatus: FollowUpState['selectedConfirmationStatus']) => {
      if (selectedConfirmationStatus?.value) {
         const statusDetails = getStatusDetailsById(selectedConfirmationStatus?.value);
         store.setState((prevState) => ({
            ...prevState,
            selectedConfirmationStatus,
            appointmentStatusDetails: { id: statusDetails?.value as string, status: statusDetails?.key as StatusType }
         }));
      }
   },
   onSelectReason: (selectedReason: FollowUpState['selectedReason']) => {
      store.setState((prevState) => ({ ...prevState, selectedReason }));
   },
   onConfirm: (isConfirmed: FollowUpState['isConfirmed']) => {
      store.setState((prevState) => ({ ...prevState, isConfirmed }));
   },
}

const useFollowUpStore = () => ({ store, actions });

export default useFollowUpStore;