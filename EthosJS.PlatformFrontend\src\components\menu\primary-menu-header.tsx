import { Stack } from '@mui/material';
import MenuHeader, { MenuHeaderProps } from './menu-header';

export interface PrimaryMenuHeaderProps extends MenuHeaderProps {
	action?: React.ReactNode;
}

export default function PrimaryMenuHeader({
	title,
	subtitle,
	icon: Icon,
	action,
}: PrimaryMenuHeaderProps) {
	return (
		<Stack
			direction={'row'}
			justifyContent={'space-between'}>
			<MenuHeader {...{ title, subtitle, icon: Icon }} />
			{action}
		</Stack>
	);
}
