import { EthosWorkflowsApiPersonalPhoneNumberDto } from '@client/workflows';
import { formOptions } from '@tanstack/react-form';
import { z } from 'zod';

const phoneNumberSchema = z.any().transform((raw: EthosWorkflowsApiPersonalPhoneNumberDto) => {
	return {
		...raw,
	};
});

const defaultValues: EthosWorkflowsApiPersonalPhoneNumberDto = {
	type: null!,
	value: '',
	preferredTime: null!,
	isPreferred: false,
	allowsVoice: false,
	allowsSms: false,
};

function phoneNumberFormOptions(savedData?: EthosWorkflowsApiPersonalPhoneNumberDto) {
	return formOptions({
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	});
}

export { phoneNumberFormOptions, defaultValues, phoneNumberSchema };
