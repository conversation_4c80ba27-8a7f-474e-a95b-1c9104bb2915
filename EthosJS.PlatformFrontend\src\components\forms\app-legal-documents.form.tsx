import { useAppForm } from "@hooks/app-form";
import { DescriptionOutlined, UploadFile } from "@mui/icons-material";
import { Grid2 as Grid, TextField, MenuItem, Box } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { GenericFormProps } from "./predefined-form-props";
import FileUpload from "@components/file-upload";
import MainCardContainer from "@components/main-container/main-card-container";
import { z } from "zod";

type LegalDocumentFormData = {
    documentType: 'passport' | 'driverLicense' | 'idCard' | 'other';
    issueDate: string;
    expiryDate: string;
    fileName?: string;
    fileId?: string;
    uploadToken?: string;
    uploadUrl?: string;
};

// Add the Zod schema for legal document validation
export const legalDocumentSchema = z.object({
    documentType: z.enum(['passport', 'driverLicense', 'idCard', 'other'], {
        required_error: "Document type is required"
    }),
    issueDate: z.string().min(1, "Issue date is required"),
    expiryDate: z.string().min(1, "Expiry date is required"),
    fileName: z.string().optional(),
    fileId: z.string().optional(),
    uploadToken: z.string().optional(),
    uploadUrl: z.string().optional()
});

export const defaultValues: LegalDocumentFormData = {
    documentType: 'passport',
    issueDate: '',
    expiryDate: '',
    fileName: '',
    fileId: '',
    uploadToken: '',
    uploadUrl: '',
};

export type LegalDocumentData = typeof defaultValues;

type LegalDocumentFormProps = GenericFormProps<LegalDocumentData> & { workflowId: string };

export default function LegalDocumentForm({ onAdd, onCancel, onDelete, formValues, workflowId }: LegalDocumentFormProps) {
    const hasValues = JSON.stringify(formValues) !== JSON.stringify(defaultValues);

    const form = useAppForm({
        defaultValues: {
            ...defaultValues,
            ...formValues
        },
        defaultState: {
            isDirty: hasValues,
            isPristine: !hasValues,
        }
    });

    const { values, isDirty } = useStore(form.store, ({ values, isDirty }) => ({ values, isDirty }));

    return (
        <MainCardContainer
            emphasis="high"
            color="primary"
            title="Add Legal Document"
            icon={<DescriptionOutlined fontSize="large" />}
            descriptionSubheader="* Indicates a required field"
            descriptionText="Please provide the document details and upload a copy of the document."
            footerProps={{
                primaryButton1: {
                    label: hasValues ? "Update" : "Add",
                    onClick: () => onAdd?.(values),
                    disabled: !isDirty,
                    "data-testid": "legalDocument.submitButton"
                },
                secondaryButton1: {
                    label: "Cancel",
                    onClick: () => {
                        form.reset();
                        onCancel?.();
                    },
                    "data-testid": "legalDocument.cancelButton"
                },
                secondaryButton2: hasValues ? {
                    label: "Delete",
                    onClick: onDelete,
                    "data-testid": "legalDocument.deleteButton"
                } : undefined
            }}
        >
            <Grid container columnSpacing={2.5} rowSpacing={2}>
                <Grid size={4}>
                    <form.AppField name="documentType">
                        {({ state, handleChange }) => (
                            <TextField
                                fullWidth
                                select
                                label="Document Type"
                                value={state.value}
                                onChange={(e) => handleChange(e.target.value as LegalDocumentFormData['documentType'])}
                                required
                                data-testid="legalDocument.documentType"
                            >
                                <MenuItem value="passport">Passport</MenuItem>
                                <MenuItem value="driverLicense">Driver's License</MenuItem>
                                <MenuItem value="idCard">ID Card</MenuItem>
                                <MenuItem value="other">Other</MenuItem>
                            </TextField>
                        )}
                    </form.AppField>
                </Grid>
                <Grid size={4}>
                    <form.AppField
                        name="issueDate"
                        children={(field) => (
                            <field.AppDateField
                                label="Issue Date"
                                required
                                data-testid="legalDocument.issueDate"
                            />
                        )}
                    />
                </Grid>
                <Grid size={4}>
                    <form.AppField
                        name="expiryDate"
                        children={(field) => (
                            <field.AppDateField
                                label="Expiry Date"
                                required
                                data-testid="legalDocument.expiryDate"
                            />
                        )}
                    />
                </Grid>
                <Grid size={12}>
                    {
                        values.fileId && values.fileName ?
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }} data-testid="legalDocument.fileDisplay">
                                <UploadFile fontSize="large" color="primary" />
                                <Box sx={{ fontWeight: 'bold' }}>{values.fileName}</Box>
                            </Box>
                            : <FileUpload
                                workflowId={workflowId}
                                onFileUpload={(fileId, fileName) => {
                                    form.setFieldValue('fileId', fileId);
                                    form.setFieldValue('fileName', fileName);
                                }}
                                data-testid="legalDocument.fileUpload"
                            />
                    }
                </Grid>
            </Grid>
        </MainCardContainer>
    );
}

export { defaultValues as legalDocumentFormDefaultValues };
