import React, { useState } from 'react';
import { Box, Typography, Stack } from '@mui/material';
import MainCardContainer from '@components/main-container/main-card-container';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-form';
import { SingleNoteItemProps } from './types';

interface EditFormData {
  content: string;
}

export const SingleNote: React.FC<SingleNoteItemProps> = ({
  note,
  allowEdit = true,
  onEdit,
  onDelete,
  size = 'medium',
  footerProps,
  icon
}) => {
  const [isEditing, setIsEditing] = useState(false);

  const editForm = useAppForm({
    defaultValues: {
      content: note.content
    } as EditFormData
  });

  const { values: editValues } = useStore(editForm.store, ({ values }) => ({ values }));

  const handlePrimaryAction = () => {
    if (!isEditing && allowEdit) {
      setIsEditing(true);
      editForm.setFieldValue('content', note.content);
    } else {
      setIsEditing(false);
    }
  };

  const handleSave = () => {
    if (editValues.content?.trim()) {
      onEdit?.({
        header: note.header,
        subHeader: note.subHeader,
        descriptionSubheader: note.descriptionSubheader,
        descriptionText: note.descriptionText,
        content: editValues.content.trim()
      });
      setIsEditing(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    editForm.setFieldValue('content', note.content);
  };

  const handleDeleteClick = () => {
    onDelete?.();
  };

  return (
    <>
      <MainCardContainer
        title={note.header}
        icon={icon}
        emphasis={ isEditing ? 'high' :  "low"}
        color="primary"
        headerSize={size}
        primaryActionType={allowEdit && !isEditing ? 'Edit' : 'Delete'}
        onPrimaryAction={handlePrimaryAction}
        descriptionSubheader={note.subHeader}
        descriptionText={note.descriptionSubheader}
        footerProps={
          isEditing
            ? {
                primaryButton1: {
                  label: 'Save',
                  onClick: handleSave,
                  disabled: !editValues.content?.trim()
                },
                primaryButton2: {
                  label: 'Cancel',
                  onClick: handleCancel
                },
                ...(onDelete && {
                  secondaryButton1: {
                    label: 'Delete',
                    onClick: handleDeleteClick
                  }
                })
              }
            : {
                ...footerProps
              }
        }>
        {isEditing ? (
          <Box sx={{ p: '16px' }}>
            <editForm.AppField
              name="content"
              children={(field) => (
                <field.AppTextField
                  label=""
                  multiline
                  placeholder="Type here..."
                  size="medium"
                  minRows={4}
                  required
                />
              )}
            />
          </Box>
        ) : (
          <Box sx={{ p: '16px' }}>
            <Typography
              variant="body1"
              sx={{
                mb: 1,
                whiteSpace: 'pre-wrap',
                lineHeight: 1.5
              }}>
              {note.content}
            </Typography>
          </Box>
        )}
      </MainCardContainer>
    </>
  );
};
