import { getWorkflowStateQuery } from "@api/workflows/query-options";
import { BadgeOutlined } from "@mui/icons-material";
import { Box, CardContent, List, Fade, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { useEffect } from "react";
import StepCard from "../step-card";
import { Flow } from "src/workflows/Flow";
import StepLeftMenu from "./step-left-menu";
import StepComponent from "./step-component";

export default function FlowComponent({ workflowId, flow, startNextFlow }: { workflowId: string, flow: Flow, startNextFlow: () => void }) {
    const { currentStep, selectedStep } = useStore(flow.getStore(), (state) => state);

    const displayStep = selectedStep ?? currentStep;

    const { data, isFetching } = useQuery(getWorkflowStateQuery(workflowId, flow.name));
    useEffect(() => {
        if (data) {
            flow.calculateFlowStatus(data);
        }
    }, [data])

    return (
        <Box sx={{ flex: 1, minHeight: 0 }}>
            <StepCard
                title={flow.name} icon={BadgeOutlined} showHeader={false} isLoading={isFetching}>
                <CardContent sx={{ overflow: 'auto', minHeight: 0, flex: 1, display: 'flex', gap: 2, height: '100%' }}>
                    <List sx={{ width: 220 }} disablePadding>
                        {
                            Array.from(flow.steps ?? []).map(([_, step], index) => (
                                <StepLeftMenu
                                    key={index}
                                    stepNumber={index + 1}
                                    step={step}
                                    onClick={() => {
                                        flow.setSelectedStep(step);
                                    }}
                                />
                            ))
                        }
                    </List>
                    <Fade in key={displayStep?.name ?? ''} timeout={300} unmountOnExit>
                        <Box sx={{ flex: 1, overflow: 'auto', height: "calc(100% - 35px)", pb: 0, mt: -2 }}>
                            {
                                displayStep ?
                                    <StepComponent step={displayStep} workflowId={workflowId} workflowName={flow.name} startNextFlow={startNextFlow} />
                                    : <Typography variant="body1" component="div" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>No step selected</Typography>
                            }
                        </Box>
                    </Fade>
                </CardContent>
            </StepCard>
        </Box>)
}