import { Shield, ShieldOutlined } from '@mui/icons-material';
import { Box, Chip, lighten, styled, Typography } from '@mui/material';
import { forwardRef } from 'react';

function getInsuranceSlot(slot: number) {
    switch (slot) {
        case 1:
            return 'Primary';
        case 2:
            return 'Secondary';
        default:
            return 'Tertiary';
    }
}

const Container = styled(Box)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'stretch',
    padding: 6,
    borderRadius: theme.shape.borderRadius - 2,
    border: `1px solid ${lighten(theme.palette.primary.light, 0.8)}`,
    backgroundColor: lighten(theme.palette.primary.light, 0.9),
    color: theme.palette.primary.main,
}));

const ImageContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: 64,
    height: 64,
    borderRadius: theme.shape.borderRadius - 6,
    backgroundColor: theme.palette.background.paper,
    border: `1px solid ${lighten(theme.palette.primary.light, 0.8)}`,
}));

const BodyContainer = styled(Box)(({ theme }) => ({
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'center',
    padding: theme.spacing(1),
}));

interface InsuranceCardProps {
    slot: number;
    imageUrl: string;
    title: string;
    description: string;
}

const InsuranceCard = forwardRef<HTMLDivElement, InsuranceCardProps>(
    (props, ref) => {

        const { imageUrl, title, description, slot } = props;

        return (
            <Container ref={ref}>
                <ImageContainer>
                    {
                        imageUrl ? (<img src={imageUrl}
                            alt="Insurance"
                            style={{ width: '100%', height: '100%', objectFit: 'contain' }}
                        />) : <ShieldOutlined sx={{ fontSize: 32 }} />
                    }
                </ImageContainer>
                <BodyContainer>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {title}
                        <Chip label={getInsuranceSlot(slot).toLocaleUpperCase()}
                            size="small"
                            variant="outlined"
                            color="primary"
                            sx={{
                                borderRadius: 1,
                            }}
                        />
                    </Typography>
                    <Typography variant="body2">
                        {description}
                    </Typography>
                </BodyContainer>
            </Container>
        );
    }
);

InsuranceCard.displayName = 'InsuranceCard';

export default InsuranceCard;
