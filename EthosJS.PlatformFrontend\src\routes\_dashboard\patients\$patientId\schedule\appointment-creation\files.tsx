import { createFileRoute } from '@tanstack/react-router';
import Dashboard from '@features/scheduling/components/dashboard';

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/schedule/appointment-creation/files'
)({
	component: RouteComponent,
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { studyId } = Route.useSearch();
	return (
		<Dashboard
			studyId={studyId!}
			patientId={patientId!}
		/>
	);
}
