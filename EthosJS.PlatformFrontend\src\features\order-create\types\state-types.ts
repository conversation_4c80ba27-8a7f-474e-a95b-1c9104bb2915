interface StepProps {
	patientId: string;
	orderId: string;
	successCallback: () => void;
}

type StateTypes = 'Complete' | 'NotStarted' | 'InProgress' | 'Error' | 'Warning';

type StepNames =
	| 'AddStudy'
	| 'AddAssociatedInsurances'
	| 'AddAssociatedInsurances'
	| 'AddPhysicians'
	| 'ReviewAndSubmitOrder';

type OrderState = {
	flowState: {
		status: StateTypes;
		progress: number;
		lastUpdate: string;
	};
	stepState: Record<StepNames, StateTypes>;
};

interface ConfirmationStepProps extends StepProps {
	studyId: string;
}

export type { StepProps, ConfirmationStepProps, OrderState, StepNames, StateTypes };
