import { SvgIconProps } from "@mui/material";

export default function EthosLogoSvg(props: SvgIconProps) {

    return (
        <svg width="54" height="53" viewBox="0 0 54 53" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
            <g filter="url(#filter0_dd_315_17464)">
                <g clip-path="url(#clip0_315_17464)">
                    <path d="M5 20.6C5 14.4394 5 11.3591 6.19893 9.0061C7.25354 6.93632 8.93632 5.25354 11.0061 4.19893C13.3591 3 16.4394 3 22.6 3H31.4C37.5606 3 40.6409 3 42.9939 4.19893C45.0637 5.25354 46.7465 6.93632 47.8011 9.0061C49 11.3591 49 14.4394 49 20.6V29.4C49 35.5606 49 38.6409 47.8011 40.9939C46.7465 43.0637 45.0637 44.7465 42.9939 45.8011C40.6409 47 37.5606 47 31.4 47H22.6C16.4394 47 13.3591 47 11.0061 45.8011C8.93632 44.7465 7.25354 43.0637 6.19893 40.9939C5 38.6409 5 35.5606 5 29.4V20.6Z" fill="url(#paint0_linear_315_17464)" />
                    <path d="M26.9437 3H27.0505V47H26.9437V3Z" fill="#3E1C96" fill-opacity="0.5" />
                    <path d="M48.9984 24.9453L48.9984 25.0521L4.99841 25.0521L4.99841 24.9453L48.9984 24.9453Z" fill="#3E1C96" fill-opacity="0.5" />
                    <path d="M41.5767 3H41.6835V47H41.5767V3Z" fill="#3E1C96" fill-opacity="0.5" />
                    <path d="M19.6293 3H19.7361V47H19.6293V3Z" fill="#3E1C96" fill-opacity="0.5" />
                    <path d="M34.2601 3H34.3669V47H34.2601V3Z" fill="#3E1C96" fill-opacity="0.5" />
                    <path d="M12.3149 3H12.4217V47H12.3149V3Z" fill="#3E1C96" fill-opacity="0.5" />
                    <path d="M48.9984 39.5781L48.9984 39.6849L4.99841 39.6849L4.99841 39.5781L48.9984 39.5781Z" fill="#3E1C96" fill-opacity="0.5" />
                    <path d="M48.9984 17.6309L48.9984 17.7377L4.99841 17.7377L4.99841 17.6309L48.9984 17.6309Z" fill="#3E1C96" fill-opacity="0.5" />
                    <path d="M48.9984 32.2617L48.9984 32.3685L4.99841 32.3685L4.99841 32.2617L48.9984 32.2617Z" fill="#3E1C96" fill-opacity="0.5" />
                    <path d="M48.9984 10.3164L48.9984 10.4232L4.99841 10.4232L4.99841 10.3164L48.9984 10.3164Z" fill="#3E1C96" fill-opacity="0.5" />
                    <path d="M26.9443 18.0093H27.164C30.7018 18.0093 33.6138 20.687 34.0138 24.1358H42.42C42.6853 24.1358 42.9402 23.992 43.0728 23.7399C43.1184 23.6524 43.1454 23.5586 43.1557 23.4627C43.3506 22.1916 43.3879 20.9871 43.2946 19.8639C43.0708 17.1924 42.1029 14.9398 40.6977 13.2727C39.2718 11.5827 37.3879 10.4929 35.3609 10.1761C34.7267 10.0782 34.0821 10.0511 33.4417 10.1073C33.3796 10.1115 33.3174 10.1178 33.2573 10.1261C32.6998 9.40717 32.0593 8.81327 31.3692 8.34441C30.022 7.4296 28.4821 6.97949 26.9443 7.00033C25.4044 7.02116 23.8728 7.51087 22.5588 8.47777C21.9516 8.92372 21.3899 9.47177 20.8966 10.1178C20.8469 10.1136 20.8013 10.1073 20.7536 10.1053C20.1132 10.0511 19.4686 10.0761 18.8344 10.174C16.8054 10.4908 14.9236 11.5827 13.4977 13.2706C12.0883 14.9398 11.1225 17.1924 10.8987 19.8639C10.8033 20.9809 10.8427 22.177 11.0355 23.4419C11.0603 23.8295 11.3795 24.1337 11.7733 24.1337H20.0946C20.4946 20.685 23.4065 18.0072 26.9443 18.0072V18.0093ZM35.1288 11.639C36.8034 11.9015 38.3682 12.8143 39.5661 14.2334C40.7848 15.6754 41.6221 17.6405 41.8189 19.989C41.877 20.6933 41.8811 21.431 41.8189 22.1937C38.2915 19.9452 36.6936 16.636 35.3485 13.852C34.9547 13.0372 34.5837 12.2662 34.1878 11.564C34.507 11.566 34.82 11.5931 35.1288 11.6411V11.639ZM32.5837 11.7452V11.7473C33.077 12.5329 33.5329 13.479 34.0241 14.4938C35.2739 17.0799 36.7246 20.0869 39.6407 22.4458C35.1744 20.7996 32.0821 17.9072 29.3671 15.367C28.9733 14.9981 28.5878 14.6376 28.2065 14.2875C29.6428 13.0727 31.0479 12.1287 32.5837 11.7473C32.5837 11.7473 32.5837 11.7473 32.5837 11.7452ZM22.5298 10.4678C22.8117 10.1678 23.1101 9.90521 23.4272 9.67182C24.4904 8.89246 25.7215 8.49444 26.9609 8.47777C28.2003 8.46319 29.4479 8.82578 30.5381 9.56971C30.9174 9.82811 31.278 10.1323 31.6117 10.4824C29.9951 11.018 28.5547 12.037 27.0915 13.2935C25.62 12.0287 24.163 10.9992 22.5298 10.4699V10.4678ZM12.364 19.989C12.5609 17.6405 13.4002 15.6754 14.6168 14.2334C15.8148 12.8143 17.3795 11.9036 19.0541 11.639C19.3547 11.5931 19.6614 11.566 19.9743 11.5619C19.6013 12.2391 19.2448 12.9851 18.8676 13.7728C17.5267 16.5777 15.9246 19.9244 12.364 22.1916C12.3018 21.4289 12.3039 20.6933 12.364 19.9869V19.989ZM20.192 14.4105C20.6604 13.4311 21.0997 12.5163 21.5744 11.7411C23.1205 12.1183 24.5319 13.0664 25.9764 14.2855C25.593 14.6376 25.2075 14.9961 24.8158 15.3649C22.1028 17.9051 19.0085 20.7996 14.5422 22.4437C17.4873 20.0619 18.9464 17.0153 20.192 14.4084V14.4105Z" fill="white" />
                    <path d="M43.1578 26.5352C43.1474 26.4394 43.1226 26.3456 43.0749 26.2581C42.9423 26.008 42.6873 25.8622 42.4221 25.8622L34.0262 25.8601C33.6884 29.3734 30.7433 32.12 27.1619 32.12H26.9422C23.9018 32.12 21.3236 30.1424 20.4054 27.3959C20.9567 27.3688 21.5308 27.3146 22.1339 27.225C22.2749 27.2021 22.4158 27.1771 22.5567 27.1521C22.6334 27.4917 22.7495 27.8106 22.9111 28.1086C23.106 28.467 23.365 28.7816 23.6883 29.0567C24.0096 29.3443 24.4075 29.5714 24.8863 29.7402C25.4148 29.9277 25.9702 30.0444 26.5485 30.0903C27.1412 30.1403 27.7526 30.0903 28.3868 29.9382C29.0376 29.7777 29.7174 29.4756 30.4241 29.0317C30.6459 28.8942 30.7723 28.742 30.8013 28.5795C30.8428 28.4211 30.8241 28.2461 30.7433 28.0606C30.6645 27.8731 30.5298 27.7168 30.3412 27.5918C30.1526 27.4667 29.9495 27.4667 29.7319 27.5918C29.3049 27.8168 28.8593 28.0127 28.393 28.1815C27.9453 28.3419 27.4956 28.444 27.0459 28.4857C26.6127 28.5212 26.1961 28.467 25.7961 28.3253C25.421 28.1919 25.1163 28.0044 24.8821 27.7626C24.6604 27.5251 24.5029 27.2521 24.4075 26.9437C24.3889 26.8791 24.3723 26.8145 24.3578 26.7499C24.5712 26.6915 24.7847 26.6311 24.9961 26.5665C25.6801 26.3644 26.3329 26.1289 26.9567 25.8601C27.1889 25.7601 27.4168 25.6559 27.6386 25.5475C28.3516 25.1891 28.9671 24.7973 29.4977 24.3743C29.5847 24.3055 29.6738 24.2388 29.7547 24.168C30.3536 23.6575 30.7557 23.1115 30.9588 22.5322C31.108 22.1071 31.1371 21.7132 31.0479 21.3485C30.9754 20.9755 30.8055 20.6546 30.536 20.3858C30.2728 20.1045 29.9454 19.894 29.5578 19.7586C28.8863 19.521 28.2521 19.4481 27.6531 19.5398C27.0542 19.6315 26.4966 19.8398 25.9764 20.1628C25.4562 20.4858 24.9837 20.8838 24.5588 21.3548C24.1505 21.8174 23.7961 22.3092 23.4997 22.826C23.2034 23.3428 22.9712 23.8408 22.8034 24.3159C22.7972 24.3368 22.7909 24.3555 22.7847 24.3764C22.6438 24.7952 22.5443 25.2141 22.4946 25.6392C22.4946 25.6517 22.4946 25.6621 22.4925 25.6746C22.3806 25.6955 22.2687 25.7184 22.1567 25.7371C21.5225 25.8497 20.7267 25.8538 20.08 25.8601C20.08 25.8601 20.08 25.8622 20.08 25.8642H11.7733C11.3795 25.8642 11.0624 26.1685 11.0355 26.5561C10.8427 27.821 10.8033 29.0171 10.8987 30.1341C11.1246 32.8055 12.0904 35.0582 13.4997 36.7273C14.9256 38.4173 16.8075 39.5072 18.8365 39.8239C19.4707 39.9219 20.1153 39.9469 20.7557 39.8927C20.8054 39.8906 20.851 39.8844 20.8987 39.8802C21.392 40.5262 21.9557 41.0743 22.5609 41.5202C23.8769 42.4871 25.4086 42.9768 26.9464 42.9976C28.4821 43.0185 30.0241 42.5684 31.3692 41.6577C32.0593 41.1868 32.6998 40.5929 33.2573 39.876C33.3195 39.8844 33.3816 39.8885 33.4417 39.8948C34.0821 39.949 34.7267 39.924 35.3609 39.826C37.3879 39.5072 39.2718 38.4173 40.6977 36.7294C42.1029 35.0624 43.0728 32.8097 43.2946 30.1382C43.3879 29.015 43.3506 27.8106 43.1557 26.5394L43.1578 26.5352ZM24.5215 24.8786C24.5775 24.716 24.6479 24.5473 24.7267 24.3743C24.851 24.0971 25.0003 23.8075 25.1785 23.5032C25.4666 23.0115 25.7982 22.5634 26.1713 22.1612C26.5567 21.7632 26.9588 21.4632 27.3775 21.2631C27.8003 21.0506 28.2106 21.0151 28.6127 21.1568C28.8718 21.2485 29.0355 21.4006 29.106 21.6132C29.1764 21.8257 29.1599 22.08 29.0562 22.378C28.9257 22.751 28.6417 23.1073 28.2044 23.4449C27.7858 23.7825 27.2697 24.0909 26.6604 24.3743C26.6521 24.3785 26.6438 24.3826 26.6355 24.3868C26.0137 24.6723 25.336 24.9265 24.6065 25.1453C24.5484 25.1641 24.4883 25.1807 24.4282 25.1974C24.4573 25.0911 24.4883 24.9849 24.5257 24.8765L24.5215 24.8786ZM19.0583 38.359C17.3837 38.0964 15.8189 37.1837 14.621 35.7646C13.4023 34.3226 12.565 32.3575 12.3681 30.009C12.308 29.3026 12.3059 28.567 12.3681 27.8043C15.9287 30.0736 17.5308 33.4182 18.8718 36.2231C19.249 37.0107 19.6075 37.7568 19.9785 38.434C19.6655 38.4299 19.3588 38.4028 19.0583 38.3569V38.359ZM21.5806 38.2548C21.106 37.4796 20.6666 36.5648 20.1982 35.5854C18.9526 32.9785 17.4935 29.9319 14.5484 27.5501C19.0148 29.1942 22.107 32.0887 24.822 34.6289C25.2158 34.9978 25.6013 35.3583 25.9826 35.7083C24.5381 36.9274 23.1288 37.8755 21.5806 38.2527V38.2548ZM30.5422 40.4241C29.4521 41.168 28.2044 41.5327 26.965 41.516C25.7257 41.5014 24.4966 41.1034 23.4313 40.322C23.1163 40.0886 22.8179 39.826 22.5339 39.526C24.165 38.9946 25.622 37.9652 27.0956 36.7003C28.5567 37.9568 29.9992 38.9758 31.6158 39.5114C31.2821 39.8594 30.9215 40.1657 30.5422 40.4241ZM32.5878 38.2506C32.5878 38.2506 32.5878 38.2506 32.5899 38.2506H32.5878C31.0521 37.8714 29.6469 36.9253 28.2127 35.7125C28.5961 35.3603 28.9816 35.0019 29.3733 34.6331C32.0863 32.0929 35.1806 29.2005 39.6469 27.5542C36.7309 29.9132 35.278 32.9202 34.0303 35.5062C33.5391 36.519 33.0832 37.4671 32.5899 38.2527L32.5878 38.2506ZM41.8252 30.009C41.6262 32.3575 40.7889 34.3226 39.5723 35.7646C38.3744 37.1837 36.8096 38.0944 35.135 38.359C34.8262 38.409 34.5132 38.434 34.1941 38.4361C34.5899 37.7338 34.963 36.9607 35.3547 36.148C36.6977 33.364 38.2977 30.0549 41.8252 27.8064C41.8873 28.5691 41.8832 29.3047 41.8252 30.0111V30.009Z" fill="white" />
                </g>
                <path d="M5.1375 20.6C5.1375 17.5174 5.13761 15.2138 5.28691 13.3864C5.43608 11.5606 5.7335 10.2224 6.32144 9.06853C7.36287 7.02462 9.02462 5.36287 11.0685 4.32144C12.2224 3.7335 13.5606 3.43608 15.3864 3.28691C17.2138 3.13761 19.5174 3.1375 22.6 3.1375H31.4C34.4826 3.1375 36.7862 3.13761 38.6136 3.28691C40.4394 3.43608 41.7776 3.7335 42.9315 4.32144C44.9754 5.36287 46.6371 7.02462 47.6786 9.06853C48.2665 10.2224 48.5639 11.5606 48.7131 13.3864C48.8624 15.2138 48.8625 17.5174 48.8625 20.6V29.4C48.8625 32.4826 48.8624 34.7862 48.7131 36.6136C48.5639 38.4394 48.2665 39.7776 47.6786 40.9315C46.6371 42.9754 44.9754 44.6371 42.9315 45.6786C41.7776 46.2665 40.4394 46.5639 38.6136 46.7131C36.7862 46.8624 34.4826 46.8625 31.4 46.8625H22.6C19.5174 46.8625 17.2138 46.8624 15.3864 46.7131C13.5606 46.5639 12.2224 46.2665 11.0685 45.6786C9.02462 44.6371 7.36287 42.9754 6.32144 40.9315C5.7335 39.7776 5.43608 38.4394 5.28691 36.6136C5.13761 34.7862 5.1375 32.4826 5.1375 29.4V20.6Z" stroke="#D0D5DD" stroke-width="0.275" />
            </g>
            <defs>
                <filter id="filter0_dd_315_17464" x="0.875" y="0.25" width="52.25" height="52.25" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feOffset dy="1.375" />
                    <feGaussianBlur stdDeviation="1.375" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.06 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_315_17464" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feOffset dy="1.375" />
                    <feGaussianBlur stdDeviation="2.0625" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.1 0" />
                    <feBlend mode="normal" in2="effect1_dropShadow_315_17464" result="effect2_dropShadow_315_17464" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_315_17464" result="shape" />
                </filter>
                <linearGradient id="paint0_linear_315_17464" x1="27" y1="3" x2="27" y2="47" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#9882D7" />
                    <stop offset="1" stop-color="#3E1C96" />
                </linearGradient>
                <clipPath id="clip0_315_17464">
                    <path d="M5 20.6C5 14.4394 5 11.3591 6.19893 9.0061C7.25354 6.93632 8.93632 5.25354 11.0061 4.19893C13.3591 3 16.4394 3 22.6 3H31.4C37.5606 3 40.6409 3 42.9939 4.19893C45.0637 5.25354 46.7465 6.93632 47.8011 9.0061C49 11.3591 49 14.4394 49 20.6V29.4C49 35.5606 49 38.6409 47.8011 40.9939C46.7465 43.0637 45.0637 44.7465 42.9939 45.8011C40.6409 47 37.5606 47 31.4 47H22.6C16.4394 47 13.3591 47 11.0061 45.8011C8.93632 44.7465 7.25354 43.0637 6.19893 40.9939C5 38.6409 5 35.5606 5 29.4V20.6Z" fill="white" />
                </clipPath>
            </defs>
        </svg>

    )
}