import { <PERSON><PERSON>ontent, Grid, <PERSON>uI<PERSON>, TextField } from "@mui/material";
import { LocalPhoneOutlined, Phone } from "@mui/icons-material";
import { useStore } from "@tanstack/react-store";

import AddInfoCard from "@components/add-info-card";
import ArrayFieldContainer from "@components/array-field-container";
import Card from "@components/card";
import Card<PERSON>eader from "@components/card-header";
import FormFooter from "@components/form-footer";
import StyledCardFooter from "@components/card-footer";
import { useAppForm, withForm } from "@hooks/app-form";

interface FormValues {
   docType: string | null
   issueDate: any
   expirationDate: any
}

const defaultValues = {
   docType: null,
   issueDate: null,
   expirationDate: null
}

enum FormNames {
   DocType = 'docType',
   IssueDate = 'issueDate',
   ExpirationDate = 'expirationDate',
}

const formatLegalDocSummary = (values: FormValues) => {

   const MAP: { [key: string]: { label: string } } = {
      [FormNames.DocType]: {
         label: 'Document Type'
      },
      [FormNames.IssueDate]: {
         label: 'Issue Date'
      },
      [FormNames.ExpirationDate]: {
         label: 'Expiry Date'
      }
   }

   return Object.entries(values)?.reduce((acc: Array<{ label: string, value: string }>, cur) => {

      const [key, value] = cur;

      if (MAP?.[key]?.label && value) {
         acc.push({
            label: MAP?.[key]?.label,
            value
         })
      }

      return acc;
   }, []) ?? []

}

const PhoneNumber = withForm<{ legalDocs: FormValues[] }>({
   defaultValues: {
      legalDocs: defaultValues
   },
   render: function Render({ form }) {
      return (
         <>
            <form.AppField
               name="legalDocs"
               mode="array"
            >
               {({ pushValue, removeValue, state, replaceValue }) => (
                  <AddInfoCard
                     title="Legal Documentation *"
                     icon={<Phone />}
                     onClick={() => pushValue(defaultValues)}
                     showHeader={state.value.length === 0}
                  >
                     {state.value.map((item, i) => {

                        return (
                           <ArrayFieldContainer
                              key={i}
                              initialEditState={true}
                              items={formatLegalDocSummary(item)}
                              title="Legal Documentation *"
                           >
                              {({ setEdit }) => (
                                 <LegalForm
                                    formValues={state.value[i]}
                                    onAdd={(data) => {
                                       replaceValue(i, data);
                                       setEdit(false);
                                    }}
                                    onCancel={() => {
                                       removeValue(i);
                                       setEdit(false);
                                    }}
                                 />
                              )}
                           </ArrayFieldContainer>
                        )
                     })}
                  </AddInfoCard>
               )}
            </form.AppField>
         </>
      );
   }
});

interface LegalFormFormProps {
   onAdd?: (values: FormValues) => void;
   onCancel?: () => void;
   onDelete?: () => void;
   formValues: FormValues;
}

const LegalForm = ({ onAdd, onCancel, onDelete, formValues }: LegalFormFormProps) => {

   const hasValues = JSON.stringify(formValues) !== JSON.stringify(defaultValues);
   const form = useAppForm({
      defaultValues: {
         ...defaultValues,
         ...formValues
      },
      defaultState: {
         isDirty: hasValues,
         isPristine: !hasValues,
      }
   });

   const values = useStore(form.store, (state) => state.values);

   return (
      <Card emphasis="dark">
         <CardHeader
            emphasis="dark"
            title="Legal Documentation "
            avatar={<LocalPhoneOutlined fontSize="large" />}
            slotProps={{
               title: { variant: 'h6' }
            }}
         />
         <CardContent>
            <Grid container spacing={3}>
               <Grid item xs={6}>
                  <form.AppField name={FormNames.DocType}>
                     {({ state, handleChange }) => (
                        <TextField
                           fullWidth
                           select
                           label="Type"
                           value={state.value}
                           onChange={(e) => handleChange(e.target.value as any)}
                        >
                           <MenuItem value="court_order">Court Order</MenuItem>
                           <MenuItem value="hpoa">Helathcare Power of Attorney</MenuItem>
                           <MenuItem value="other">Other</MenuItem>
                        </TextField>
                     )}
                  </form.AppField>
               </Grid>
               <Grid item xs={6}>
                  <form.AppField
                     name={FormNames.IssueDate}
                     children={(field) => (
                        <field.AppDateField label="Issue Date" required />
                     )}
                  />
               </Grid>
               <Grid item xs={6}>
                  <form.AppField
                     name={FormNames.ExpirationDate}
                     children={(field) => (
                        <field.AppDateField label="Expiry Date" required />
                     )}
                  />
               </Grid>
            </Grid>
         </CardContent>
         <StyledCardFooter sx={{ justifyContent: 'flex-end' }}>
            <form.Subscribe selector={({ isDirty }) => ({ isDirty })}>
               {({ isDirty }) => (
                  <FormFooter
                     onCancel={() => {
                        form.reset();
                        onCancel?.();
                     }}

                     onSubmit={() => onAdd?.(values)}
                     isUpdate={hasValues}
                     isDirty={isDirty}
                     onDelete={onDelete}
                  />
               )}
            </form.Subscribe>
         </StyledCardFooter>
      </Card>
   )
}

export default PhoneNumber;
