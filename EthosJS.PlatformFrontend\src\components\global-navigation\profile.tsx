import {
  Box,
  BoxProps,
  lighten,
  List,
  Popover,
  PopoverProps,
  Typography,
  SxProps,
  useTheme,
  Button
} from '@mui/material';
import { ChevronDown } from 'lucide-react';
import { useId, useState } from 'react';
import { getPopoverTriggerIconIndicatorStyle } from './styles';
import MenuItem, { IMenuItem } from './menu-item';

interface IProfile {
  title?: string;
  popoverProps?: Partial<PopoverProps> & {
    items?: Array<IMenuItem>;
    onMenuItemSelect?: (item: IMenuItem, e: React.MouseEvent<HTMLLIElement, MouseEvent>) => void;
  };
}

const getTriggerStyle = (selected: boolean): SxProps => {
  const { palette }: any = useTheme();
  const borderColor = lighten(palette.primary.light, 0.9);
  const hoverColor = lighten(palette.primary.light, 0.8);

  return {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 121,
    minHeight: 44,
    borderRadius: 100,
    border: `2px solid ${borderColor}`,
   backgroundColor: selected ? palette.primary.light : 'transparent',
    '.menu-label': {
      color: selected ? palette.common.white : 'inherit'
    },
    '&:hover': {
      backgroundColor: hoverColor,
      '.menu-label': {
        color: palette.primary.main
      }
    },
   };
};

const defaultPopoverPlacement: PopoverProps['anchorOrigin'] = {
  vertical: 'bottom',
  horizontal: 'left'
};

const Profile: React.FC<IProfile> = (props) => {
  const { title } = props;

  const { items = [], onMenuItemSelect, ...popoverProps } = props.popoverProps ?? {};

  const rcPrefix = useId(),
    id = `${rcPrefix}-profile`;

  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [selected,setSelected] =  useState(false)

  const open = !!anchorEl;

  const menuItems = items ?? [];

  const onOpenPopover: BoxProps['onClick'] = (e) => {
   setSelected(true)
    setAnchorEl(e.currentTarget);
  };

  const onClosePopover = () => {
   setSelected(false)
    setAnchorEl(null);
  };

  const onSelectMenuItem: (
    item: IMenuItem,
    e: React.MouseEvent<HTMLLIElement, MouseEvent>
  ) => void = (item, e) => {
    onMenuItemSelect?.(item, e);
    onClosePopover();
  };

  return (
    <>
      <Box aria-describedby={id} sx={getTriggerStyle(selected)} onClick={onOpenPopover}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            cursor: 'pointer',
           
          }}>
          <Typography
            className="menu-label"
            variant="asapCondensed"
            fontSize={16}
            sx={{ userSelect: 'none', cursor: 'pointer' }}>
            {title}
          </Typography>
          <ChevronDown
            className="menu-label"
            size={16}
            style={getPopoverTriggerIconIndicatorStyle(open)}
          />
        </Box>
      </Box>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={onClosePopover}
        anchorOrigin={defaultPopoverPlacement}
        {...popoverProps}
        PaperProps={{
          sx: (theme) => {
            return {
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'flex-start',
              p: 1.5,
              marginTop:1,
              gap: '0.5rem',
              borderRadius: '1rem',
              border: '1px solid',
              borderColor: lighten(theme.palette.primary.light, 0.7),
              boxShadow:
                '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)'
            };
          }
        }}
        component={List}>
        {menuItems.map((item, index) => {
          return (
            <MenuItem {...item} key={index.toString()} onClick={(e) => onSelectMenuItem(item, e)} />
          );
        })}
      </Popover>
    </>
  );
};

export type { IProfile };
export default Profile;
