import { FormField } from '@components/workflow(deprecated)/step-form-generator';
import { AppFormType } from '@hooks/app-form';
import { Box, CardContent } from '@mui/material';
import { useMemo } from 'react';
import { FormFieldComponent } from './app-form-field';
import Card from '@components/card';
import CardHeader from '@components/card-header';
import { capitalizeFirstLetter, pascalToSpacedWords } from '@utils/generator-helper';
import { PredefinedFormMap } from '@config/forms';

const AppGenerativeForm = ({ formField, form }: { formField: FormField; form: AppFormType }) => {
	const { name, label, fields } = formField;

	const fieldConfigs = useMemo(() => {
		return fields.map((field) => {
			return {
				...field,
				name: `${name}.${field.name}`,
			};
		});
	}, [fields]);

	const PredefinedForm = PredefinedFormMap[capitalizeFirstLetter(label)];
	if (PredefinedForm) {
		return (
			<PredefinedForm
				form={form}
				parentKey={name}
			/>
		);
	}

	return (
		<Card>
			<CardHeader title={pascalToSpacedWords(label)} />
			<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
				{fieldConfigs.map((field) => {
					return (
						<FormFieldComponent
							key={field.name}
							form={form}
							fieldDef={field}
						/>
					);
				})}
			</CardContent>
		</Card>
	);
};

export default AppGenerativeForm;
