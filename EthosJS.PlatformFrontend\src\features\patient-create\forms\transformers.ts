import { z } from 'zod';
import { PatientBasicInformationData } from '../types/form-types';
import {
	EthosWorkflowsApiAddressDto,
	EthosWorkflowsApiAddressWithUseDto,
	EthosWorkflowsApiPatientGuardianDto,
	EthosWorkflowsApiPersonalAddressDto,
} from '@client/workflows';

const patientInformationTransformer = z
	.any()
	.transform((raw: PatientBasicInformationData['patientInformation']) => {
		if (!raw) {
			return raw;
		}
		return {
			...raw,
			middleName: raw.middleName === '' ? null : raw.middleName,
			mrn: raw.mrn === '' ? null : raw.mrn,
		};
	});

const demographicsTransformer = z
	.any()
	.transform((raw: PatientBasicInformationData['demographics']) => {
		return {
			...raw,
		};
	});

const physicalMeasurementsTransformer = z
	.any()
	.transform((raw: PatientBasicInformationData['physicalMeasurements']) => {
		if (!raw) {
			return raw;
		}
		return {
			...raw,
			bmi: raw.bmi ? Number(raw.bmi) : 0,
			heightInches: raw.heightInches ? Number(raw.heightInches) : 0,
			weightPounds: raw.weightPounds ? Number(raw.weightPounds) : 0,
			neckSize: raw.neckSize ? Number(raw.neckSize) : 0,
		};
	});

const patientBasicInformationSchema = z.object({
	patientInformation: patientInformationTransformer,
	demographics: demographicsTransformer,
	physicalMeasurements: physicalMeasurementsTransformer,
});

function patientBasicInformationTransformer<T>(data: T): PatientBasicInformationData {
	const { data: parsed } = patientBasicInformationSchema.safeParse(data);
	return parsed as PatientBasicInformationData;
}

const addressTrasnsformer = z.any().transform((raw: EthosWorkflowsApiAddressDto) => {
	return {
		...raw,
		line2: !raw.line2 ? null : raw.line2,
	};
});

const addressWithUseTypeTransformer = z
	.any()
	.transform((raw: EthosWorkflowsApiPersonalAddressDto) => {
		return {
			...raw,
			address: addressTrasnsformer.parse(raw.address),
		};
	});

const addressWithUseTransformer = z.any().transform((raw: EthosWorkflowsApiAddressWithUseDto) => {
	if (!raw.address) {
		return raw;
	}
	return {
		...raw,
		address: addressTrasnsformer.parse(raw.address),
	};
});

const guardianTransformer = z.any().transform((raw: EthosWorkflowsApiPatientGuardianDto) => {
	return {
		...raw,
		id: raw.id === '' ? null : raw.id,
		names: raw.names.map((name) => {
			return {
				...name,
				middleName: !name.middleName ? null : name.middleName,
			};
		}),
	};
});

function addressesWithUseTypeTransformer(data: EthosWorkflowsApiPersonalAddressDto[]) {
	return data.map((item) => addressWithUseTypeTransformer.parse(item));
}

function addressesWithUseTransformer(data: EthosWorkflowsApiAddressWithUseDto) {
	return addressWithUseTransformer.parse(data);
}

function guardiansTransformer(data: EthosWorkflowsApiPatientGuardianDto) {
	return guardianTransformer.parse(data);
}

export {
	patientBasicInformationSchema,
	patientBasicInformationTransformer,
	addressesWithUseTypeTransformer,
	addressesWithUseTransformer,
	guardianTransformer,
	guardiansTransformer,
};
