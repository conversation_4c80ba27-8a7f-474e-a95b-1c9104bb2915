import MainCardContainer from '@components/main-container/main-card-container';
import { Chip } from '@mui/material';
import { ShieldCheck } from 'lucide-react';

export default function NotStartedStatus({
	onStartVerification,
	disabled = false,
}: {
	onStartVerification: () => void;
	disabled?: boolean;
}) {
	return (
		<MainCardContainer
			title="Insurance Verification"
			headerSize="medium"
			icon={<ShieldCheck />}
			color="gray"
			emphasis="low"
			descriptionSubheader="System is going to check coverage and eligibility for the ordered services."
			customAction={
				<Chip
					label="Not Started"
					sx={{ borderRadius: 2, color: 'black', cursor: disabled ? 'default' : 'pointer' }}
					onClick={disabled ? undefined : onStartVerification}
					disabled={disabled}
				/>
			}
		/>
	);
}
