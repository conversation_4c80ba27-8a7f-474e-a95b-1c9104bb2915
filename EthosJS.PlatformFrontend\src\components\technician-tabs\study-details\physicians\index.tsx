import Card from "@components/card";
import CardHeader from "@components/card-header";
import { Box, CardContent, Stack, Typography } from "@mui/material";
import styles from "@components/technician-tabs/styles";
import Summary from "@components/technician-tabs/summary";

interface StudyDetailsTypeProp {
   title?: string
}


const StudyDetailsPhysicians = (props: StudyDetailsTypeProp) => {

   const { title } = props;

   return (
      <Card>
         <CardHeader
            {...{
               title: <Typography>{title}</Typography>,
               emphasis: 'dark'
            }}
         />
         <Box sx={styles.cardBody}>
            <Stack gap={2}>
               <Stack>
                  <Card sx={{ borderBottomLeftRadius: 0, borderBottomRightRadius: 0 }} >
                     <CardHeader
                        {...{
                           title: <Typography>{'Dr. <PERSON>'}</Typography>,
                        }}
                     />
                     <CardContent>
                        <Summary
                           data={[
                              {
                                 value: 'Primary Care Physician',
                              },
                              {
                                 label: 'NPI',
                                 value: '**********' as string
                              },
                              {
                                 label: 'Specialty',
                                 value: 'Specialty' as string
                              },
                           ]}
                        />
                     </CardContent>
                  </Card>
                  <Card sx={{ borderRadius: 0 }} >
                     <CardHeader
                        {...{
                           title: <Typography>{'Dr. <PERSON>'}</Typography>,
                        }}
                     />
                     <CardContent>
                        <Summary
                           data={[
                              {
                                 value: 'Primary Care Physician',
                              },
                              {
                                 label: 'NPI',
                                 value: '**********' as string
                              },
                              {
                                 label: 'Specialty',
                                 value: 'Specialty' as string
                              },
                           ]}
                        />
                     </CardContent>
                  </Card>
                  <Card sx={{ borderTopLeftRadius: 0, borderTopRightRadius: 0 }}>
                     <CardHeader
                        {...{
                           title: <Typography>{'Dr. Michael Chen'}</Typography>,
                        }}
                     />
                     <CardContent>
                        <Summary
                           data={[
                              {
                                 value: 'Primary Care Physician',
                              },
                              {
                                 label: 'NPI',
                                 value: '**********' as string
                              },
                              {
                                 label: 'Specialty',
                                 value: 'Specialty' as string
                              },
                           ]}
                        />
                     </CardContent>
                  </Card>
               </Stack>
            </Stack>
         </Box>
      </Card>
   )
}

export default StudyDetailsPhysicians;