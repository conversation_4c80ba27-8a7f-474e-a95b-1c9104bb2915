import { IPrimitiveQuery } from './core';
import { EntityType } from './entity-types';

export interface DraftQ extends IPrimitiveQuery {
	$type: 'WithId' | 'WithEntityId' | 'WithIndexValue';
}

export const DraftQuery = {
	withId: (id: string): DraftQ => ({
		$type: 'WithId',
		Id: id,
	}),

	withEntityId: (type: EntityType, entityId: string): DraftQ => ({
		$type: 'WithEntityId',
		Type: type,
		Id: entityId,
	}),

	withIndexValue: (name: string, indexValue: string): DraftQ => ({
		$type: 'WithIndexValue',
		Name: name,
		Value: indexValue,
	}),
};
