import { PatientCreate, PatientRead } from "@auth/scopes";
import { EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_AddedBasicInformation as AddBasicInformation, EthosWorkflowsWorkflowAddNewPatientAddNewPatientState as AddNewPatientState } from "@client/workflows";
import { getApiAddNewPatientStateByIdOptions, postApiAddNewOrderListOptions } from "@client/workflows/@tanstack/react-query.gen";
import Card from "@components/card";
import WorkflowProgressCardSkeleton from "@components/workflow-progress-card-skeleton";
import { Box, CardContent, LinearProgress, Stack, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { find } from "lodash";
import { CheckCircle, CheckCircle2 } from "lucide-react";
import { calculateWorkflowStatus, IWorkflowState } from '@utils/flows';
import { useEffect, useState } from "react";
import { Status } from "@components/left-menu";
import { createPatientSteps } from "@features/patient-create/components/steps/-constants";

const defaultCreatePatientState = {
    progress: 0,
    status: Status.None,
    lastUpdate: '',
    steps: createPatientSteps,
    currentStep: createPatientSteps[0],
    selectedStep: null
};

interface FlowStatusProps {
    workflowId: string;
    navigateToFlow: ({ addNewPatient, patientId }: { addNewPatient: string, patientId: string }) => void;
}

export default function FlowStatus({ workflowId, navigateToFlow }: FlowStatusProps) {

    const [workflowState, setWorkflowState] = useState<IWorkflowState<typeof createPatientSteps[number]>>(defaultCreatePatientState);

    const { progress, status } = workflowState ?? { progress: 0 };

    const { data: patientData, isFetching } = useQuery(getApiAddNewPatientStateByIdOptions({
        path: { id: workflowId },
        scopes: [PatientCreate.value, PatientRead.value],
        responseType: 'json'
    }));

    const { entityLinks } = patientData as AddNewPatientState ?? {};
    const { Patient } = entityLinks ?? {}

    const {
        data: orders,
    } = useQuery({
        ...postApiAddNewOrderListOptions({
            scopes: [PatientCreate.value, PatientRead.value],
            responseType: 'json',
            body: {
                Patient: Patient
            },
        }),
        enabled: !!Patient
    });

    useEffect(() => {
        if (patientData) {
            const newState = calculateWorkflowStatus(createPatientSteps, patientData);
            setWorkflowState(newState);
        }
    }, [patientData])


    if (isFetching) {
        return <WorkflowProgressCardSkeleton />;
    }

    const getPatientName = (data: AddNewPatientState | undefined): string => {
        if (!data || !data.pastTransitions) return 'New Patient';

        const addBasicInfo = find(data.pastTransitions, { key: 'AddBasicInformation' });
        if (!addBasicInfo) return 'New Patient';

        const basicInfo = (addBasicInfo as any).data as AddBasicInformation;
        const firstName = basicInfo?.patientInformation?.firstName || '';
        const lastName = basicInfo?.patientInformation?.lastName || '';

        return firstName && lastName ? `${firstName} ${lastName}` : firstName || lastName || 'New Patient';
    };

    const workflowName = getPatientName(patientData);

    return (
        <Card sx={{ maxWidth: 400, width: "100%", cursor: 'pointer' }} onClick={() => {
            if (!Patient) return;
            navigateToFlow({ addNewPatient: workflowId, patientId: Patient });
        }}>
            <CardContent>
                <Stack spacing={2}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Typography variant="h6" component="div">
                            {workflowName}
                        </Typography>
                        {progress === 100 && <CheckCircle size={20} color="success.main" />}
                    </Box>

                    <Box>
                        <Box display="flex" justifyContent="space-between" mb={0.5}>
                            <Typography variant="body2" color="text.secondary">
                                Progress
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                                {progress.toFixed(0)}%
                            </Typography>
                        </Box>
                        <LinearProgress
                            variant="determinate"
                            value={progress}
                            sx={{
                                height: 8,
                                borderRadius: 4,
                                backgroundColor: "grey.200",
                                "& .MuiLinearProgress-bar": {
                                    borderRadius: 4,
                                    backgroundColor: progress === 100 ? "success.main" : "primary.main",
                                },
                            }}
                        />
                    </Box>
                    <Box display="flex" gap={3}>
                        <Box display="flex" alignItems="center" gap={1}>
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    width: 24,
                                    height: 24,
                                    borderRadius: "50%",
                                    backgroundColor: "primary.main",
                                    color: "white",
                                }}
                            >
                                <Typography variant="caption" fontWeight="bold">
                                    O
                                </Typography>
                            </Box>
                            <Typography variant="body2">
                                {orders?.length} {orders?.length === 1 ? "Order" : "Orders"}
                            </Typography>
                        </Box>
                        {/* <Box display="flex" alignItems="center" gap={1}>
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    width: 24,
                                    height: 24,
                                    borderRadius: "50%",
                                    backgroundColor: "secondary.main",
                                    color: "white",
                                }}
                            >
                                <Typography variant="caption" fontWeight="bold">
                                    S
                                </Typography>
                            </Box>
                            <Typography variant="body2">
                                {studyCount} {studyCount === 1 ? "Study" : "Studies"}
                            </Typography>
                        </Box> */}
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                        {status === "completed"
                            ? <Box display="flex" gap={1} alignItems="center"><CheckCircle2 size={16} />Completed</Box>
                            : status === Status.InProgress
                                ? "In progress"
                                : ""}
                    </Typography>
                </Stack>
            </CardContent>
        </Card>
    );
}
