import { useState } from 'react';
import { Button, CardContent, FormControl, lighten, MenuItem, Select, Stack } from '@mui/material';
import ChipSummary from '@components/chip-summary';
import Card from '@components/card';
import CardHeader from '@components/card-header';
import { withForm } from '@hooks/app-form';
import { filter, indexOf, map } from 'lodash';
import {
	EthosWorkflowsApiCreateStudyDto,
	EthosWorkflowsApiInsuranceOutputDto,
} from '@client/workflows';
import { formOptions, useStore } from '@tanstack/react-form';
import { useRefDataValues } from '@hooks/use-ref-data-values';

const associatedInsuranceFormOptions = formOptions({
	defaultValues: {
		insurances: [] as EthosWorkflowsApiCreateStudyDto['insurances'],
	},
	props: {
		insurances: [] as Array<EthosWorkflowsApiInsuranceOutputDto>,
	},
});

const AssociatedInsurances = withForm({
	...associatedInsuranceFormOptions,
	render: function Render({ insurances, form }) {
		const [activeTabs, setActiveTabs] = useState<{ [key: string]: string }>({});

		const handleTabChange = (insuranceId: string, newTab: string) => {
			setActiveTabs((prev) => ({
				...prev,
				[insuranceId]: newTab,
			}));
		};

		const currentInsurances = useStore(form.store, (state) => state.values.insurances) || [];

		const { values: insuranceCarriers } = useRefDataValues({
			ids: map(insurances, 'insuranceCarrier') as number[],
		});

		return (
			<Stack sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
				{insurances
					.filter((insurance) => insurance.id !== null && insurance.id !== undefined)
					.map((insurance, index) => {
						const insIdStr = insurance.id?.toString() ?? '';
						const activeTab = activeTabs[insIdStr] || 'basic';

						const tabButton = (label: string, key: string) => (
							<Button
								key={key}
								onClick={() => handleTabChange(insIdStr, key)}
								variant={activeTab === key ? 'contained' : 'outlined'}>
								{label}
							</Button>
						);
						const priorityIndex = indexOf(currentInsurances, insIdStr);
						console.log({
							currentInsurances,
							priorityIndex,
							insurances,
						});
						return (
							<Card
								key={index}
								color="primary">
								<CardHeader
									title={insuranceCarriers ? (insuranceCarriers[index]?.title ?? '') : ''}
									action={
										<FormControl size="small">
											<Select
												value={priorityIndex}
												onChange={(e) => {
													const priority = Number(e.target.value);
													const currentInsrancesLocal = [...currentInsurances];
													const idx = indexOf(currentInsrancesLocal, insIdStr);

													// Remove existing entry first
													if (idx !== -1) {
														currentInsrancesLocal.splice(idx, 1);
													}

													// Add at new priority position
													if (priority !== -1) {
														// Insert at the specific priority position, shifting others if needed
														currentInsrancesLocal.splice(priority, 0, insIdStr);
													}

													// Clean up to ensure no gaps - filter out empty strings
													const cleanedInsurances = currentInsrancesLocal.filter(
														(ins) => ins && ins.trim() !== ''
													);

													form.setFieldValue('insurances', cleanedInsurances);
												}}>
												<MenuItem value={-1}>Not Selected</MenuItem>
												<MenuItem value={0}>Primary</MenuItem>
												<MenuItem
													value={1}
													disabled={insurances.length < 2}>
													Secondary
												</MenuItem>
												<MenuItem
													value={2}
													disabled={insurances.length < 3}>
													Tertiary
												</MenuItem>
											</Select>
										</FormControl>
									}
								/>
								<CardContent
									sx={(theme) => ({
										backgroundColor: lighten(theme.palette.primary.light, 0.95),
									})}>
									<Stack
										direction="row"
										spacing={2}
										mb={2}>
										{tabButton('Basic Details', 'basic')}
										{tabButton('Contact Info', 'contact')}
										{tabButton('Address', 'address')}
									</Stack>
									{activeTab === 'basic' && (
										<ChipSummary
											items={
												filter(
													[
														insurance?.memberId && {
															label: 'Member ID',
															value: `#${insurance?.memberId}`,
														},
														insurance?.policyId && {
															label: 'Policy ID',
															value: `#${insurance?.policyId}`,
														},
														insurance?.groupNumber && {
															label: 'Group Number',
															value: `#${insurance?.groupNumber}`,
														},
													],
													Boolean
												) as { label: string; value: string }[]
											}
										/>
									)}
									{activeTab === 'contact' && (
										<ChipSummary
											items={
												//getContactSummaryItems(insurance)
												[]
											}
										/>
									)}
									{activeTab === 'address' && (
										<ChipSummary
											items={
												//getAddressSummaryItems(insurance)
												[]
											}
										/>
									)}
								</CardContent>
							</Card>
						);
					})}
			</Stack>
		);
	},
});

export default AssociatedInsurances;
