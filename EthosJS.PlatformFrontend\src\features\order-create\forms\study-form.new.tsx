import { Box, Button, InputAdornment, Stack, TextField } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { map, find, debounce, filter } from 'lodash';
import { formOptions, useStore } from '@tanstack/react-form';
import { useEffect, useMemo, useState } from 'react';
import { FormProps } from '@components/forms/predefined-form-props';
import { useRefDataOptions } from '@hooks/use-ref-data-options';
import MainCardContainer from '@components/main-container/main-card-container';
import LoadingComponent from '@components/loading-component';
import { Prettify } from '@utils/types';
import StepCardControl from '@components/step-card-control';
import { MonitorHeart } from '@mui/icons-material';
import { Hospital, Search } from 'lucide-react';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { postApiCareLocationSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import { useQuery } from '@tanstack/react-query';
import { QueryDto, Query, LiteralQuery, CareLocationQ, CareLocationQuery } from '@utils/query-dsl';
import ChipSummary from '@components/chip-summary';
import { EthosModelQueryDto1EthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null } from '@client/workflows';
import { studyFormDataToDto } from './transformers';

type StudyAndCarelocationFormData = Prettify<{
	studyData: {
		encounterType: number | null;
		studyType: number | null;
		studyAttributes: Array<number> | null;
	};
	careLocationId: string | null;
}>;

type StudyFormProps = Prettify<
	Omit<FormProps<StudyAndCarelocationFormData>, 'onValidate' | 'onSaveDraft' | 'onSubmit'> & {
		orderId: string;
		onSubmit: (data: StudyAndCarelocationFormData) => void;
		onSaveDraft: (data: StudyAndCarelocationFormData) => void;
	}
>;

const defaultValues = {
	studyData: {
		encounterType: null,
		studyType: null,
		studyAttributes: null,
	},
	careLocationId: null,
};

function studyFormOptions(savedData?: StudyAndCarelocationFormData) {
	return formOptions({
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	});
}

export default function StudyForm({ onSubmit, savedData, onSaveDraft, orderId }: StudyFormProps) {
	const options = useMemo(() => studyFormOptions(savedData), [savedData]);
	const form = useAppForm({
		...options,
		onSubmit: async ({ value }) => {
			//@ts-expect-error type mismatch
			const studyTransformed = studyFormDataToDto({
				orderId,
				...value.studyData,
				insurances: [],
			});
			onSubmit({
				//@ts-expect-error type mismatch
				studyData: studyTransformed,
				careLocationId: value.careLocationId,
			});
		},
	});

	const [userSearchValue, setUserSearchValue] = useState('');
	const [searchQuery, setSearchQuery] = useState<QueryDto<CareLocationQ>>(
		Query.literal(CareLocationQuery.withApproximateName(''))
	);

	const debouncedSearch = useMemo(
		() =>
			debounce((value: string) => {
				setSearchQuery((prevQ) => {
					if (prevQ?.$type === 'And') {
						return Query.and([
							...filter(
								prevQ.Exprs,
								(q) =>
									q.$type === 'Literal' &&
									(q as LiteralQuery<CareLocationQ>).Value.$type !== 'WithApproximateName'
							),
							Query.literal(CareLocationQuery.withApproximateName(value)),
						]);
					}
					return Query.literal(CareLocationQuery.withApproximateName(value));
				});
			}, 300),
		[]
	);

	useEffect(() => {
		debouncedSearch(userSearchValue);
	}, [debouncedSearch, userSearchValue]);

	const { data: careLocations, isFetching: isCareLocationFetching } = useQuery({
		...postApiCareLocationSearchOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			body: searchQuery as unknown as EthosModelQueryDto1EthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		select: (data) =>
			map(data?.items, (item) => ({
				id: item.id,
				label: item.name,
				data: [
					map(item.contactDetail?.phoneNumbers, (phoneNumber) => ({
						label: 'Phone',
						value: phoneNumber.phoneNumber,
					})),
					map(item.contactDetail?.emails, (email) => ({
						label: 'Email',
						value: email.email,
					})),
					map(item.supportedEncounterTypes, (encounterType) => ({
						label: 'Encounter Type',
						value: encounterType.toString(),
					})),
					map(item.supportedStudyTypes, (studyType) => ({
						label: 'Study Type',
						value: studyType.toString(),
					})),
				],
			})) ?? [],
		initialData: {
			items: [],
		},
	});

	const [encounterTypeName, setEncounterTypeName] = useState<string | null>(null);
	const [studyTypeName, setStudyTypeName] = useState<string | null>(null);

	const { values } = useStore(form.store, (state) => ({ values: state.values }));

	const { options: encounterTypeOptions, isFetching: isFetchingEncounterTypeOptions } =
		useRefDataOptions({ setName: 'encounterType' });

	const { options: studyTypeOptions, isFetching: isFetchingStudyTypeOptions } = useRefDataOptions({
		setName: 'studyType',
		filter: `encounterType eq ${encounterTypeName}`,
	});

	const { options: studyAttributesOptions, isFetching: isFetchingStudyAttributesOptions } =
		useRefDataOptions({
			setName: 'studyAttributes',
			filter: `studyType eq ${studyTypeName}`,
		});

	useEffect(() => {
		const savedEncId = form.getFieldValue('studyData.encounterType');
		if (encounterTypeOptions && savedEncId) {
			const encItem = find(encounterTypeOptions, { value: savedEncId.toString() });
			if (encItem) {
				setEncounterTypeName(encItem.title);
			}
		}
	}, [encounterTypeOptions, form]);

	useEffect(() => {
		const savedStudId = form.getFieldValue('studyData.studyType');
		if (studyTypeOptions && savedStudId) {
			const stItem = find(studyTypeOptions, { value: savedStudId.toString() });
			if (stItem) {
				setStudyTypeName(stItem.title);
			}
		}
	}, [studyTypeOptions, form]);

	return (
		<Box
			sx={{ height: '100%', overflow: 'auto', pb: 8 }}
			component="form"
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<Stack gap={3}>
				<Stack sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
					{isFetchingEncounterTypeOptions ? (
						<LoadingComponent />
					) : (
						<form.AppField
							name="studyData.encounterType"
							children={(field) => {
								return (
									<MainCardContainer
										title="Encounter Type"
										color="primary"
										icon={<MonitorHeart />}
										emphasis={values.studyData.encounterType ? 'high' : 'low'}
										descriptionSubheader="Select the appropriate study and attributes based on the physician's order."
										descriptionText="Each type has different protocols and equipment requirements.">
										<field.AppRadioField
											label="1. Encounter Type"
											options={encounterTypeOptions ?? []}
											onValueChange={(value) => {
												if (typeof value === 'string') {
													form.setFieldValue('studyData.studyType', null);
													form.setFieldValue('studyData.studyAttributes', []);
													setStudyTypeName(null);

													const encItem = find(encounterTypeOptions, {
														value,
													});
													if (encItem) {
														setEncounterTypeName(encItem.title);
													} else {
														setEncounterTypeName(null);
													}
												}
											}}
										/>
									</MainCardContainer>
								);
							}}
						/>
					)}
					{!!encounterTypeName && (
						<form.AppField
							name="careLocationId"
							children={(field) => (
								<MainCardContainer
									title="Care Location"
									color="primary"
									icon={<Hospital />}
									emphasis={values.careLocationId ? 'high' : 'low'}
									descriptionSubheader="Select the appropriate study and attributes based on the physician's order."
									descriptionText="Each type has different protocols and equipment requirements.">
									{isCareLocationFetching ? (
										<LoadingComponent />
									) : (
										<Stack
											spacing={2}
											sx={{ py: 2, flex: 1, overflow: 'auto', minHeight: 0 }}>
											<TextField
												label="Search"
												variant="outlined"
												fullWidth
												value={userSearchValue}
												slotProps={{
													input: {
														startAdornment: (
															<InputAdornment position="start">
																<Search />
															</InputAdornment>
														),
													},
												}}
												placeholder="Search locations by name or address.."
												onChange={(e) => setUserSearchValue(e.target.value)}
											/>
											{Array.isArray(careLocations) &&
												careLocations?.map((item, index) => {
													const { label: title, data } = item;

													const checked = item?.id === values?.careLocationId;

													const filteredData = filter(data, (item) => item.length > 0);

													return (
														<Box key={index.toString()}>
															<MainCardContainer
																title={title}
																containerSx={{ maxHeight: `300px` }}
																color={checked ? 'primary' : 'gray'}
																emphasis={checked ? 'high' : 'low'}
																sx={{ minHeight: 'content' }}
																onClick={() => field.setValue(item.id)}
																secondaryActionType="Selection"
																selectionSelected={checked}
																selectionLabel={checked ? 'Selected' : 'Select'}
																onSelectionChange={() => field.setValue(item.id)}>
																{filteredData.length > 0 ? (
																	<Stack gap={1}>
																		{data.map((d, dIndex) => (
																			<ChipSummary
																				key={dIndex.toString()}
																				items={d.map(({ ...item }) => ({
																					label: item.label ? item.label : '',
																					value: item.value,
																				}))}
																				hideSeperator
																			/>
																		))}
																	</Stack>
																) : (
																	<Box sx={{ py: 2, display: 'flex', justifyContent: 'center' }}>
																		No Data
																	</Box>
																)}
															</MainCardContainer>
														</Box>
													);
												})}
										</Stack>
									)}
								</MainCardContainer>
							)}
						/>
					)}
					{!!encounterTypeName &&
						values.careLocationId &&
						(isFetchingStudyTypeOptions ? (
							<LoadingComponent />
						) : (
							<form.AppField
								name="studyData.studyType"
								children={(field) => (
									<field.AppRadioField
										label="2. Study Type"
										options={studyTypeOptions ?? []}
										onValueChange={(value) => {
											if (typeof value === 'string') {
												form.setFieldValue('studyData.studyAttributes', []);
												const stItem = find(studyTypeOptions, {
													value,
												});
												if (stItem) {
													setStudyTypeName(stItem.title);
												} else {
													setStudyTypeName(null);
												}
											}
										}}
									/>
								)}
							/>
						))}

					{!!studyTypeName &&
						(isFetchingStudyAttributesOptions ? (
							<LoadingComponent />
						) : (
							<form.AppField
								name="studyData.studyAttributes"
								children={(field) => (
									<field.AppRadioField
										label="3. Study Attributes"
										isMulti
										options={studyAttributesOptions ?? []}
									/>
								)}
							/>
						))}
				</Stack>
				<StepCardControl>
					<Button
						variant="outlined"
						color="primary"
						onClick={() => onSaveDraft(values)}>
						Save Draft
					</Button>
					<form.Subscribe
						selector={({ isDirty, canSubmit, isSubmitting }) => ({
							isDirty,
							canSubmit,
							isSubmitting,
						})}>
						{({ isDirty, canSubmit, isSubmitting }) => (
							<Button
								variant="contained"
								color="primary"
								type="submit"
								loading={isSubmitting}
								disabled={!isDirty || !canSubmit}>
								Next
							</Button>
						)}
					</form.Subscribe>
				</StepCardControl>
			</Stack>
		</Box>
	);
}
