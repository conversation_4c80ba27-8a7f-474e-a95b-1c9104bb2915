import { createFileRoute, redirect } from '@tanstack/react-router';
import ConfirmationStep from '@features/order-create/components/steps/confirmation.step';
import { useCallback } from 'react';
import useInsuranceVerificationStart from '@features/insruance-verification/hooks/use-insurance-verification-start';

export const Route = createFileRoute('/_dashboard/patients/$patientId/order/confirmation')({
	component: RouteComponent,
	loaderDeps: ({ search }) => ({ ...search }),
	beforeLoad: ({ params, search }) => {
		const { patientId } = params;
		const { orderId } = search;
		if (!orderId) {
			throw redirect({
				to: '/patients/$patientId/order/study',
				params: { patientId: patientId },
				search,
			});
		}
	},
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const navigate = Route.useNavigate();

	const { startInsuranceVerification } = useInsuranceVerificationStart();

	const onSuccessCallback = useCallback(() => {
		startInsuranceVerification({
			studyId: studyId!,
			serviceId: '00000000-0000-0000-0000-000000000001',
		});
		if (studyId) {
			navigate({
				to: '/patients/$patientId/insurance/insurance-verification',
				params: { patientId },
				search: { orderId: orderId!, studyId },
			});
		}
	}, [startInsuranceVerification, studyId, navigate, patientId, orderId]);

	return (
		<ConfirmationStep
			patientId={patientId}
			orderId={orderId!}
			studyId={studyId!}
			successCallback={onSuccessCallback}
		/>
	);
}
