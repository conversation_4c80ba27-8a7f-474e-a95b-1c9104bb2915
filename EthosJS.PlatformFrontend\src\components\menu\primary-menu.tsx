import { ReactElement } from 'react';
import MenuContainer from './menu-container';
import { MenuItem } from '@mui/material';
import MenuFooter, { MenuFooterProps } from './menu-footer';
import PrimaryMenuHeader, { PrimaryMenuHeaderProps } from './primary-menu-header';
import StyledList from './menu-list';

interface PrimaryMenuProps {
	color: 'primary';
	headerProps?: PrimaryMenuHeaderProps;
	children: ReactElement<typeof MenuItem> | ReactElement<typeof MenuItem>[];
	footerProps?: MenuFooterProps;
}

export default function PrimaryMenu({ headerProps, children, footerProps }: PrimaryMenuProps) {
	return (
		<MenuContainer
			color="primary"
			gap={1}>
			{headerProps && <PrimaryMenuHeader {...headerProps} />}
			<StyledList>{children}</StyledList>
			{footerProps && <MenuFooter {...footerProps} />}
		</MenuContainer>
	);
}
