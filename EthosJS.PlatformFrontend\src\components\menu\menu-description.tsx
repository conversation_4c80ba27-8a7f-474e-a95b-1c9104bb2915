import { Box, lighten, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import { PropsWithChildren } from 'react';

const StyledDescription = styled(Box)(({ theme }) => ({
	padding: theme.spacing(1.25),
	display: 'flex',
	justifyContent: 'center',
	flexDirection: 'column',
	color: theme.palette.primary.dark,
	borderRadius: theme.shape.borderRadius - 2,
	border: `1px solid ${theme.palette.primary.light}`,
	backgroundColor: lighten(theme.palette.primary.light, 0.9),
}));

export default function MenuDescription({ text, children }: { text: string } & PropsWithChildren) {
	return (
		<StyledDescription>
			<Typography
				variant="caption"
				sx={{ fontWeight: 600, color: 'inherit' }}>
				{text}
			</Typography>
			{children}
		</StyledDescription>
	);
}
