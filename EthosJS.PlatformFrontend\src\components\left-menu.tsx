import { ReactNode } from 'react';
import {
    ListItemButton,
    alpha,
    styled,
    Theme,
    ListItemText,
    lighten,
    ListItemIcon,
} from '@mui/material';
import { CheckCircleOutlineRounded, WarningAmberRounded as WarningAmberIcon, SvgIconComponent, HourglassTop, LockOutlined } from '@mui/icons-material';
import { darken } from '@mui/material/styles'; // New import
import { LucideIcon } from 'lucide-react';

// Move the Status enum here so it can be used in styled props
export enum Status {
    InProgress = 'in-progress',
    Completed = 'completed',
    MissingInformation = 'missing-information',
    None = 'none',
    Failed = 'Failed',
    Warning = 'Warning',
}

const rootStyles = ({ theme, selected }: { theme: Theme, selected: boolean }) => {

    return [
        {
            backgroundColor: theme.palette.background.paper,
            border: `1.5px solid ${selected ? theme.palette.grey[300] : theme.palette.grey[200]}`,
            "& .MuiListItemIcon-root": {
                backgroundColor: theme.palette.grey[200],
            }
        },
        theme.applyStyles('dark', {
            border: `1.5px solid ${selected ? theme.palette.grey[900] : theme.palette.grey[700]}`,
            "& .MuiListItemIcon-root": {
                backgroundColor: theme.palette.grey[900],
            }
        }),
    ]
};

const inProgressStyles = ({ theme, selected }: { theme: Theme, selected: boolean }) => {

    const borderColor = selected ? theme.palette.primary.main : lighten(theme.palette.primary.main, 0.5);
    const backgroundColor = lighten(theme.palette.primary.light, 0.85);
    const color = darken(theme.palette.primary.main, 0.5);
    return (
        [
            {
                backgroundColor,
                color,
                border: `1.5px solid ${borderColor}`,
                "& .MuiListItemIcon-root": {
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.primary.contrastText
                },
            },
            theme.applyStyles('dark', {
                backgroundColor: alpha(theme.palette.primary.light, 0.9),
                border: `1.5px solid ${theme.palette.primary.main}`,
                color: theme.palette.primary.contrastText,
                "& .MuiListItemIcon-root": {
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.primary.contrastText,
                },
            })
        ]
    )
}

const completedStyles = ({ theme, selected }: { theme: Theme, selected: boolean }) => {
    const borderColor = selected ? theme.palette.success.main : lighten(theme.palette.success.main, 0.5);
    const backgroundColor = lighten(theme.palette.success.light, 0.85);
    const color = darken(theme.palette.success.main, 0.5);
    return [
        {
            backgroundColor,
            color,
            border: `1.5px solid ${borderColor}`,
            "& .MuiListItemIcon-root": {
                backgroundColor: theme.palette.success.main,
                color: theme.palette.success.contrastText
            },
        },
        theme.applyStyles('dark', {
            backgroundColor: alpha(theme.palette.success.light, 0.9),
            border: `1.5px solid ${theme.palette.success.main}`,
            "& .MuiListItemIcon-root": {
                backgroundColor: theme.palette.success.main,
                color: theme.palette.success.contrastText,
            }
        })
    ]
}

const missingInformationStyles = ({ theme, selected }: { theme: Theme, selected: boolean }) => {
    const borderColor = selected ? theme.palette.warning.main : lighten(theme.palette.warning.main, 0.5);
    const backgroundColor = lighten(theme.palette.warning.light, 0.85);
    const color = darken(theme.palette.warning.main, 0.5);
    return [
        {
            backgroundColor,
            color,
            border: `1.5px solid ${borderColor}`,
            "& .MuiListItemIcon-root": {
                backgroundColor: theme.palette.warning.main,
                color: theme.palette.warning.contrastText
            },
        },
        theme.applyStyles('dark', {
            backgroundColor: alpha(theme.palette.warning.light, 0.9),
            border: `1.5px solid ${theme.palette.warning.main}`,
            color: theme.palette.warning.contrastText,
            "& .MuiListItemIcon-root": {
                backgroundColor: theme.palette.warning.main,
                color: theme.palette.warning.contrastText,
            }
        })
    ]
}


const listItemIconMixin = (theme: Theme) => ({
    minHeight: '100%',
    width: 44,
    minWidth: 44,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transitions: theme.transitions.create('background-color'),
    borderRadious: 0,
});

// Add status and selected props to styled component
const StyledListItemButton = styled(ListItemButton, {
    skipSx: true,
    shouldForwardProp: (prop) => prop !== 'status' && prop !== 'selected',
})<{ status: Status; selected: boolean }>(({ theme, status, selected }) => {
    let stylesToApply = rootStyles;
    switch (status) {
        case Status.InProgress:
            stylesToApply = inProgressStyles;
            break;
        case Status.Completed:
            stylesToApply = completedStyles;
            break;
        case Status.MissingInformation:
            stylesToApply = missingInformationStyles;
            break;
        default:
            break;
    }
    return [{
        padding: 0,
        height: 52,
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'clip',
        gap: theme.spacing(1),
        borderRadius: theme.shape.borderRadius + 2,
        ':not(:last-child)': {
            marginBottom: theme.spacing(1),
        },
        "& .MuiListItemIcon-root": {
            ...listItemIconMixin(theme)
        },
        "& .MuiListItemText-root": {
            color: 'inherit',
        },

    },
    ...stylesToApply({ theme, selected }),
    ]
});

const StyledStatusIcon = styled('div', {
    shouldForwardProp: (prop) => prop !== 'status',
})<{ status: Status }>(({ theme, status }) => {
    //const { color } = getColors(theme, status);
    return {
        ...listItemIconMixin(theme),
        color: 'inherit',
    };
})

export interface LeftMenuProps {
    stepNumber?: number;
    name: string;
    status?: Status;
    selected?: boolean;
    disabled?: boolean;
    icon: SvgIconComponent | LucideIcon;
    onClick?: () => void;
}

export default function LeftMenu({
    stepNumber = 1,
    name,
    status = Status.None,
    selected = false,
    disabled = false,
    icon: Icon,
    onClick
}: LeftMenuProps) {
    // Determine status label and icon based on status
    let statusLabel: string | null = null;
    let statusIcon: ReactNode = null;

    switch (status) {
        case Status.InProgress:
            statusLabel = 'In Progress';
            statusIcon = <HourglassTop />;
            break;

        case Status.Completed:
            statusLabel = 'Completed';
            statusIcon = <CheckCircleOutlineRounded />;
            break;

        case Status.MissingInformation:
            statusLabel = 'Missing Information';
            statusIcon = <WarningAmberIcon />;
            break;

        case Status.None:
            if (disabled) {
                statusLabel = 'Locked';
                statusIcon = <LockOutlined />;
            }
            break
        default:
            statusLabel = null;
            statusIcon = null;
            break;
    }

    return (
        <StyledListItemButton status={status} selected={selected} disabled={disabled} onClick={onClick}>
            <ListItemIcon >
                <Icon />
            </ListItemIcon>
            <ListItemText
                slotProps={{
                    primary: {
                        variant: 'asapCondensed',
                        fontSize: '0.875rem',
                        textOverflow: 'ellipsis',
                        maxWidth: '100%'
                    },
                    secondary: {
                        variant: 'asapCondensed',
                        fontSize: '0.75rem',
                        color: 'inherit',

                    },
                }}
                sx={{
                    flex: 1,
                    minWidth: 0,
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                    overflow: 'hidden',
                }}
                primary={`${stepNumber}. ${name}`} secondary={statusLabel} />
            {statusIcon && (
                <StyledStatusIcon status={status}>
                    {statusIcon}
                </StyledStatusIcon>
            )}
        </StyledListItemButton>
    );
}