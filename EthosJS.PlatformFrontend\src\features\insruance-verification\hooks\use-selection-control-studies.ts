import { postApiStudySearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import { StudyQuery, Query } from '@utils/query-dsl';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { useQueries, useQuery } from '@tanstack/react-query';
import { EthosModelQueryDto1EthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null } from '@client/workflows';
import { getApiReferenceSetsValuesOptions } from '@client/refdata/@tanstack/react-query.gen';
import { find, map, some } from 'lodash';
import { ReferenceDataSetKeyValueDto } from '@client/refdata';

export default function useSelectionControlStudies({ orderId }: { orderId?: string }) {
	const { data: studies } = useQuery({
		...postApiStudySearchOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			body: Query.literal(
				StudyQuery.withOrderId(orderId!)
			) as unknown as EthosModelQueryDto1EthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!orderId,
		select: (data) => data?.items ?? [],
		initialData: {
			items: [],
		},
	});

	console.log('studies', studies);

	const { data: studyTypes } = useQueries({
		queries: studies?.length
			? studies.map((study) => {
					const { studyType } = study;
					return getApiReferenceSetsValuesOptions({
						scopes: [PatientCreate.value, PatientRead.value],
						responseType: 'json',
						query: {
							setName: 'studyType',
							filter: `id eq ${studyType}`,
						},
					});
				})
			: [],
		combine: (results) => {
			return {
				data: map(results, (result) => result.data),
				isPending: some(results, (result) => result.isPending),
			};
		},
	});

	const combinedStudies =
		studies?.length && studyTypes?.length
			? map(studies, (study) => {
					const studyType = find(studyTypes, {
						id: study.studyType,
					}) as ReferenceDataSetKeyValueDto;
					return {
						...study,
						//@ts-expect-error Type Mismatch
						studyTypeName: studyType?.values?.name?.toString() ?? '',
					};
				})
			: [];

	return {
		studies: combinedStudies,
	};
}
