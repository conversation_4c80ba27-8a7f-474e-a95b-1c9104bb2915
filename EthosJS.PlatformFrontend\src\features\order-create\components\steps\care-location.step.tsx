import {
	<PERSON>ack,
	TextField,
	InputAdornment,
	Box,
	Button,
	Grid,
	debounce,
	CircularProgress,
} from '@mui/material';
import { Hospital, Search } from 'lucide-react';
import ChipSummary from '@components/chip-summary';
import { useEffect, useMemo, useState } from 'react';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-store';
import StepCardControl from '@components/step-card-control';
import { useQuery } from '@tanstack/react-query';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { Query, CareLocationQuery, CareLocationQ, QueryDto, LiteralQuery } from '@utils/query-dsl';
import { postApiCareLocationSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import MainCardContainer from '@components/main-container/main-card-container';
import { OrderState, StepProps } from '@features/order-create/types';
import useOrder from '@features/order-create/hooks/use-order';
import dayjs from 'dayjs';
import { filter, map } from 'lodash';
import NotificationBanner from '@components/notification-banner';
import SelectedCareLocation from '../selected-care-location';

type Location = {
	id: string;
	label: string;
	data: Array<Array<{ label?: string; value: string }>>;
};

type FormDataTypes = {
	location: Location | null;
};

const defaultValues: FormDataTypes = {
	location: null,
};

export default function CareLocation({ orderId, successCallback }: StepProps) {
	const [userSearchValue, setUserearchValue] = useState('');
	const [searchQuery, setSearchQuery] = useState<QueryDto<CareLocationQ> | undefined>();
	const [edit, setEdit] = useState(false);

	const { orderData, updateOrder } = useOrder({ orderId });

	const form = useAppForm({
		defaultValues,
		onSubmit: ({ value }) => {
			const { data } = orderData;
			const { _state } = data;
			const { flowState, stepState } = _state as unknown as OrderState;
			updateOrder(
				{
					...data,
					careLocationId: value.location?.id,
				},
				{
					flowState: {
						...flowState,
						status: 'InProgress',
						progress: 50,
						lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
					},
					stepState: {
						...stepState,
						AddCareLocation: 'Complete',
						AddPhysicians: 'InProgress',
					},
				},
				successCallback
			);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	const debouncedSearch = useMemo(
		() =>
			debounce((value: string) => {
				setSearchQuery((prevQ) => {
					if (prevQ?.$type === 'And') {
						return Query.and([
							...filter(
								prevQ.Exprs,
								(q) =>
									q.$type === 'Literal' &&
									(q as LiteralQuery<CareLocationQ>).Value.$type !== 'WithApproximateName'
							),
							Query.literal(CareLocationQuery.withApproximateName(value)),
						]);
					}
					return Query.literal(CareLocationQuery.withApproximateName(value));
				});
			}, 300),
		[]
	);

	const onSelect = (selectedValue: Location) => {
		if (selectedValue === values.location) {
			form.setFieldValue('location', null);
			return;
		}
		form.setFieldValue('location', selectedValue);
	};

	useEffect(() => {
		debouncedSearch(userSearchValue);
	}, [debouncedSearch, userSearchValue]);

	const {
		data: careLocations,
		isFetching: isCareLocationFetching,
		error,
	} = useQuery({
		...postApiCareLocationSearchOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			// @ts-expect-error Type Mismatch
			body: searchQuery,
		}),
		select: (data) =>
			map(data?.items, (item) => ({
				id: item.id,
				label: item.name,
				data: [
					map(item.contactDetail?.phoneNumbers, (phoneNumber) => ({
						label: 'Phone',
						value: phoneNumber.phoneNumber,
					})),
					map(item.contactDetail?.emails, (email) => ({
						label: 'Email',
						value: email.email,
					})),
					map(item.supportedEncounterTypes, (encounterType) => ({
						label: 'Encounter Type',
						value: encounterType.toString(),
					})),
					map(item.supportedStudyTypes, (studyType) => ({
						label: 'Study Type',
						value: studyType.toString(),
					})),
				],
			})) ?? [],
		initialData: {
			items: [],
		},
	});

	useEffect(() => {
		if (orderData?.data?.careLocationId) {
			setEdit(false);
			form.setFieldValue('location', {
				id: orderData.data.careLocationId,
				label: '',
				data: [],
			});
		}
	}, [orderData, form]);

	return (
		<Box
			component="form"
			p={2}
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			{orderData?.data?.careLocationId && !edit ? (
				<SelectedCareLocation
					careLocationId={orderData.data.careLocationId}
					onEdit={() => setEdit(true)}
				/>
			) : (
				<MainCardContainer
					title="Care Location"
					icon={<Hospital />}
					color="primary"
					emphasis={values.location ? 'high' : 'low'}
					containerSx={{ height: '100%', display: 'flex', flexDirection: 'column' }}
					sx={{ flex: 1, minHeight: 0, mb: -2 }}
					primaryActionType={orderData?.data?.careLocationId ? 'Delete' : 'none'}
					onPrimaryAction={() => setEdit(false)}
					descriptionSubheader="Select the care location where the study will be performed."
					descriptionText="The physician selection will be based on your location choice.">
					<Stack sx={{ height: '100%', minHeight: 0 }}>
						<Grid
							container
							spacing={2}>
							<Grid
								item
								md={2}>
								<TextField
									label="All Types"
									fullWidth
									disabled
								/>
							</Grid>
							<Grid
								item
								md={10}>
								<TextField
									value={userSearchValue}
									onChange={(e) => {
										setUserearchValue(e.target.value);
									}}
									variant="outlined"
									placeholder="Search locations by name or address.."
									fullWidth
									slotProps={{
										input: {
											startAdornment: (
												<InputAdornment position="start">
													<Search />
												</InputAdornment>
											),
										},
									}}
								/>
							</Grid>
						</Grid>
						<NotificationBanner
							message={error?.message}
							severity="error"
							scrollIntoView
						/>
						{isCareLocationFetching ? (
							<Box sx={{ py: 2, display: 'flex', justifyContent: 'center' }}>
								<CircularProgress />
							</Box>
						) : (
							<Stack
								spacing={2}
								sx={{ mt: 2, flex: 1, overflow: 'auto', minHeight: 0 }}>
								{Array.isArray(careLocations) &&
									careLocations?.map((item, index) => {
										const { label: title, data } = item;

										const checked = item?.id === values?.location?.id;

										const filteredData = filter(data, (item) => item.length > 0);

										return (
											<Box key={index.toString()}>
												<MainCardContainer
													title={title}
													color={checked ? 'primary' : 'gray'}
													emphasis={checked ? 'high' : 'low'}
													sx={{ minHeight: 'content' }}
													onClick={() => onSelect(item)}
													secondaryActionType="Selection"
													selectionSelected={checked}
													selectionLabel={checked ? 'Selected' : 'Select'}
													onSelectionChange={() => void onSelect(item)}>
													{filteredData.length > 0 ? (
														<Stack gap={1}>
															{data.map((d, dIndex) => (
																<ChipSummary
																	key={dIndex.toString()}
																	items={d.map(({ ...item }) => ({
																		label: item.label ? item.label : '',
																		value: item.value,
																	}))}
																	hideSeperator
																/>
															))}
														</Stack>
													) : (
														<Box sx={{ py: 2, display: 'flex', justifyContent: 'center' }}>
															No Data
														</Box>
													)}
												</MainCardContainer>
											</Box>
										);
									})}
							</Stack>
						)}
					</Stack>
				</MainCardContainer>
			)}
			<Box>
				<StepCardControl>
					<Button
						variant="outlined"
						color="primary">
						Save Draft
					</Button>
					<form.Subscribe>
						{({ isDirty, canSubmit, isSubmitting }) => (
							<Button
								variant="contained"
								color="primary"
								type="submit"
								loading={isSubmitting}
								disabled={!isDirty || !canSubmit}>
								Next
							</Button>
						)}
					</form.Subscribe>
				</StepCardControl>
			</Box>
		</Box>
	);
}
