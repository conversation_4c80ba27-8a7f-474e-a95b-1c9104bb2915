import { Patient<PERSON><PERSON>, PatientCreate } from '@auth/scopes';
import { getApiReferenceSetsOptions } from '@client/refdata/@tanstack/react-query.gen';
import { useAppForm } from '@hooks/app-form';
import { Card, CardContent, Typography, Button, Box } from '@mui/material';
import { useStore } from '@tanstack/react-form';
import { useQuery } from '@tanstack/react-query';

interface TestFormProps {
	onSubmit?: (values: { testSelect: string }) => void;
}

export default function TestForm({ onSubmit }: TestFormProps) {
	const form = useAppForm({
		defaultValues: {
			testSelect: '',
		},
		onSubmit: async ({ value }) => {
			onSubmit?.(value);
			console.log('Form submitted with values:', value);
		},
	});

	const { isDirty, canSubmit } = useStore(form.store, (state) => ({
		isDirty: state.isDirty,
		canSubmit: state.canSubmit,
	}));

	const { data: maritalStatusOptions } = useQuery(
		getApiReferenceSetsOptions({
			responseType: 'json',
		})
	);

	console.log('the marital status options are', maritalStatusOptions);

	return (
		<Card>
			<CardContent>
				<Typography
					variant="h6"
					gutterBottom>
					Test Form Component
				</Typography>

				<Box
					component="form"
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
					sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
					<form.AppField
						name="testSelect"
						children={(field) => (
							<field.AppSelectField
								label="Test Select Field"
								referenceDataSetName="namePrefix"
								required
							/>
						)}
					/>

					<Button
						type="submit"
						variant="contained"
						disabled={!isDirty || !canSubmit}
						sx={{ alignSelf: 'flex-start' }}>
						Submit
					</Button>
				</Box>
			</CardContent>
		</Card>
	);
}
