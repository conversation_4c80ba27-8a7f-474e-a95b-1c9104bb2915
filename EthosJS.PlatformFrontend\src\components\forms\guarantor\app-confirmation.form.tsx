import React, { CSSProperties, useState } from 'react';
import Card from '@components/card';
import CardInfo from '@components/card-info';

import { CircleAlert } from "lucide-react";
import ChipSummary from '@components/chip-summary';

import { Avatar, Chip, CardContent, useTheme, alpha, Typography, Stack, Checkbox, Box, Button } from '@mui/material';
import AddInfoCard from '@components/add-info-card';
import CardHeader from '@components/card-header';
import SectionHeader from '@components/section-header';
import StepCardControl from '@components/step-card-control';



const ConfirmationForm = () => {
    const { palette } = useTheme();
    const colorWithAlpha = alpha(palette.primary.main, 0.85);
    const [checked, setChecked] = useState(false);
    const handleChange = (event: any) => {
        setChecked(event.target.checked);
    };

    return (
        <Box p={2} display='flex' gap={2} flexDirection='column'>
            <Card>
                <Card sx={styles.borderBottomNone} >
                    <CardInfo title="1. Patient Information" />
                    <CardHeader
                        titleTypographyProps={{ variant: "h6", color: 'primary.dark' }}
                        title="<PERSON>"
                        action={
                            <ChipSummary
                                items={[
                                    { label: 'ID', value: '#34197564' },

                                ]}
                                variant="outlined"
                            />
                        }
                    />
                    <CardContent >
                        <ChipSummary
                            variant="outlined"
                            items={[
                                { label: 'DOB', value: '23/4/2002' },
                                { label: 'Primary Phone', value: '************' },
                                { label: 'Email', value: '<EMAIL>' },
                                { label: 'Address', value: 'aasdfg dfghj rtyui' },
                            ]}
                        />
                    </CardContent>
                </Card>
                <Card sx={styles.borderNone}>
                    <CardInfo title="1. Study Details" />
                    <CardHeader
                        titleTypographyProps={{ variant: "h6", color: 'primary.dark' }}
                        title="PSG (Polysomnography)"
                        action={
                            <ChipSummary
                                items={[
                                    { label: 'ID', value: 'In-Lab' },
                                    { label: 'CPT Code', value: '34197564' },

                                ]}
                                variant="outlined"

                            />
                        }
                    />
                    <CardContent >
                        <Typography variant="h6" color="primary.dark" sx={{ mb: 1 }}>Study Attributes</Typography>
                        <ChipSummary
                            variant="outlined"
                            items={[
                                { label: '', value: 'Extended EEG' },
                                { label: '', value: 'Video Recording' },
                                { label: '', value: 'ETCO2 Monitoring' },
                            ]}
                            hideSeperator
                        />
                    </CardContent>
                </Card>
                <Card sx={styles.borderNone}>
                    <CardInfo title="1. Location" />
                    <CardHeader
                        titleTypographyProps={{ variant: "h6", color: 'primary.dark' }}
                        title="Northwest Sleep Center"
                        action={
                            <ChipSummary
                                items={[
                                    { label: '', value: 'Clinic' },


                                ]}
                                variant="outlined"
                                hideSeperator

                            />
                        }
                    />
                    <CardContent >

                        <ChipSummary
                            variant="outlined"
                            items={[
                                { label: 'Phone', value: '************' },
                                { label: 'Address', value: 'aasdfg dfghj rtyui' },

                            ]}

                        />
                    </CardContent>
                </Card>

                <Card sx={styles.borderNone}>
                    <CardInfo title="1. Physician" />

                    <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start">

                            <Box>
                                <Typography variant="h5" color="primary.dark" >Primary Physician</Typography>
                                <Typography variant="h6" color="primary.dark" >
                                    Dr. Miacheal John
                                </Typography>
                            </Box>


                            <ChipSummary
                                variant="outlined"
                                items={[
                                    { label: 'Phone', value: '************' },
                                    { label: 'Address', value: 'aasdfg dfghj rtyui' },
                                ]}
                            />
                        </Box>


                    </CardContent>
                    <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start">

                            <Box>
                                <Typography variant="h5" color="primary.dark" >Primary Physician</Typography>
                                <Typography variant="h6" color="primary.dark" >
                                    Dr. Miacheal John
                                </Typography>
                            </Box>


                            <ChipSummary
                                variant="outlined"
                                items={[
                                    { label: 'Phone', value: '************' },
                                    { label: 'Address', value: 'aasdfg dfghj rtyui' },
                                ]}
                            />
                        </Box>


                    </CardContent>
                    <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start">

                            <Box>
                                <Typography variant="h5" color="primary.dark" >Primary Physician</Typography>
                                <Typography variant="h6" color="primary.dark" >
                                    Dr. Miacheal John
                                </Typography>
                            </Box>


                            <ChipSummary
                                variant="outlined"
                                items={[
                                    { label: 'Phone', value: '************' },
                                    { label: 'Address', value: 'aasdfg dfghj rtyui' },
                                ]}
                            />
                        </Box>


                    </CardContent>
                </Card>

                <Card sx={styles.borderTopNone}>
                    <CardInfo title="1. Insurance Information" />

                    <CardContent >
                        <Typography variant="h6" color="primary.dark" sx={{ mb: 1 }}>Blue Cross Blue Shield</Typography>

                        <ChipSummary
                            variant="outlined"
                            items={[
                                { label: '', value: 'Extended EEG' },
                                { label: 'member ID', value: 'Video Recording' },
                                { label: '', value: 'ETCO2 Monitoring' },
                            ]}

                        />
                    </CardContent>
                    <CardContent >
                        <Typography variant="h6" color="primary.dark" sx={{ mb: 1 }}>UMR</Typography>

                        <ChipSummary
                            variant="outlined"
                            items={[
                                { label: '', value: 'Extended EEG' },
                                { label: 'member ID', value: 'Video Recording' },
                                { label: '', value: 'ETCO2 Monitoring' },
                            ]}

                        />
                    </CardContent>
                </Card>
            </Card>
            <Card>
                <CardHeader title='Confirmation Checklist' />
                <CardContent>
                    <Stack flexDirection='row' justifyContent='space-between'>
                        <Typography variant="h6" color="primary.dark" sx={{ mb: 1 }}> I have reviewed the Patient information and confirm that it is accurate.</Typography>


                        <Checkbox
                            checked={checked}
                            onChange={handleChange}
                        />
                    </Stack>
                </CardContent>
            </Card>
            <Box >
                <StepCardControl>
                    <Button
                        variant="outlined"
                        color="primary"
                    >
                        Save Draft
                    </Button>

                    <Button
                        variant="contained"
                        color="primary"
                        type="submit"

                    >
                        Next
                    </Button>


                </StepCardControl>
            </Box>


        </Box>
    )



};

const styles: Record<'borderBottomNone' | 'borderTopNone' | 'borderNone', CSSProperties> = {
    borderBottomNone: {
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
    },
    borderTopNone: {
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0,
    },
    borderNone: {
        borderRadius: 0
    }
}

export default ConfirmationForm;
