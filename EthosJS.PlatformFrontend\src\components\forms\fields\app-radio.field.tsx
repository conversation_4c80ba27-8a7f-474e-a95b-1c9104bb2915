import { useFieldContext } from '@hooks/form-context';
import { FieldPropType } from './FieldPropType';
import {
	List,
	ListItem,
	Radio as MuiRadio,
	Checkbox as MuiCheckbox,
	ListItemText,
} from '@mui/material';

type OptionTypes = {
	title: string;
	description: string;
	value: string | number;
};

interface AppRadioFieldProps extends FieldPropType {
	options: Array<OptionTypes>;
	isMulti?: boolean;
	onValueChange?: (value: string | string[]) => void;
}
export default function AppRadioField({
	options,
	isMulti = false,
	onValueChange,
}: AppRadioFieldProps) {
	const field = useFieldContext<string | string[]>();
	const onSelect = (selectedItem: OptionTypes) => {
		if (isMulti) {
			const currentSelected = Array.isArray(field.state.value) ? field.state.value : [];
			const newSelected = currentSelected.includes(String(selectedItem.value))
				? currentSelected.filter((value) => value !== String(selectedItem.value))
				: [...currentSelected, String(selectedItem.value)];

			field.setValue(newSelected);
			onValueChange?.(newSelected);
		} else {
			const newValue = String(selectedItem.value);
			field.setValue(newValue);
			onValueChange?.(newValue);
		}
	};

	const isItemSelected = (value: OptionTypes['value']) => {
		console.log('isItemSelected', value, field.state.value);
		if (isMulti) {
			return Array.isArray(field.state.value) && field.state.value.includes(String(value));
		}
		return String(field.state.value) === String(value);
	};

	return (
		<List disablePadding>
			{options?.map((option, index) => {
				const isSelected = isItemSelected(option.value);
				return (
					<ListItem
						key={index}
						onClick={() => onSelect(option)}
						sx={{
							padding: 0,
							':last-child': {
								borderBottom: 'none',
							},
						}}
						divider
						secondaryAction={
							isMulti ? <MuiCheckbox checked={isSelected} /> : <MuiRadio checked={isSelected} />
						}>
						<ListItemText
							primary={option.title}
							secondary={option.description}
							slotProps={{
								primary: {
									variant: 'h6',
									color: 'primary.main',
									fontFamily: 'Roboto',
								},
								secondary: {
									variant: 'body2',
									color: 'primary.main',
									fontFamily: 'Roboto',
								},
							}}
						/>
					</ListItem>
				);
			})}
		</List>
	);
}
