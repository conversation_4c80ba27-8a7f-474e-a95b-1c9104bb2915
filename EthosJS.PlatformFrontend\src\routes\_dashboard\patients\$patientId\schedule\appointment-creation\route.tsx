import { Stack } from '@mui/material';
import { createFileRoute, Outlet } from '@tanstack/react-router';
import { useState } from 'react';
import ContextSelection from '../../../../../../features/scheduling/components/context-selection';

const getTabFromPath = (path: string): string => {
	const segments = path.split('/');
	const lastSegment = segments[segments.length - 1];

	const tabValues = TAB_ITEM_S.map((tab) => tab.value);
	if (tabValues.includes(lastSegment)) {
		return lastSegment;
	}

	return 'dashboard';
};

const TAB_ITEM_S = [
	{
		name: 'Dashboard',
		value: 'dashboard',
	},
	{
		name: 'Notes',
		value: 'notes',
	},
	{
		name: 'Notifications',
		value: 'notifications',
	},
	{
		name: 'Files',
		value: 'files',
	},
];

type TabValueType = (typeof TAB_ITEM_S)[number]['value'];

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/schedule/appointment-creation'
)({
	component: RouteComponent,
	loader: ({ location }) => {
		const selectedTab = getTabFromPath(location.pathname);
		return { selectedTab };
	},
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const navigate = Route.useNavigate();
	const { selectedTab } = Route.useLoaderData() as { selectedTab: TabValueType };
	const [selected, setSelected] = useState<TabValueType>(selectedTab);

	const onTabChange = (value: string) => {
		const search = { orderId, studyId };
		navigate({
			to: `/patients/$patientId/schedule/appointment-creation/${value}`,
			params: { patientId },
			search,
		});
		setSelected(value as TabValueType);
	};

	return (
		<Stack gap={2}>
			<ContextSelection
				items={TAB_ITEM_S}
				onChange={(e) => onTabChange(e.value)}
				selected={selected}
			/>
			<Outlet />
		</Stack>
	);
}
