import MenuItem from '@components/menu/menu-item';
import { User } from 'lucide-react';
import usePatient from '../hooks/use-patient';
import { Status } from '@config/status';
import { PatientState } from '../types';

export default function PatientInformationMenuItem({
	patientId,
	selected,
	onClick,
}: {
	patientId: string;
	selected: boolean;
	onClick: () => void;
}) {
	const { patientData } = usePatient({ patientId });

	const { data } = patientData ?? {};
	const patientState = (data?._state as unknown as PatientState) ?? {};
	const { flowState } = patientState ?? {};

	return (
		<MenuItem
			title="Patient Information"
			value="patient-information"
			selected={selected}
			icon={User}
			onClick={onClick}
			status={
				flowState?.status === 'Complete'
					? Status.Success
					: flowState?.status === 'InProgress'
						? Status.Process
						: Status.NotStarted
			}
			size="large"
		/>
	);
}
