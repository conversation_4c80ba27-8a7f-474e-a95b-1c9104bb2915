import { Autocomplete, Box, Button, Card, Menu, MenuItem, Paper, Tab, Tabs, TextField } from '@mui/material'
import { createFileRoute } from '@tanstack/react-router'
import StatsCard from '@components/stats-card'
import { ClosedCaption, ExpandMore, Schedule, Timeline } from '@mui/icons-material'
import { useState } from 'react'
import { DataGridPro } from '@mui/x-data-grid-pro'
import { useSuspenseQuery } from '@tanstack/react-query'
import PageContainer from '@components/page-container'

export const Route = createFileRoute('/_dashboard/schedule/')({
  component: RouteComponent,
});

function RouteComponent() {

  const { data = [] } = useSuspenseQuery({ queryKey: ['schedule'] })

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedItem, setSelectedItem] = useState<string>("All");
  const open = Boolean(anchorEl);

  const [value, setValue] = useState(2);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleSelect = (item: string) => {
    setSelectedItem(item);
    handleClose();
  }
  return (
    <PageContainer title="Schedule" icon={Schedule}>
      <Box sx={{ display: 'flex', gap: 2, mb: "25px" }}>
        <StatsCard
          title="Today's Schedule"
          mainContent="1,248"
          subContent="+ 12"
          icon={Schedule}
          iconType='success'
          type="success"
        />
        <StatsCard
          title="Upcoming Appointments"
          mainContent="5"
          subContent="+ 2"
          icon={Timeline}
          iconType='info'
          type="info"
        />
        <StatsCard
          title="Total Appointments"
          mainContent="1,248"
          subContent="+ 12"
          icon={ClosedCaption}
          iconType='warning'
          type="warning"
        />
        <StatsCard
          title="Total Appointments"
          mainContent="1,248"
          subContent="+ 12"
          icon={ClosedCaption}
          iconType='warning'
          type="warning"
        />
      </Box>
      <Card component={Paper} sx={{ p: 2, mb: 2 }}>
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'space-between', mb: '25px' }}>
          <Box sx={{ display: 'flex', gap: 2, flex: 1 }}>
            <Button
              aria-controls={open ? 'basic-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={open ? 'true' : undefined}
              onClick={handleClick}
              variant='contained'
              endIcon={
                <ExpandMore />
              }
            >
              {selectedItem}
            </Button>
            <Menu
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              MenuListProps={{
                'aria-labelledby': 'basic-button',
              }}
            >
              <MenuItem onClick={() => handleSelect("All")}>All</MenuItem>
              <MenuItem onClick={() => handleSelect("Upcoming")}>Upcoming</MenuItem>
              <MenuItem onClick={() => handleSelect("Past")}>Past</MenuItem>
            </Menu>
            <Autocomplete disablePortal sx={{ minWidth: 200 }} renderInput={(params) => <TextField {...params} label="Search..." />} options={[]} />
          </Box>
          <Box>
            <Tabs value={value} onChange={handleChange} aria-label="tabs">
              <Tab label="All" />
              <Tab label="Pending Review" />
              <Tab label="In Progress" />
              <Tab label="Completed" />
            </Tabs>
          </Box>
        </Box>
        <Box sx={{ minHeight: 400, maxHeight: 600, width: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* <DataGridPro rows={data} initialState={{
            pagination: {
              paginationModel: {
                page: 1,
                pageSize: 10,
              }
            }
          }} columns={columns} pagination /> */}
        </Box>
      </Card>
    </PageContainer>
  )
}
