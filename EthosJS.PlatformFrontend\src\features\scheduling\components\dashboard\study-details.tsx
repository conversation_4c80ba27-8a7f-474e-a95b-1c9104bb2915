import ChipSummary from '@components/chip-summary';
import CollapsibleMainCardContainer from '@components/collapsible-main-card-container';
import MainCardContainer from '@components/main-container/main-card-container';
import { Stack } from '@mui/material';
import { MapPin } from 'lucide-react';
import { MonitorHeartOutlined } from '@mui/icons-material';
import useStudyDetails from '@features/scheduling/hooks/use-study-details';

export default function StudyDetails({ studyId }: { studyId: string }) {
	// const { studyData, isFetchingStudyData, fetchStudyError } = useStudyDetails({
	// 	studyId,
	// });
	return (
		<CollapsibleMainCardContainer
			mainContainerProps={{
				title: 'Study Details',
				icon: <MonitorHeartOutlined />,
			}}
			defaultCollapse>
			<Stack gap={2}>
				<MainCardContainer
					color="gray"
					emphasis="low"
					icon={<MonitorHeartOutlined />}
					title="Study Information">
					<ChipSummary
						items={[
							{
								label: 'Provider',
								value: 'Dr. <PERSON>',
							},
							{
								label: 'Encounter Type',
								value: 'Sleep',
							},
							{
								label: 'Study Type',
								value: 'PSG (Polysomnography)',
							},
						]}
					/>
				</MainCardContainer>
				<MainCardContainer
					color="gray"
					emphasis="low"
					icon={<MonitorHeartOutlined />}
					title="Study Parameters">
					<ChipSummary
						hideSeperator
						items={[
							{
								value: 'ETCO2 Monitoring',
							},
							{
								value: 'Extended EEG',
							},
							{
								value: 'Parasomnia Assessment',
							},
							{
								value: 'Wheelchair accessible',
							},
							{
								value: 'Female technician preferred',
							},
						]}
					/>
				</MainCardContainer>
				<MainCardContainer
					color="gray"
					emphasis="low"
					icon={<MapPin />}
					title="Location Details">
					<ChipSummary
						items={[
							{
								label: 'Care Location',
								value: 'Northwest Sleep Center',
							},
							{
								label: 'Phone',
								value: '(*************',
							},
							{
								label: 'Fax',
								value: '(*************',
							},
							{
								label: 'Address',
								value: '1708 S Yakima Avenue, Ste 105 Tacoma WA 98405-5307',
							},
						]}
					/>
				</MainCardContainer>
			</Stack>
		</CollapsibleMainCardContainer>
	);
}
