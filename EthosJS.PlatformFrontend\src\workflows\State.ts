import { IState } from './TypeDef';

export class State {
  name: string;
  description: string | null;
  isDeprecated: boolean;
  isFinal: boolean;
  data: string; // type name

  constructor({ name, description, is_deprecated = false, is_final = false, data }: IState) {
    this.name = name;
    this.description = description;
    this.isDeprecated = is_deprecated;
    this.isFinal = is_final;
    this.data = data;
  }
}
