import { useAppForm } from '@hooks/app-form';
import { EmailOutlined } from '@mui/icons-material';
import { Grid2 as Grid, Box, Checkbox, FormLabel, FormHelperText } from '@mui/material';
import { useStore } from '@tanstack/react-form';
import ContactMethodContainer from '@components/contact-method-container';
import MainCardContainer, {
	MainCardContainerProps,
} from '@components/main-container/main-card-container';
import { EthosWorkflowsApiPersonalEmailDto } from '@client/workflows';
import { ValidationErrors } from '@app-types/validation';
import { useMemo } from 'react';
import { defaultValues, emailFormOptions } from './utils';
import { formHasErrors, formHasValues } from '../utils';

interface EmailFormProps {
	onAdd?: (values: EthosWorkflowsApiPersonalEmailDto) => void;
	onCancel?: (shouldRemove: boolean) => void;
	onDelete?: () => void;
	formValues?: EthosWorkflowsApiPersonalEmailDto;
	onValidate: (data: EthosWorkflowsApiPersonalEmailDto) => Promise<ValidationErrors | undefined>;
	isUpdate?: boolean;
	containerProps?: MainCardContainerProps;
}

export default function EmailForm({
	onAdd,
	onCancel,
	onDelete,
	formValues,
	onValidate,
	isUpdate,
	containerProps = {},
}: EmailFormProps) {
	const options = useMemo(() => emailFormOptions(formValues), [formValues]);

	const hasValues = formHasValues(formValues, defaultValues);

	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(value);
				return res;
			},
		},
		defaultState: {
			isDirty: hasValues,
			isPristine: !hasValues,
		},
	});

	const { values, canSubmit, isDirty, fieldMeta } = useStore(
		form.store,
		({ values, canSubmit, isDirty, fieldMeta }) => ({
			values,
			canSubmit,
			isDirty,
			fieldMeta,
		})
	);

	const hasErrors = formHasErrors(fieldMeta);

	return (
		<MainCardContainer
			title="Add Email Address"
			icon={<EmailOutlined fontSize="large" />}
			color={hasErrors ? 'error' : 'primary'}
			emphasis={hasErrors || hasValues ? 'high' : 'low'}
			descriptionSubheader="* Indicates a required field"
			descriptionText="Add an email address to contact the patient."
			primaryActionType="none"
			footerProps={{
				primaryButton1: {
					label: hasValues || isUpdate ? 'Edit' : 'Add',
					onClick: () => onAdd?.(values),
					disabled: !canSubmit || !isDirty,
					'data-testid': 'email.submitButton',
				},
				primaryButton2: {
					label: 'Cancel',
					onClick: () => {
						form.reset();
						onCancel?.(hasValues);
					},
					'data-testid': 'email.cancelButton',
				},
				secondaryButton1: hasValues
					? {
							label: 'Delete',
							onClick: onDelete,
							color: 'error',
							'data-testid': 'email.deleteButton',
						}
					: undefined,
			}}
			containerSlot={
				<ContactMethodContainer>
					<form.AppField name="isPreferred">
						{({ state, handleChange }) => (
							<Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
								<Checkbox
									checked={state.value}
									onChange={(e) => handleChange(e.target.checked)}
									data-testid="email.isPreferred"
								/>
								<Box sx={{ display: 'flex', flexDirection: 'column', color: 'primary.dark' }}>
									<FormLabel sx={{ textTransform: 'uppercase', color: 'inherit' }}>
										Contact Method
									</FormLabel>
									<FormHelperText sx={{ color: 'inherit' }}>
										This email address is the patient's preferred primary contact method.
									</FormHelperText>
								</Box>
							</Box>
						)}
					</form.AppField>
				</ContactMethodContainer>
			}
			{...containerProps}>
			<Grid
				container
				columnSpacing={2.5}>
				<Grid size={3}>
					<form.AppField name="use">
						{(field) => (
							<field.AppSelectField
								label="Type"
								referenceDataSetName="emailUse"
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={9}>
					<form.AppField
						name="value"
						children={(field) => <field.AppEmailField label="Email Address" />}
					/>
				</Grid>
			</Grid>
		</MainCardContainer>
	);
}
