// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, TDataShape, Client } from './client';
import type { GetApiAddNewOrderValidationRulesData, GetApiAddNewOrderValidationRulesResponses, GetApiAddNewOrderStateByIdData, GetApiAddNewOrderStateByIdResponses, PostApiAddNewOrderStartData, PostApiAddNewOrderStartResponses, PostApiAddNewOrderRewindData, PostApiAddNewOrderRewindResponses, PostApiAddNewOrderListData, PostApiAddNewOrderListResponses, PostApiAddNewOrderAddStudyData, PostApiAddNewOrderAddStudyResponses, PostApiAddNewOrderAddStudyDraftData, PostApiAddNewOrderAddStudyDraftResponses, PostApiAddNewOrderAddStudyValidateData, PostApiAddNewOrderAddStudyValidateResponses, PostApiAddNewOrderAddCareLocationData, PostApiAddNewOrderAddCareLocationResponses, PostApiAddNewOrderAddCareLocationDraftData, PostApiAddNewOrderAddCareLocationDraftResponses, PostApiAddNewOrderAddCareLocationValidateData, PostApiAddNewOrderAddCareLocationValidateResponses, PostApiAddNewOrderAddPhysiciansData, PostApiAddNewOrderAddPhysiciansResponses, PostApiAddNewOrderAddPhysiciansDraftData, PostApiAddNewOrderAddPhysiciansDraftResponses, PostApiAddNewOrderAddPhysiciansValidateData, PostApiAddNewOrderAddPhysiciansValidateResponses, PostApiAddNewOrderReviewAndSubmitOrderData, PostApiAddNewOrderReviewAndSubmitOrderResponses, PostApiAddNewOrderReviewAndSubmitOrderDraftData, PostApiAddNewOrderReviewAndSubmitOrderDraftResponses, PostApiAddNewOrderReviewAndSubmitOrderValidateData, PostApiAddNewOrderReviewAndSubmitOrderValidateResponses, GetApiAddNewPatientValidationRulesData, GetApiAddNewPatientValidationRulesResponses, GetApiAddNewPatientStateByIdData, GetApiAddNewPatientStateByIdResponses, PostApiAddNewPatientStartData, PostApiAddNewPatientStartResponses, PostApiAddNewPatientRewindData, PostApiAddNewPatientRewindResponses, PostApiAddNewPatientListData, PostApiAddNewPatientListResponses, PostApiAddNewPatientAddBasicInformationData, PostApiAddNewPatientAddBasicInformationResponses, PostApiAddNewPatientAddBasicInformationDraftData, PostApiAddNewPatientAddBasicInformationDraftResponses, PostApiAddNewPatientAddBasicInformationValidateData, PostApiAddNewPatientAddBasicInformationValidateResponses, PostApiAddNewPatientAddContactsData, PostApiAddNewPatientAddContactsResponses, PostApiAddNewPatientAddContactsDraftData, PostApiAddNewPatientAddContactsDraftResponses, PostApiAddNewPatientAddContactsValidateData, PostApiAddNewPatientAddContactsValidateResponses, PostApiAddNewPatientAddAddressesData, PostApiAddNewPatientAddAddressesResponses, PostApiAddNewPatientAddAddressesDraftData, PostApiAddNewPatientAddAddressesDraftResponses, PostApiAddNewPatientAddAddressesValidateData, PostApiAddNewPatientAddAddressesValidateResponses, PostApiAddNewPatientAddInsurancesData, PostApiAddNewPatientAddInsurancesResponses, PostApiAddNewPatientAddInsurancesDraftData, PostApiAddNewPatientAddInsurancesDraftResponses, PostApiAddNewPatientAddInsurancesValidateData, PostApiAddNewPatientAddInsurancesValidateResponses, PostApiAddNewPatientAddGuardiansData, PostApiAddNewPatientAddGuardiansResponses, PostApiAddNewPatientAddGuardiansDraftData, PostApiAddNewPatientAddGuardiansDraftResponses, PostApiAddNewPatientAddGuardiansValidateData, PostApiAddNewPatientAddGuardiansValidateResponses, PostApiAddNewPatientAddClinicalInformationData, PostApiAddNewPatientAddClinicalInformationResponses, PostApiAddNewPatientAddClinicalInformationDraftData, PostApiAddNewPatientAddClinicalInformationDraftResponses, PostApiAddNewPatientAddClinicalInformationValidateData, PostApiAddNewPatientAddClinicalInformationValidateResponses, GetApiCareLocationByIdData, GetApiCareLocationByIdResponses, GetApiCareLocationByIdErrors, PatchApiCareLocationByIdData, PatchApiCareLocationByIdResponses, PatchApiCareLocationByIdErrors, PutApiCareLocationByIdData, PutApiCareLocationByIdResponses, PutApiCareLocationByIdErrors, PostApiCareLocationSearchData, PostApiCareLocationSearchResponses, GetApiCareLocationData, GetApiCareLocationResponses, PostApiCareLocationData, PostApiCareLocationResponses, PostApiCareLocationErrors, PostApiCareLocationDraftData, PostApiCareLocationDraftResponses, PostApiCareLocationDraftErrors, GetApiCareLocationDraftByEntityIdData, GetApiCareLocationDraftByEntityIdResponses, GetApiCareLocationDraftByEntityIdErrors, PutApiCareLocationDraftByEntityIdData, PutApiCareLocationDraftByEntityIdResponses, PutApiCareLocationDraftByEntityIdErrors, PostApiCareLocationDraftByEntityIdCommitData, PostApiCareLocationDraftByEntityIdCommitResponses, PostApiCareLocationDraftByEntityIdCommitErrors, PostApiCareLocationDraftValidateData, PostApiCareLocationDraftValidateResponses, PostApiCareLocationDraftValidateErrors, PostApiCareLocationDraftByEntityIdValidateData, PostApiCareLocationDraftByEntityIdValidateResponses, PostApiCareLocationDraftByEntityIdValidateErrors, GetApiDraftByIdData, GetApiDraftByIdResponses, GetApiDraftByIdErrors, PatchApiDraftByIdData, PatchApiDraftByIdResponses, PatchApiDraftByIdErrors, PutApiDraftByIdData, PutApiDraftByIdResponses, PutApiDraftByIdErrors, PostApiDraftSearchData, PostApiDraftSearchResponses, GetApiDraftData, GetApiDraftResponses, PostApiDraftData, PostApiDraftResponses, PostApiDraftErrors, PostApiDraftDraftData, PostApiDraftDraftResponses, PostApiDraftDraftErrors, GetApiDraftDraftByEntityIdData, GetApiDraftDraftByEntityIdResponses, GetApiDraftDraftByEntityIdErrors, PutApiDraftDraftByEntityIdData, PutApiDraftDraftByEntityIdResponses, PutApiDraftDraftByEntityIdErrors, PostApiDraftDraftByEntityIdCommitData, PostApiDraftDraftByEntityIdCommitResponses, PostApiDraftDraftByEntityIdCommitErrors, PostApiDraftDraftValidateData, PostApiDraftDraftValidateResponses, PostApiDraftDraftValidateErrors, PostApiDraftDraftByEntityIdValidateData, PostApiDraftDraftByEntityIdValidateResponses, PostApiDraftDraftByEntityIdValidateErrors, GetApiEquipmentByIdData, GetApiEquipmentByIdResponses, GetApiEquipmentByIdErrors, PatchApiEquipmentByIdData, PatchApiEquipmentByIdResponses, PatchApiEquipmentByIdErrors, PutApiEquipmentByIdData, PutApiEquipmentByIdResponses, PutApiEquipmentByIdErrors, PostApiEquipmentSearchData, PostApiEquipmentSearchResponses, GetApiEquipmentData, GetApiEquipmentResponses, PostApiEquipmentData, PostApiEquipmentResponses, PostApiEquipmentErrors, PostApiEquipmentDraftData, PostApiEquipmentDraftResponses, PostApiEquipmentDraftErrors, GetApiEquipmentDraftByEntityIdData, GetApiEquipmentDraftByEntityIdResponses, GetApiEquipmentDraftByEntityIdErrors, PutApiEquipmentDraftByEntityIdData, PutApiEquipmentDraftByEntityIdResponses, PutApiEquipmentDraftByEntityIdErrors, PostApiEquipmentDraftByEntityIdCommitData, PostApiEquipmentDraftByEntityIdCommitResponses, PostApiEquipmentDraftByEntityIdCommitErrors, PostApiEquipmentDraftValidateData, PostApiEquipmentDraftValidateResponses, PostApiEquipmentDraftValidateErrors, PostApiEquipmentDraftByEntityIdValidateData, PostApiEquipmentDraftByEntityIdValidateResponses, PostApiEquipmentDraftByEntityIdValidateErrors, PostApiFileRequestUploadTokenData, PostApiFileRequestUploadTokenResponses, PostApiFileRequestUploadTokenErrors, PostApiFileUploadData, PostApiFileUploadResponses, PostApiFileUploadErrors, GetApiFileStatusByFileIdData, GetApiFileStatusByFileIdResponses, GetApiFileStatusByFileIdErrors, GetApiHealthData, GetApiHealthResponses, GetApiInsuranceByIdData, GetApiInsuranceByIdResponses, GetApiInsuranceByIdErrors, PatchApiInsuranceByIdData, PatchApiInsuranceByIdResponses, PatchApiInsuranceByIdErrors, PutApiInsuranceByIdData, PutApiInsuranceByIdResponses, PutApiInsuranceByIdErrors, PostApiInsuranceSearchData, PostApiInsuranceSearchResponses, GetApiInsuranceData, GetApiInsuranceResponses, PostApiInsuranceData, PostApiInsuranceResponses, PostApiInsuranceErrors, PostApiInsuranceDraftData, PostApiInsuranceDraftResponses, PostApiInsuranceDraftErrors, GetApiInsuranceDraftByEntityIdData, GetApiInsuranceDraftByEntityIdResponses, GetApiInsuranceDraftByEntityIdErrors, PutApiInsuranceDraftByEntityIdData, PutApiInsuranceDraftByEntityIdResponses, PutApiInsuranceDraftByEntityIdErrors, PostApiInsuranceDraftByEntityIdCommitData, PostApiInsuranceDraftByEntityIdCommitResponses, PostApiInsuranceDraftByEntityIdCommitErrors, PostApiInsuranceDraftValidateData, PostApiInsuranceDraftValidateResponses, PostApiInsuranceDraftValidateErrors, PostApiInsuranceDraftByEntityIdValidateData, PostApiInsuranceDraftByEntityIdValidateResponses, PostApiInsuranceDraftByEntityIdValidateErrors, PostApiInsuranceVerificationStartData, PostApiInsuranceVerificationStartResponses, PostApiInsuranceVerificationStartErrors, GetApiInsuranceVerificationStatusByJobIdData, GetApiInsuranceVerificationStatusByJobIdResponses, GetApiInsuranceVerificationStatusByJobIdErrors, PostApiInsuranceVerificationSearchData, PostApiInsuranceVerificationSearchResponses, PostApiInsuranceVerificationSearchErrors, PostApiInsuranceVerificationFastauthWebhookData, PostApiInsuranceVerificationFastauthWebhookResponses, PostApiInsuranceVerificationFastauthWebhookErrors, PostApiLoginData, PostApiLoginResponses, GetApiLoginCheckData, GetApiLoginCheckResponses, PostApiMockDbDeleteData, PostApiMockDbDeleteResponses, GetApiMockDbResetData, GetApiMockDbResetResponses, GetApiNoteByIdData, GetApiNoteByIdResponses, GetApiNoteByIdErrors, PatchApiNoteByIdData, PatchApiNoteByIdResponses, PatchApiNoteByIdErrors, PutApiNoteByIdData, PutApiNoteByIdResponses, PutApiNoteByIdErrors, PostApiNoteSearchData, PostApiNoteSearchResponses, GetApiNoteData, GetApiNoteResponses, PostApiNoteData, PostApiNoteResponses, PostApiNoteErrors, PostApiNoteDraftData, PostApiNoteDraftResponses, PostApiNoteDraftErrors, GetApiNoteDraftByEntityIdData, GetApiNoteDraftByEntityIdResponses, GetApiNoteDraftByEntityIdErrors, PutApiNoteDraftByEntityIdData, PutApiNoteDraftByEntityIdResponses, PutApiNoteDraftByEntityIdErrors, PostApiNoteDraftByEntityIdCommitData, PostApiNoteDraftByEntityIdCommitResponses, PostApiNoteDraftByEntityIdCommitErrors, PostApiNoteDraftValidateData, PostApiNoteDraftValidateResponses, PostApiNoteDraftValidateErrors, PostApiNoteDraftByEntityIdValidateData, PostApiNoteDraftByEntityIdValidateResponses, PostApiNoteDraftByEntityIdValidateErrors, GetApiOrderByIdData, GetApiOrderByIdResponses, GetApiOrderByIdErrors, PatchApiOrderByIdData, PatchApiOrderByIdResponses, PatchApiOrderByIdErrors, PutApiOrderByIdData, PutApiOrderByIdResponses, PutApiOrderByIdErrors, PostApiOrderSearchData, PostApiOrderSearchResponses, GetApiOrderData, GetApiOrderResponses, PostApiOrderData, PostApiOrderResponses, PostApiOrderErrors, PostApiOrderDraftData, PostApiOrderDraftResponses, PostApiOrderDraftErrors, GetApiOrderDraftByEntityIdData, GetApiOrderDraftByEntityIdResponses, GetApiOrderDraftByEntityIdErrors, PutApiOrderDraftByEntityIdData, PutApiOrderDraftByEntityIdResponses, PutApiOrderDraftByEntityIdErrors, PostApiOrderDraftByEntityIdCommitData, PostApiOrderDraftByEntityIdCommitResponses, PostApiOrderDraftByEntityIdCommitErrors, PostApiOrderDraftValidateData, PostApiOrderDraftValidateResponses, PostApiOrderDraftValidateErrors, PostApiOrderDraftByEntityIdValidateData, PostApiOrderDraftByEntityIdValidateResponses, PostApiOrderDraftByEntityIdValidateErrors, GetApiPatientByIdData, GetApiPatientByIdResponses, GetApiPatientByIdErrors, PatchApiPatientByIdData, PatchApiPatientByIdResponses, PatchApiPatientByIdErrors, PutApiPatientByIdData, PutApiPatientByIdResponses, PutApiPatientByIdErrors, PostApiPatientSearchData, PostApiPatientSearchResponses, GetApiPatientData, GetApiPatientResponses, PostApiPatientData, PostApiPatientResponses, PostApiPatientErrors, PostApiPatientDraftData, PostApiPatientDraftResponses, PostApiPatientDraftErrors, GetApiPatientDraftByEntityIdData, GetApiPatientDraftByEntityIdResponses, GetApiPatientDraftByEntityIdErrors, PutApiPatientDraftByEntityIdData, PutApiPatientDraftByEntityIdResponses, PutApiPatientDraftByEntityIdErrors, PostApiPatientDraftByEntityIdCommitData, PostApiPatientDraftByEntityIdCommitResponses, PostApiPatientDraftByEntityIdCommitErrors, PostApiPatientDraftValidateData, PostApiPatientDraftValidateResponses, PostApiPatientDraftValidateErrors, PostApiPatientDraftByEntityIdValidateData, PostApiPatientDraftByEntityIdValidateResponses, PostApiPatientDraftByEntityIdValidateErrors, GetApiPatientAppointmentByIdData, GetApiPatientAppointmentByIdResponses, GetApiPatientAppointmentByIdErrors, PatchApiPatientAppointmentByIdData, PatchApiPatientAppointmentByIdResponses, PatchApiPatientAppointmentByIdErrors, PutApiPatientAppointmentByIdData, PutApiPatientAppointmentByIdResponses, PutApiPatientAppointmentByIdErrors, PostApiPatientAppointmentSearchData, PostApiPatientAppointmentSearchResponses, GetApiPatientAppointmentData, GetApiPatientAppointmentResponses, PostApiPatientAppointmentData, PostApiPatientAppointmentResponses, PostApiPatientAppointmentErrors, PostApiPatientAppointmentDraftData, PostApiPatientAppointmentDraftResponses, PostApiPatientAppointmentDraftErrors, GetApiPatientAppointmentDraftByEntityIdData, GetApiPatientAppointmentDraftByEntityIdResponses, GetApiPatientAppointmentDraftByEntityIdErrors, PutApiPatientAppointmentDraftByEntityIdData, PutApiPatientAppointmentDraftByEntityIdResponses, PutApiPatientAppointmentDraftByEntityIdErrors, PostApiPatientAppointmentDraftByEntityIdCommitData, PostApiPatientAppointmentDraftByEntityIdCommitResponses, PostApiPatientAppointmentDraftByEntityIdCommitErrors, PostApiPatientAppointmentDraftValidateData, PostApiPatientAppointmentDraftValidateResponses, PostApiPatientAppointmentDraftValidateErrors, PostApiPatientAppointmentDraftByEntityIdValidateData, PostApiPatientAppointmentDraftByEntityIdValidateResponses, PostApiPatientAppointmentDraftByEntityIdValidateErrors, GetApiPatientAppointmentConfirmationByIdData, GetApiPatientAppointmentConfirmationByIdResponses, GetApiPatientAppointmentConfirmationByIdErrors, PatchApiPatientAppointmentConfirmationByIdData, PatchApiPatientAppointmentConfirmationByIdResponses, PatchApiPatientAppointmentConfirmationByIdErrors, PutApiPatientAppointmentConfirmationByIdData, PutApiPatientAppointmentConfirmationByIdResponses, PutApiPatientAppointmentConfirmationByIdErrors, PostApiPatientAppointmentConfirmationSearchData, PostApiPatientAppointmentConfirmationSearchResponses, GetApiPatientAppointmentConfirmationData, GetApiPatientAppointmentConfirmationResponses, PostApiPatientAppointmentConfirmationData, PostApiPatientAppointmentConfirmationResponses, PostApiPatientAppointmentConfirmationErrors, PostApiPatientAppointmentConfirmationDraftData, PostApiPatientAppointmentConfirmationDraftResponses, PostApiPatientAppointmentConfirmationDraftErrors, GetApiPatientAppointmentConfirmationDraftByEntityIdData, GetApiPatientAppointmentConfirmationDraftByEntityIdResponses, GetApiPatientAppointmentConfirmationDraftByEntityIdErrors, PutApiPatientAppointmentConfirmationDraftByEntityIdData, PutApiPatientAppointmentConfirmationDraftByEntityIdResponses, PutApiPatientAppointmentConfirmationDraftByEntityIdErrors, PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData, PostApiPatientAppointmentConfirmationDraftByEntityIdCommitResponses, PostApiPatientAppointmentConfirmationDraftByEntityIdCommitErrors, PostApiPatientAppointmentConfirmationDraftValidateData, PostApiPatientAppointmentConfirmationDraftValidateResponses, PostApiPatientAppointmentConfirmationDraftValidateErrors, PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData, PostApiPatientAppointmentConfirmationDraftByEntityIdValidateResponses, PostApiPatientAppointmentConfirmationDraftByEntityIdValidateErrors, GetApiPhysicianByIdData, GetApiPhysicianByIdResponses, GetApiPhysicianByIdErrors, PatchApiPhysicianByIdData, PatchApiPhysicianByIdResponses, PatchApiPhysicianByIdErrors, PutApiPhysicianByIdData, PutApiPhysicianByIdResponses, PutApiPhysicianByIdErrors, PostApiPhysicianSearchData, PostApiPhysicianSearchResponses, GetApiPhysicianData, GetApiPhysicianResponses, PostApiPhysicianData, PostApiPhysicianResponses, PostApiPhysicianErrors, PostApiPhysicianDraftData, PostApiPhysicianDraftResponses, PostApiPhysicianDraftErrors, GetApiPhysicianDraftByEntityIdData, GetApiPhysicianDraftByEntityIdResponses, GetApiPhysicianDraftByEntityIdErrors, PutApiPhysicianDraftByEntityIdData, PutApiPhysicianDraftByEntityIdResponses, PutApiPhysicianDraftByEntityIdErrors, PostApiPhysicianDraftByEntityIdCommitData, PostApiPhysicianDraftByEntityIdCommitResponses, PostApiPhysicianDraftByEntityIdCommitErrors, PostApiPhysicianDraftValidateData, PostApiPhysicianDraftValidateResponses, PostApiPhysicianDraftValidateErrors, PostApiPhysicianDraftByEntityIdValidateData, PostApiPhysicianDraftByEntityIdValidateResponses, PostApiPhysicianDraftByEntityIdValidateErrors, GetApiProviderByIdData, GetApiProviderByIdResponses, GetApiProviderByIdErrors, PatchApiProviderByIdData, PatchApiProviderByIdResponses, PatchApiProviderByIdErrors, PutApiProviderByIdData, PutApiProviderByIdResponses, PutApiProviderByIdErrors, PostApiProviderSearchData, PostApiProviderSearchResponses, GetApiProviderData, GetApiProviderResponses, PostApiProviderData, PostApiProviderResponses, PostApiProviderErrors, PostApiProviderDraftData, PostApiProviderDraftResponses, PostApiProviderDraftErrors, GetApiProviderDraftByEntityIdData, GetApiProviderDraftByEntityIdResponses, GetApiProviderDraftByEntityIdErrors, PutApiProviderDraftByEntityIdData, PutApiProviderDraftByEntityIdResponses, PutApiProviderDraftByEntityIdErrors, PostApiProviderDraftByEntityIdCommitData, PostApiProviderDraftByEntityIdCommitResponses, PostApiProviderDraftByEntityIdCommitErrors, PostApiProviderDraftValidateData, PostApiProviderDraftValidateResponses, PostApiProviderDraftValidateErrors, PostApiProviderDraftByEntityIdValidateData, PostApiProviderDraftByEntityIdValidateResponses, PostApiProviderDraftByEntityIdValidateErrors, GetPyapiZipData, GetPyapiZipResponses, GetTsapiZipData, GetTsapiZipResponses, GetApiRoomByIdData, GetApiRoomByIdResponses, GetApiRoomByIdErrors, PatchApiRoomByIdData, PatchApiRoomByIdResponses, PatchApiRoomByIdErrors, PutApiRoomByIdData, PutApiRoomByIdResponses, PutApiRoomByIdErrors, PostApiRoomSearchData, PostApiRoomSearchResponses, GetApiRoomData, GetApiRoomResponses, PostApiRoomData, PostApiRoomResponses, PostApiRoomErrors, PostApiRoomDraftData, PostApiRoomDraftResponses, PostApiRoomDraftErrors, GetApiRoomDraftByEntityIdData, GetApiRoomDraftByEntityIdResponses, GetApiRoomDraftByEntityIdErrors, PutApiRoomDraftByEntityIdData, PutApiRoomDraftByEntityIdResponses, PutApiRoomDraftByEntityIdErrors, PostApiRoomDraftByEntityIdCommitData, PostApiRoomDraftByEntityIdCommitResponses, PostApiRoomDraftByEntityIdCommitErrors, PostApiRoomDraftValidateData, PostApiRoomDraftValidateResponses, PostApiRoomDraftValidateErrors, PostApiRoomDraftByEntityIdValidateData, PostApiRoomDraftByEntityIdValidateResponses, PostApiRoomDraftByEntityIdValidateErrors, PostApiSchedulingFindSlotsData, PostApiSchedulingFindSlotsResponses, GetApiSchedulingConstraintByIdData, GetApiSchedulingConstraintByIdResponses, GetApiSchedulingConstraintByIdErrors, PatchApiSchedulingConstraintByIdData, PatchApiSchedulingConstraintByIdResponses, PatchApiSchedulingConstraintByIdErrors, PutApiSchedulingConstraintByIdData, PutApiSchedulingConstraintByIdResponses, PutApiSchedulingConstraintByIdErrors, PostApiSchedulingConstraintSearchData, PostApiSchedulingConstraintSearchResponses, GetApiSchedulingConstraintData, GetApiSchedulingConstraintResponses, PostApiSchedulingConstraintData, PostApiSchedulingConstraintResponses, PostApiSchedulingConstraintErrors, PostApiSchedulingConstraintDraftData, PostApiSchedulingConstraintDraftResponses, PostApiSchedulingConstraintDraftErrors, GetApiSchedulingConstraintDraftByEntityIdData, GetApiSchedulingConstraintDraftByEntityIdResponses, GetApiSchedulingConstraintDraftByEntityIdErrors, PutApiSchedulingConstraintDraftByEntityIdData, PutApiSchedulingConstraintDraftByEntityIdResponses, PutApiSchedulingConstraintDraftByEntityIdErrors, PostApiSchedulingConstraintDraftByEntityIdCommitData, PostApiSchedulingConstraintDraftByEntityIdCommitResponses, PostApiSchedulingConstraintDraftByEntityIdCommitErrors, PostApiSchedulingConstraintDraftValidateData, PostApiSchedulingConstraintDraftValidateResponses, PostApiSchedulingConstraintDraftValidateErrors, PostApiSchedulingConstraintDraftByEntityIdValidateData, PostApiSchedulingConstraintDraftByEntityIdValidateResponses, PostApiSchedulingConstraintDraftByEntityIdValidateErrors, GetApiStudyByIdData, GetApiStudyByIdResponses, GetApiStudyByIdErrors, PatchApiStudyByIdData, PatchApiStudyByIdResponses, PatchApiStudyByIdErrors, PutApiStudyByIdData, PutApiStudyByIdResponses, PutApiStudyByIdErrors, PostApiStudySearchData, PostApiStudySearchResponses, GetApiStudyData, GetApiStudyResponses, PostApiStudyData, PostApiStudyResponses, PostApiStudyErrors, PostApiStudyDraftData, PostApiStudyDraftResponses, PostApiStudyDraftErrors, GetApiStudyDraftByEntityIdData, GetApiStudyDraftByEntityIdResponses, GetApiStudyDraftByEntityIdErrors, PutApiStudyDraftByEntityIdData, PutApiStudyDraftByEntityIdResponses, PutApiStudyDraftByEntityIdErrors, PostApiStudyDraftByEntityIdCommitData, PostApiStudyDraftByEntityIdCommitResponses, PostApiStudyDraftByEntityIdCommitErrors, PostApiStudyDraftValidateData, PostApiStudyDraftValidateResponses, PostApiStudyDraftValidateErrors, PostApiStudyDraftByEntityIdValidateData, PostApiStudyDraftByEntityIdValidateResponses, PostApiStudyDraftByEntityIdValidateErrors, GetApiTechnicianByIdData, GetApiTechnicianByIdResponses, GetApiTechnicianByIdErrors, PatchApiTechnicianByIdData, PatchApiTechnicianByIdResponses, PatchApiTechnicianByIdErrors, PutApiTechnicianByIdData, PutApiTechnicianByIdResponses, PutApiTechnicianByIdErrors, PostApiTechnicianSearchData, PostApiTechnicianSearchResponses, GetApiTechnicianData, GetApiTechnicianResponses, PostApiTechnicianData, PostApiTechnicianResponses, PostApiTechnicianErrors, PostApiTechnicianDraftData, PostApiTechnicianDraftResponses, PostApiTechnicianDraftErrors, GetApiTechnicianDraftByEntityIdData, GetApiTechnicianDraftByEntityIdResponses, GetApiTechnicianDraftByEntityIdErrors, PutApiTechnicianDraftByEntityIdData, PutApiTechnicianDraftByEntityIdResponses, PutApiTechnicianDraftByEntityIdErrors, PostApiTechnicianDraftByEntityIdCommitData, PostApiTechnicianDraftByEntityIdCommitResponses, PostApiTechnicianDraftByEntityIdCommitErrors, PostApiTechnicianDraftValidateData, PostApiTechnicianDraftValidateResponses, PostApiTechnicianDraftValidateErrors, PostApiTechnicianDraftByEntityIdValidateData, PostApiTechnicianDraftByEntityIdValidateResponses, PostApiTechnicianDraftByEntityIdValidateErrors, GetApiTechnicianAppointmentByIdData, GetApiTechnicianAppointmentByIdResponses, GetApiTechnicianAppointmentByIdErrors, PatchApiTechnicianAppointmentByIdData, PatchApiTechnicianAppointmentByIdResponses, PatchApiTechnicianAppointmentByIdErrors, PutApiTechnicianAppointmentByIdData, PutApiTechnicianAppointmentByIdResponses, PutApiTechnicianAppointmentByIdErrors, PostApiTechnicianAppointmentSearchData, PostApiTechnicianAppointmentSearchResponses, GetApiTechnicianAppointmentData, GetApiTechnicianAppointmentResponses, PostApiTechnicianAppointmentData, PostApiTechnicianAppointmentResponses, PostApiTechnicianAppointmentErrors, PostApiTechnicianAppointmentDraftData, PostApiTechnicianAppointmentDraftResponses, PostApiTechnicianAppointmentDraftErrors, GetApiTechnicianAppointmentDraftByEntityIdData, GetApiTechnicianAppointmentDraftByEntityIdResponses, GetApiTechnicianAppointmentDraftByEntityIdErrors, PutApiTechnicianAppointmentDraftByEntityIdData, PutApiTechnicianAppointmentDraftByEntityIdResponses, PutApiTechnicianAppointmentDraftByEntityIdErrors, PostApiTechnicianAppointmentDraftByEntityIdCommitData, PostApiTechnicianAppointmentDraftByEntityIdCommitResponses, PostApiTechnicianAppointmentDraftByEntityIdCommitErrors, PostApiTechnicianAppointmentDraftValidateData, PostApiTechnicianAppointmentDraftValidateResponses, PostApiTechnicianAppointmentDraftValidateErrors, PostApiTechnicianAppointmentDraftByEntityIdValidateData, PostApiTechnicianAppointmentDraftByEntityIdValidateResponses, PostApiTechnicianAppointmentDraftByEntityIdValidateErrors, GetApiWorkflowFlowsData, GetApiWorkflowFlowsResponses, GetApiWorkflowMyTasksData, GetApiWorkflowMyTasksResponses, GetApiWorkflowProfileData, GetApiWorkflowProfileResponses } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

export const getApiAddNewOrderValidationRules = <ThrowOnError extends boolean = false>(options?: Options<GetApiAddNewOrderValidationRulesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAddNewOrderValidationRulesResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/validation-rules',
        ...options
    });
};

export const getApiAddNewOrderStateById = <ThrowOnError extends boolean = false>(options: Options<GetApiAddNewOrderStateByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAddNewOrderStateByIdResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/state/{id}',
        ...options
    });
};

export const postApiAddNewOrderStart = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderStartData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderStartResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/start',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewOrderRewind = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderRewindData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderRewindResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/rewind',
        ...options
    });
};

export const postApiAddNewOrderList = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderListResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewOrderAddStudy = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderAddStudyData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderAddStudyResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/add-study',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewOrderAddStudyDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderAddStudyDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderAddStudyDraftResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/add-study/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewOrderAddStudyValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderAddStudyValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderAddStudyValidateResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/add-study/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewOrderAddCareLocation = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderAddCareLocationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderAddCareLocationResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/add-care-location',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewOrderAddCareLocationDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderAddCareLocationDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderAddCareLocationDraftResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/add-care-location/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewOrderAddCareLocationValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderAddCareLocationValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderAddCareLocationValidateResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/add-care-location/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewOrderAddPhysicians = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderAddPhysiciansData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderAddPhysiciansResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/add-physicians',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewOrderAddPhysiciansDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderAddPhysiciansDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderAddPhysiciansDraftResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/add-physicians/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewOrderAddPhysiciansValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderAddPhysiciansValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderAddPhysiciansValidateResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/add-physicians/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewOrderReviewAndSubmitOrder = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderReviewAndSubmitOrderData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderReviewAndSubmitOrderResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/review-and-submit-order',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewOrderReviewAndSubmitOrderDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderReviewAndSubmitOrderDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderReviewAndSubmitOrderDraftResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/review-and-submit-order/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewOrderReviewAndSubmitOrderValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewOrderReviewAndSubmitOrderValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewOrderReviewAndSubmitOrderValidateResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewOrder/review-and-submit-order/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAddNewPatientValidationRules = <ThrowOnError extends boolean = false>(options?: Options<GetApiAddNewPatientValidationRulesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAddNewPatientValidationRulesResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/validation-rules',
        ...options
    });
};

export const getApiAddNewPatientStateById = <ThrowOnError extends boolean = false>(options: Options<GetApiAddNewPatientStateByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAddNewPatientStateByIdResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/state/{id}',
        ...options
    });
};

export const postApiAddNewPatientStart = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientStartData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientStartResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/start',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientRewind = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientRewindData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientRewindResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/rewind',
        ...options
    });
};

export const postApiAddNewPatientList = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientListResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddBasicInformation = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddBasicInformationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddBasicInformationResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-basic-information',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddBasicInformationDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddBasicInformationDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddBasicInformationDraftResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-basic-information/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddBasicInformationValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddBasicInformationValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddBasicInformationValidateResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-basic-information/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddContacts = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddContactsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddContactsResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-contacts',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddContactsDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddContactsDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddContactsDraftResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-contacts/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddContactsValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddContactsValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddContactsValidateResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-contacts/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddAddresses = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddAddressesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddAddressesResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-addresses',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddAddressesDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddAddressesDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddAddressesDraftResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-addresses/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddAddressesValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddAddressesValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddAddressesValidateResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-addresses/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddInsurances = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddInsurancesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddInsurancesResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-insurances',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddInsurancesDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddInsurancesDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddInsurancesDraftResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-insurances/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddInsurancesValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddInsurancesValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddInsurancesValidateResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-insurances/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddGuardians = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddGuardiansData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddGuardiansResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-guardians',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddGuardiansDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddGuardiansDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddGuardiansDraftResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-guardians/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddGuardiansValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddGuardiansValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddGuardiansValidateResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-guardians/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddClinicalInformation = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddClinicalInformationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddClinicalInformationResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-clinical-information',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddClinicalInformationDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddClinicalInformationDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddClinicalInformationDraftResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-clinical-information/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAddNewPatientAddClinicalInformationValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiAddNewPatientAddClinicalInformationValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAddNewPatientAddClinicalInformationValidateResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/AddNewPatient/add-clinical-information/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiCareLocationById = <ThrowOnError extends boolean = false>(options: Options<GetApiCareLocationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiCareLocationByIdResponses, GetApiCareLocationByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/CareLocation/{id}',
        ...options
    });
};

export const patchApiCareLocationById = <ThrowOnError extends boolean = false>(options: Options<PatchApiCareLocationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiCareLocationByIdResponses, PatchApiCareLocationByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/CareLocation/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiCareLocationById = <ThrowOnError extends boolean = false>(options: Options<PutApiCareLocationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiCareLocationByIdResponses, PutApiCareLocationByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/CareLocation/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiCareLocationSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiCareLocationSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiCareLocationSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/CareLocation/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiCareLocation = <ThrowOnError extends boolean = false>(options?: Options<GetApiCareLocationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiCareLocationResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/CareLocation',
        ...options
    });
};

export const postApiCareLocation = <ThrowOnError extends boolean = false>(options?: Options<PostApiCareLocationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiCareLocationResponses, PostApiCareLocationErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/CareLocation',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiCareLocationDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiCareLocationDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiCareLocationDraftResponses, PostApiCareLocationDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/CareLocation/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiCareLocationDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiCareLocationDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiCareLocationDraftByEntityIdResponses, GetApiCareLocationDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/CareLocation/draft/{entityId}',
        ...options
    });
};

export const putApiCareLocationDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiCareLocationDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiCareLocationDraftByEntityIdResponses, PutApiCareLocationDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/CareLocation/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiCareLocationDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiCareLocationDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiCareLocationDraftByEntityIdCommitResponses, PostApiCareLocationDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/CareLocation/draft/{entityId}/commit',
        ...options
    });
};

export const postApiCareLocationDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiCareLocationDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiCareLocationDraftValidateResponses, PostApiCareLocationDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/CareLocation/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiCareLocationDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiCareLocationDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiCareLocationDraftByEntityIdValidateResponses, PostApiCareLocationDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/CareLocation/draft/{entityId}/validate',
        ...options
    });
};

export const getApiDraftById = <ThrowOnError extends boolean = false>(options: Options<GetApiDraftByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiDraftByIdResponses, GetApiDraftByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Draft/{id}',
        ...options
    });
};

export const patchApiDraftById = <ThrowOnError extends boolean = false>(options: Options<PatchApiDraftByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiDraftByIdResponses, PatchApiDraftByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Draft/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiDraftById = <ThrowOnError extends boolean = false>(options: Options<PutApiDraftByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiDraftByIdResponses, PutApiDraftByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Draft/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiDraftSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiDraftSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiDraftSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Draft/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiDraft = <ThrowOnError extends boolean = false>(options?: Options<GetApiDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiDraftResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Draft',
        ...options
    });
};

export const postApiDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiDraftResponses, PostApiDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiDraftDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiDraftDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiDraftDraftResponses, PostApiDraftDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Draft/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiDraftDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiDraftDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiDraftDraftByEntityIdResponses, GetApiDraftDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Draft/draft/{entityId}',
        ...options
    });
};

export const putApiDraftDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiDraftDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiDraftDraftByEntityIdResponses, PutApiDraftDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Draft/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiDraftDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiDraftDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiDraftDraftByEntityIdCommitResponses, PostApiDraftDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Draft/draft/{entityId}/commit',
        ...options
    });
};

export const postApiDraftDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiDraftDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiDraftDraftValidateResponses, PostApiDraftDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Draft/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiDraftDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiDraftDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiDraftDraftByEntityIdValidateResponses, PostApiDraftDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Draft/draft/{entityId}/validate',
        ...options
    });
};

export const getApiEquipmentById = <ThrowOnError extends boolean = false>(options: Options<GetApiEquipmentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEquipmentByIdResponses, GetApiEquipmentByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Equipment/{id}',
        ...options
    });
};

export const patchApiEquipmentById = <ThrowOnError extends boolean = false>(options: Options<PatchApiEquipmentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiEquipmentByIdResponses, PatchApiEquipmentByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Equipment/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiEquipmentById = <ThrowOnError extends boolean = false>(options: Options<PutApiEquipmentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEquipmentByIdResponses, PutApiEquipmentByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Equipment/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEquipmentSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiEquipmentSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEquipmentSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Equipment/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEquipment = <ThrowOnError extends boolean = false>(options?: Options<GetApiEquipmentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEquipmentResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Equipment',
        ...options
    });
};

export const postApiEquipment = <ThrowOnError extends boolean = false>(options?: Options<PostApiEquipmentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEquipmentResponses, PostApiEquipmentErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Equipment',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEquipmentDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiEquipmentDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEquipmentDraftResponses, PostApiEquipmentDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Equipment/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEquipmentDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiEquipmentDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEquipmentDraftByEntityIdResponses, GetApiEquipmentDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Equipment/draft/{entityId}',
        ...options
    });
};

export const putApiEquipmentDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiEquipmentDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEquipmentDraftByEntityIdResponses, PutApiEquipmentDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Equipment/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEquipmentDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiEquipmentDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiEquipmentDraftByEntityIdCommitResponses, PostApiEquipmentDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Equipment/draft/{entityId}/commit',
        ...options
    });
};

export const postApiEquipmentDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiEquipmentDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEquipmentDraftValidateResponses, PostApiEquipmentDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Equipment/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEquipmentDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiEquipmentDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiEquipmentDraftByEntityIdValidateResponses, PostApiEquipmentDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Equipment/draft/{entityId}/validate',
        ...options
    });
};

export const postApiFileRequestUploadToken = <ThrowOnError extends boolean = false>(options?: Options<PostApiFileRequestUploadTokenData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiFileRequestUploadTokenResponses, PostApiFileRequestUploadTokenErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/File/request-upload-token',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiFileUpload = <ThrowOnError extends boolean = false>(options: Options<PostApiFileUploadData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiFileUploadResponses, PostApiFileUploadErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/File/upload',
        ...options
    });
};

export const getApiFileStatusByFileId = <ThrowOnError extends boolean = false>(options: Options<GetApiFileStatusByFileIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiFileStatusByFileIdResponses, GetApiFileStatusByFileIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/File/status/{fileId}',
        ...options
    });
};

export const getApiHealth = <ThrowOnError extends boolean = false>(options?: Options<GetApiHealthData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiHealthResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Health',
        ...options
    });
};

export const getApiInsuranceById = <ThrowOnError extends boolean = false>(options: Options<GetApiInsuranceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiInsuranceByIdResponses, GetApiInsuranceByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Insurance/{id}',
        ...options
    });
};

export const patchApiInsuranceById = <ThrowOnError extends boolean = false>(options: Options<PatchApiInsuranceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiInsuranceByIdResponses, PatchApiInsuranceByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Insurance/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiInsuranceById = <ThrowOnError extends boolean = false>(options: Options<PutApiInsuranceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiInsuranceByIdResponses, PutApiInsuranceByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Insurance/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiInsuranceSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiInsuranceSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiInsuranceSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Insurance/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiInsurance = <ThrowOnError extends boolean = false>(options?: Options<GetApiInsuranceData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiInsuranceResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Insurance',
        ...options
    });
};

export const postApiInsurance = <ThrowOnError extends boolean = false>(options?: Options<PostApiInsuranceData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiInsuranceResponses, PostApiInsuranceErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Insurance',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiInsuranceDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiInsuranceDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiInsuranceDraftResponses, PostApiInsuranceDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Insurance/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiInsuranceDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiInsuranceDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiInsuranceDraftByEntityIdResponses, GetApiInsuranceDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Insurance/draft/{entityId}',
        ...options
    });
};

export const putApiInsuranceDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiInsuranceDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiInsuranceDraftByEntityIdResponses, PutApiInsuranceDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Insurance/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiInsuranceDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiInsuranceDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiInsuranceDraftByEntityIdCommitResponses, PostApiInsuranceDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Insurance/draft/{entityId}/commit',
        ...options
    });
};

export const postApiInsuranceDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiInsuranceDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiInsuranceDraftValidateResponses, PostApiInsuranceDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Insurance/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiInsuranceDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiInsuranceDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiInsuranceDraftByEntityIdValidateResponses, PostApiInsuranceDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Insurance/draft/{entityId}/validate',
        ...options
    });
};

export const postApiInsuranceVerificationStart = <ThrowOnError extends boolean = false>(options?: Options<PostApiInsuranceVerificationStartData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiInsuranceVerificationStartResponses, PostApiInsuranceVerificationStartErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/InsuranceVerification/start',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiInsuranceVerificationStatusByJobId = <ThrowOnError extends boolean = false>(options: Options<GetApiInsuranceVerificationStatusByJobIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiInsuranceVerificationStatusByJobIdResponses, GetApiInsuranceVerificationStatusByJobIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/InsuranceVerification/status/{jobId}',
        ...options
    });
};

export const postApiInsuranceVerificationSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiInsuranceVerificationSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiInsuranceVerificationSearchResponses, PostApiInsuranceVerificationSearchErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/InsuranceVerification/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiInsuranceVerificationFastauthWebhook = <ThrowOnError extends boolean = false>(options?: Options<PostApiInsuranceVerificationFastauthWebhookData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiInsuranceVerificationFastauthWebhookResponses, PostApiInsuranceVerificationFastauthWebhookErrors, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/InsuranceVerification/fastauth-webhook',
        ...options
    });
};

export const postApiLogin = <ThrowOnError extends boolean = false>(options?: Options<PostApiLoginData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiLoginResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Login',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiLoginCheck = <ThrowOnError extends boolean = false>(options?: Options<GetApiLoginCheckData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiLoginCheckResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Login/check',
        ...options
    });
};

export const postApiMockDbDelete = <ThrowOnError extends boolean = false>(options?: Options<PostApiMockDbDeleteData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiMockDbDeleteResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/MockDb/delete',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiMockDbReset = <ThrowOnError extends boolean = false>(options?: Options<GetApiMockDbResetData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiMockDbResetResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/MockDb/reset',
        ...options
    });
};

export const getApiNoteById = <ThrowOnError extends boolean = false>(options: Options<GetApiNoteByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiNoteByIdResponses, GetApiNoteByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Note/{id}',
        ...options
    });
};

export const patchApiNoteById = <ThrowOnError extends boolean = false>(options: Options<PatchApiNoteByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiNoteByIdResponses, PatchApiNoteByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Note/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiNoteById = <ThrowOnError extends boolean = false>(options: Options<PutApiNoteByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiNoteByIdResponses, PutApiNoteByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Note/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiNoteSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiNoteSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiNoteSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Note/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiNote = <ThrowOnError extends boolean = false>(options?: Options<GetApiNoteData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiNoteResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Note',
        ...options
    });
};

export const postApiNote = <ThrowOnError extends boolean = false>(options?: Options<PostApiNoteData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiNoteResponses, PostApiNoteErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Note',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiNoteDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiNoteDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiNoteDraftResponses, PostApiNoteDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Note/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiNoteDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiNoteDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiNoteDraftByEntityIdResponses, GetApiNoteDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Note/draft/{entityId}',
        ...options
    });
};

export const putApiNoteDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiNoteDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiNoteDraftByEntityIdResponses, PutApiNoteDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Note/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiNoteDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiNoteDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiNoteDraftByEntityIdCommitResponses, PostApiNoteDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Note/draft/{entityId}/commit',
        ...options
    });
};

export const postApiNoteDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiNoteDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiNoteDraftValidateResponses, PostApiNoteDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Note/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiNoteDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiNoteDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiNoteDraftByEntityIdValidateResponses, PostApiNoteDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Note/draft/{entityId}/validate',
        ...options
    });
};

export const getApiOrderById = <ThrowOnError extends boolean = false>(options: Options<GetApiOrderByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiOrderByIdResponses, GetApiOrderByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Order/{id}',
        ...options
    });
};

export const patchApiOrderById = <ThrowOnError extends boolean = false>(options: Options<PatchApiOrderByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiOrderByIdResponses, PatchApiOrderByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Order/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiOrderById = <ThrowOnError extends boolean = false>(options: Options<PutApiOrderByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiOrderByIdResponses, PutApiOrderByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Order/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiOrderSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiOrderSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOrderSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Order/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiOrder = <ThrowOnError extends boolean = false>(options?: Options<GetApiOrderData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiOrderResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Order',
        ...options
    });
};

export const postApiOrder = <ThrowOnError extends boolean = false>(options?: Options<PostApiOrderData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOrderResponses, PostApiOrderErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Order',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiOrderDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiOrderDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOrderDraftResponses, PostApiOrderDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Order/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiOrderDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiOrderDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiOrderDraftByEntityIdResponses, GetApiOrderDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Order/draft/{entityId}',
        ...options
    });
};

export const putApiOrderDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiOrderDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiOrderDraftByEntityIdResponses, PutApiOrderDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Order/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiOrderDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiOrderDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiOrderDraftByEntityIdCommitResponses, PostApiOrderDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Order/draft/{entityId}/commit',
        ...options
    });
};

export const postApiOrderDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiOrderDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiOrderDraftValidateResponses, PostApiOrderDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Order/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiOrderDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiOrderDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiOrderDraftByEntityIdValidateResponses, PostApiOrderDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Order/draft/{entityId}/validate',
        ...options
    });
};

export const getApiPatientById = <ThrowOnError extends boolean = false>(options: Options<GetApiPatientByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPatientByIdResponses, GetApiPatientByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Patient/{id}',
        ...options
    });
};

export const patchApiPatientById = <ThrowOnError extends boolean = false>(options: Options<PatchApiPatientByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiPatientByIdResponses, PatchApiPatientByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Patient/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiPatientById = <ThrowOnError extends boolean = false>(options: Options<PutApiPatientByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiPatientByIdResponses, PutApiPatientByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Patient/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiPatientSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiPatientSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPatientSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Patient/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiPatient = <ThrowOnError extends boolean = false>(options?: Options<GetApiPatientData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPatientResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Patient',
        ...options
    });
};

export const postApiPatient = <ThrowOnError extends boolean = false>(options?: Options<PostApiPatientData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPatientResponses, PostApiPatientErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Patient',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiPatientDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiPatientDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPatientDraftResponses, PostApiPatientDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Patient/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiPatientDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiPatientDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPatientDraftByEntityIdResponses, GetApiPatientDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Patient/draft/{entityId}',
        ...options
    });
};

export const putApiPatientDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiPatientDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiPatientDraftByEntityIdResponses, PutApiPatientDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Patient/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiPatientDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiPatientDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiPatientDraftByEntityIdCommitResponses, PostApiPatientDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Patient/draft/{entityId}/commit',
        ...options
    });
};

export const postApiPatientDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiPatientDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPatientDraftValidateResponses, PostApiPatientDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Patient/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiPatientDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiPatientDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiPatientDraftByEntityIdValidateResponses, PostApiPatientDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Patient/draft/{entityId}/validate',
        ...options
    });
};

export const getApiPatientAppointmentById = <ThrowOnError extends boolean = false>(options: Options<GetApiPatientAppointmentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPatientAppointmentByIdResponses, GetApiPatientAppointmentByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointment/{id}',
        ...options
    });
};

export const patchApiPatientAppointmentById = <ThrowOnError extends boolean = false>(options: Options<PatchApiPatientAppointmentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiPatientAppointmentByIdResponses, PatchApiPatientAppointmentByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointment/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiPatientAppointmentById = <ThrowOnError extends boolean = false>(options: Options<PutApiPatientAppointmentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiPatientAppointmentByIdResponses, PutApiPatientAppointmentByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointment/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiPatientAppointmentSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiPatientAppointmentSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPatientAppointmentSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointment/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiPatientAppointment = <ThrowOnError extends boolean = false>(options?: Options<GetApiPatientAppointmentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPatientAppointmentResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointment',
        ...options
    });
};

export const postApiPatientAppointment = <ThrowOnError extends boolean = false>(options?: Options<PostApiPatientAppointmentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPatientAppointmentResponses, PostApiPatientAppointmentErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointment',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiPatientAppointmentDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiPatientAppointmentDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPatientAppointmentDraftResponses, PostApiPatientAppointmentDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointment/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiPatientAppointmentDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiPatientAppointmentDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPatientAppointmentDraftByEntityIdResponses, GetApiPatientAppointmentDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointment/draft/{entityId}',
        ...options
    });
};

export const putApiPatientAppointmentDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiPatientAppointmentDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiPatientAppointmentDraftByEntityIdResponses, PutApiPatientAppointmentDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointment/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiPatientAppointmentDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiPatientAppointmentDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiPatientAppointmentDraftByEntityIdCommitResponses, PostApiPatientAppointmentDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointment/draft/{entityId}/commit',
        ...options
    });
};

export const postApiPatientAppointmentDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiPatientAppointmentDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPatientAppointmentDraftValidateResponses, PostApiPatientAppointmentDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointment/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiPatientAppointmentDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiPatientAppointmentDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiPatientAppointmentDraftByEntityIdValidateResponses, PostApiPatientAppointmentDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointment/draft/{entityId}/validate',
        ...options
    });
};

export const getApiPatientAppointmentConfirmationById = <ThrowOnError extends boolean = false>(options: Options<GetApiPatientAppointmentConfirmationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPatientAppointmentConfirmationByIdResponses, GetApiPatientAppointmentConfirmationByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointmentConfirmation/{id}',
        ...options
    });
};

export const patchApiPatientAppointmentConfirmationById = <ThrowOnError extends boolean = false>(options: Options<PatchApiPatientAppointmentConfirmationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiPatientAppointmentConfirmationByIdResponses, PatchApiPatientAppointmentConfirmationByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointmentConfirmation/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiPatientAppointmentConfirmationById = <ThrowOnError extends boolean = false>(options: Options<PutApiPatientAppointmentConfirmationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiPatientAppointmentConfirmationByIdResponses, PutApiPatientAppointmentConfirmationByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointmentConfirmation/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiPatientAppointmentConfirmationSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiPatientAppointmentConfirmationSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPatientAppointmentConfirmationSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointmentConfirmation/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiPatientAppointmentConfirmation = <ThrowOnError extends boolean = false>(options?: Options<GetApiPatientAppointmentConfirmationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPatientAppointmentConfirmationResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointmentConfirmation',
        ...options
    });
};

export const postApiPatientAppointmentConfirmation = <ThrowOnError extends boolean = false>(options?: Options<PostApiPatientAppointmentConfirmationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPatientAppointmentConfirmationResponses, PostApiPatientAppointmentConfirmationErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointmentConfirmation',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiPatientAppointmentConfirmationDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiPatientAppointmentConfirmationDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPatientAppointmentConfirmationDraftResponses, PostApiPatientAppointmentConfirmationDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointmentConfirmation/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiPatientAppointmentConfirmationDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiPatientAppointmentConfirmationDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPatientAppointmentConfirmationDraftByEntityIdResponses, GetApiPatientAppointmentConfirmationDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointmentConfirmation/draft/{entityId}',
        ...options
    });
};

export const putApiPatientAppointmentConfirmationDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiPatientAppointmentConfirmationDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiPatientAppointmentConfirmationDraftByEntityIdResponses, PutApiPatientAppointmentConfirmationDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointmentConfirmation/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiPatientAppointmentConfirmationDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiPatientAppointmentConfirmationDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiPatientAppointmentConfirmationDraftByEntityIdCommitResponses, PostApiPatientAppointmentConfirmationDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointmentConfirmation/draft/{entityId}/commit',
        ...options
    });
};

export const postApiPatientAppointmentConfirmationDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiPatientAppointmentConfirmationDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPatientAppointmentConfirmationDraftValidateResponses, PostApiPatientAppointmentConfirmationDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointmentConfirmation/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiPatientAppointmentConfirmationDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiPatientAppointmentConfirmationDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiPatientAppointmentConfirmationDraftByEntityIdValidateResponses, PostApiPatientAppointmentConfirmationDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/PatientAppointmentConfirmation/draft/{entityId}/validate',
        ...options
    });
};

export const getApiPhysicianById = <ThrowOnError extends boolean = false>(options: Options<GetApiPhysicianByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPhysicianByIdResponses, GetApiPhysicianByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Physician/{id}',
        ...options
    });
};

export const patchApiPhysicianById = <ThrowOnError extends boolean = false>(options: Options<PatchApiPhysicianByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiPhysicianByIdResponses, PatchApiPhysicianByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Physician/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiPhysicianById = <ThrowOnError extends boolean = false>(options: Options<PutApiPhysicianByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiPhysicianByIdResponses, PutApiPhysicianByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Physician/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiPhysicianSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiPhysicianSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPhysicianSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Physician/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiPhysician = <ThrowOnError extends boolean = false>(options?: Options<GetApiPhysicianData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiPhysicianResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Physician',
        ...options
    });
};

export const postApiPhysician = <ThrowOnError extends boolean = false>(options?: Options<PostApiPhysicianData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPhysicianResponses, PostApiPhysicianErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Physician',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiPhysicianDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiPhysicianDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPhysicianDraftResponses, PostApiPhysicianDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Physician/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiPhysicianDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiPhysicianDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiPhysicianDraftByEntityIdResponses, GetApiPhysicianDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Physician/draft/{entityId}',
        ...options
    });
};

export const putApiPhysicianDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiPhysicianDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiPhysicianDraftByEntityIdResponses, PutApiPhysicianDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Physician/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiPhysicianDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiPhysicianDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiPhysicianDraftByEntityIdCommitResponses, PostApiPhysicianDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Physician/draft/{entityId}/commit',
        ...options
    });
};

export const postApiPhysicianDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiPhysicianDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiPhysicianDraftValidateResponses, PostApiPhysicianDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Physician/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiPhysicianDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiPhysicianDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiPhysicianDraftByEntityIdValidateResponses, PostApiPhysicianDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Physician/draft/{entityId}/validate',
        ...options
    });
};

export const getApiProviderById = <ThrowOnError extends boolean = false>(options: Options<GetApiProviderByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiProviderByIdResponses, GetApiProviderByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Provider/{id}',
        ...options
    });
};

export const patchApiProviderById = <ThrowOnError extends boolean = false>(options: Options<PatchApiProviderByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiProviderByIdResponses, PatchApiProviderByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Provider/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiProviderById = <ThrowOnError extends boolean = false>(options: Options<PutApiProviderByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiProviderByIdResponses, PutApiProviderByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Provider/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiProviderSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiProviderSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiProviderSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Provider/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiProvider = <ThrowOnError extends boolean = false>(options?: Options<GetApiProviderData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiProviderResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Provider',
        ...options
    });
};

export const postApiProvider = <ThrowOnError extends boolean = false>(options?: Options<PostApiProviderData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiProviderResponses, PostApiProviderErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Provider',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiProviderDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiProviderDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiProviderDraftResponses, PostApiProviderDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Provider/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiProviderDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiProviderDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiProviderDraftByEntityIdResponses, GetApiProviderDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Provider/draft/{entityId}',
        ...options
    });
};

export const putApiProviderDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiProviderDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiProviderDraftByEntityIdResponses, PutApiProviderDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Provider/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiProviderDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiProviderDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiProviderDraftByEntityIdCommitResponses, PostApiProviderDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Provider/draft/{entityId}/commit',
        ...options
    });
};

export const postApiProviderDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiProviderDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiProviderDraftValidateResponses, PostApiProviderDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Provider/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiProviderDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiProviderDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiProviderDraftByEntityIdValidateResponses, PostApiProviderDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Provider/draft/{entityId}/validate',
        ...options
    });
};

export const getPyapiZip = <ThrowOnError extends boolean = false>(options?: Options<GetPyapiZipData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetPyapiZipResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/pyapi.zip',
        ...options
    });
};

export const getTsapiZip = <ThrowOnError extends boolean = false>(options?: Options<GetTsapiZipData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetTsapiZipResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/tsapi.zip',
        ...options
    });
};

export const getApiRoomById = <ThrowOnError extends boolean = false>(options: Options<GetApiRoomByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiRoomByIdResponses, GetApiRoomByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Room/{id}',
        ...options
    });
};

export const patchApiRoomById = <ThrowOnError extends boolean = false>(options: Options<PatchApiRoomByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiRoomByIdResponses, PatchApiRoomByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Room/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiRoomById = <ThrowOnError extends boolean = false>(options: Options<PutApiRoomByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiRoomByIdResponses, PutApiRoomByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Room/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiRoomSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiRoomSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiRoomSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Room/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiRoom = <ThrowOnError extends boolean = false>(options?: Options<GetApiRoomData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiRoomResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Room',
        ...options
    });
};

export const postApiRoom = <ThrowOnError extends boolean = false>(options?: Options<PostApiRoomData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiRoomResponses, PostApiRoomErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Room',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiRoomDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiRoomDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiRoomDraftResponses, PostApiRoomDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Room/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiRoomDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiRoomDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiRoomDraftByEntityIdResponses, GetApiRoomDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Room/draft/{entityId}',
        ...options
    });
};

export const putApiRoomDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiRoomDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiRoomDraftByEntityIdResponses, PutApiRoomDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Room/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiRoomDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiRoomDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiRoomDraftByEntityIdCommitResponses, PostApiRoomDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Room/draft/{entityId}/commit',
        ...options
    });
};

export const postApiRoomDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiRoomDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiRoomDraftValidateResponses, PostApiRoomDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Room/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiRoomDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiRoomDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiRoomDraftByEntityIdValidateResponses, PostApiRoomDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Room/draft/{entityId}/validate',
        ...options
    });
};

export const postApiSchedulingFindSlots = <ThrowOnError extends boolean = false>(options?: Options<PostApiSchedulingFindSlotsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSchedulingFindSlotsResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Scheduling/find-slots',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiSchedulingConstraintById = <ThrowOnError extends boolean = false>(options: Options<GetApiSchedulingConstraintByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiSchedulingConstraintByIdResponses, GetApiSchedulingConstraintByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/SchedulingConstraint/{id}',
        ...options
    });
};

export const patchApiSchedulingConstraintById = <ThrowOnError extends boolean = false>(options: Options<PatchApiSchedulingConstraintByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiSchedulingConstraintByIdResponses, PatchApiSchedulingConstraintByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/SchedulingConstraint/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiSchedulingConstraintById = <ThrowOnError extends boolean = false>(options: Options<PutApiSchedulingConstraintByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiSchedulingConstraintByIdResponses, PutApiSchedulingConstraintByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/SchedulingConstraint/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiSchedulingConstraintSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiSchedulingConstraintSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSchedulingConstraintSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/SchedulingConstraint/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiSchedulingConstraint = <ThrowOnError extends boolean = false>(options?: Options<GetApiSchedulingConstraintData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiSchedulingConstraintResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/SchedulingConstraint',
        ...options
    });
};

export const postApiSchedulingConstraint = <ThrowOnError extends boolean = false>(options?: Options<PostApiSchedulingConstraintData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSchedulingConstraintResponses, PostApiSchedulingConstraintErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/SchedulingConstraint',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiSchedulingConstraintDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiSchedulingConstraintDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSchedulingConstraintDraftResponses, PostApiSchedulingConstraintDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/SchedulingConstraint/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiSchedulingConstraintDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiSchedulingConstraintDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiSchedulingConstraintDraftByEntityIdResponses, GetApiSchedulingConstraintDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/SchedulingConstraint/draft/{entityId}',
        ...options
    });
};

export const putApiSchedulingConstraintDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiSchedulingConstraintDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiSchedulingConstraintDraftByEntityIdResponses, PutApiSchedulingConstraintDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/SchedulingConstraint/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiSchedulingConstraintDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiSchedulingConstraintDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiSchedulingConstraintDraftByEntityIdCommitResponses, PostApiSchedulingConstraintDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/SchedulingConstraint/draft/{entityId}/commit',
        ...options
    });
};

export const postApiSchedulingConstraintDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiSchedulingConstraintDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiSchedulingConstraintDraftValidateResponses, PostApiSchedulingConstraintDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/SchedulingConstraint/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiSchedulingConstraintDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiSchedulingConstraintDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiSchedulingConstraintDraftByEntityIdValidateResponses, PostApiSchedulingConstraintDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/SchedulingConstraint/draft/{entityId}/validate',
        ...options
    });
};

export const getApiStudyById = <ThrowOnError extends boolean = false>(options: Options<GetApiStudyByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiStudyByIdResponses, GetApiStudyByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Study/{id}',
        ...options
    });
};

export const patchApiStudyById = <ThrowOnError extends boolean = false>(options: Options<PatchApiStudyByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiStudyByIdResponses, PatchApiStudyByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Study/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiStudyById = <ThrowOnError extends boolean = false>(options: Options<PutApiStudyByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiStudyByIdResponses, PutApiStudyByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Study/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiStudySearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiStudySearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiStudySearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Study/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiStudy = <ThrowOnError extends boolean = false>(options?: Options<GetApiStudyData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiStudyResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Study',
        ...options
    });
};

export const postApiStudy = <ThrowOnError extends boolean = false>(options?: Options<PostApiStudyData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiStudyResponses, PostApiStudyErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Study',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiStudyDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiStudyDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiStudyDraftResponses, PostApiStudyDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Study/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiStudyDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiStudyDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiStudyDraftByEntityIdResponses, GetApiStudyDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Study/draft/{entityId}',
        ...options
    });
};

export const putApiStudyDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiStudyDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiStudyDraftByEntityIdResponses, PutApiStudyDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Study/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiStudyDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiStudyDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiStudyDraftByEntityIdCommitResponses, PostApiStudyDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Study/draft/{entityId}/commit',
        ...options
    });
};

export const postApiStudyDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiStudyDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiStudyDraftValidateResponses, PostApiStudyDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Study/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiStudyDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiStudyDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiStudyDraftByEntityIdValidateResponses, PostApiStudyDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Study/draft/{entityId}/validate',
        ...options
    });
};

export const getApiTechnicianById = <ThrowOnError extends boolean = false>(options: Options<GetApiTechnicianByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiTechnicianByIdResponses, GetApiTechnicianByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Technician/{id}',
        ...options
    });
};

export const patchApiTechnicianById = <ThrowOnError extends boolean = false>(options: Options<PatchApiTechnicianByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiTechnicianByIdResponses, PatchApiTechnicianByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Technician/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiTechnicianById = <ThrowOnError extends boolean = false>(options: Options<PutApiTechnicianByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiTechnicianByIdResponses, PutApiTechnicianByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Technician/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiTechnicianSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiTechnicianSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiTechnicianSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Technician/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiTechnician = <ThrowOnError extends boolean = false>(options?: Options<GetApiTechnicianData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTechnicianResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Technician',
        ...options
    });
};

export const postApiTechnician = <ThrowOnError extends boolean = false>(options?: Options<PostApiTechnicianData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiTechnicianResponses, PostApiTechnicianErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Technician',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiTechnicianDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiTechnicianDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiTechnicianDraftResponses, PostApiTechnicianDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Technician/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiTechnicianDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiTechnicianDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiTechnicianDraftByEntityIdResponses, GetApiTechnicianDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Technician/draft/{entityId}',
        ...options
    });
};

export const putApiTechnicianDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiTechnicianDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiTechnicianDraftByEntityIdResponses, PutApiTechnicianDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Technician/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiTechnicianDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiTechnicianDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiTechnicianDraftByEntityIdCommitResponses, PostApiTechnicianDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Technician/draft/{entityId}/commit',
        ...options
    });
};

export const postApiTechnicianDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiTechnicianDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiTechnicianDraftValidateResponses, PostApiTechnicianDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Technician/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiTechnicianDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiTechnicianDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiTechnicianDraftByEntityIdValidateResponses, PostApiTechnicianDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Technician/draft/{entityId}/validate',
        ...options
    });
};

export const getApiTechnicianAppointmentById = <ThrowOnError extends boolean = false>(options: Options<GetApiTechnicianAppointmentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiTechnicianAppointmentByIdResponses, GetApiTechnicianAppointmentByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/TechnicianAppointment/{id}',
        ...options
    });
};

export const patchApiTechnicianAppointmentById = <ThrowOnError extends boolean = false>(options: Options<PatchApiTechnicianAppointmentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).patch<PatchApiTechnicianAppointmentByIdResponses, PatchApiTechnicianAppointmentByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/TechnicianAppointment/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const putApiTechnicianAppointmentById = <ThrowOnError extends boolean = false>(options: Options<PutApiTechnicianAppointmentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiTechnicianAppointmentByIdResponses, PutApiTechnicianAppointmentByIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/TechnicianAppointment/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiTechnicianAppointmentSearch = <ThrowOnError extends boolean = false>(options?: Options<PostApiTechnicianAppointmentSearchData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiTechnicianAppointmentSearchResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/TechnicianAppointment/search',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiTechnicianAppointment = <ThrowOnError extends boolean = false>(options?: Options<GetApiTechnicianAppointmentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTechnicianAppointmentResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/TechnicianAppointment',
        ...options
    });
};

export const postApiTechnicianAppointment = <ThrowOnError extends boolean = false>(options?: Options<PostApiTechnicianAppointmentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiTechnicianAppointmentResponses, PostApiTechnicianAppointmentErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/TechnicianAppointment',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiTechnicianAppointmentDraft = <ThrowOnError extends boolean = false>(options?: Options<PostApiTechnicianAppointmentDraftData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiTechnicianAppointmentDraftResponses, PostApiTechnicianAppointmentDraftErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/TechnicianAppointment/draft',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiTechnicianAppointmentDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<GetApiTechnicianAppointmentDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiTechnicianAppointmentDraftByEntityIdResponses, GetApiTechnicianAppointmentDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/TechnicianAppointment/draft/{entityId}',
        ...options
    });
};

export const putApiTechnicianAppointmentDraftByEntityId = <ThrowOnError extends boolean = false>(options: Options<PutApiTechnicianAppointmentDraftByEntityIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiTechnicianAppointmentDraftByEntityIdResponses, PutApiTechnicianAppointmentDraftByEntityIdErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/TechnicianAppointment/draft/{entityId}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiTechnicianAppointmentDraftByEntityIdCommit = <ThrowOnError extends boolean = false>(options: Options<PostApiTechnicianAppointmentDraftByEntityIdCommitData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiTechnicianAppointmentDraftByEntityIdCommitResponses, PostApiTechnicianAppointmentDraftByEntityIdCommitErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/TechnicianAppointment/draft/{entityId}/commit',
        ...options
    });
};

export const postApiTechnicianAppointmentDraftValidate = <ThrowOnError extends boolean = false>(options?: Options<PostApiTechnicianAppointmentDraftValidateData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiTechnicianAppointmentDraftValidateResponses, PostApiTechnicianAppointmentDraftValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/TechnicianAppointment/draft/validate',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiTechnicianAppointmentDraftByEntityIdValidate = <ThrowOnError extends boolean = false>(options: Options<PostApiTechnicianAppointmentDraftByEntityIdValidateData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiTechnicianAppointmentDraftByEntityIdValidateResponses, PostApiTechnicianAppointmentDraftByEntityIdValidateErrors, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/TechnicianAppointment/draft/{entityId}/validate',
        ...options
    });
};

export const getApiWorkflowFlows = <ThrowOnError extends boolean = false>(options?: Options<GetApiWorkflowFlowsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiWorkflowFlowsResponses, unknown, ThrowOnError>({
        responseType: 'json',
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Workflow/flows',
        ...options
    });
};

export const getApiWorkflowMyTasks = <ThrowOnError extends boolean = false>(options?: Options<GetApiWorkflowMyTasksData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiWorkflowMyTasksResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Workflow/myTasks',
        ...options
    });
};

export const getApiWorkflowProfile = <ThrowOnError extends boolean = false>(options?: Options<GetApiWorkflowProfileData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiWorkflowProfileResponses, unknown, ThrowOnError>({
        security: [
            {
                scheme: 'bearer',
                type: 'http'
            }
        ],
        url: '/api/Workflow/profile',
        ...options
    });
};