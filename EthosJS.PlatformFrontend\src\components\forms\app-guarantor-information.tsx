
import SectionHeader from "@components/section-header";
import { withForm } from "@hooks/app-form";
import { Autocomplete, Grid2 as Grid, TextField, } from "@mui/material";

const GuarantorInformation = withForm({
   render: function Render({ form }) {
      return (
         <Grid container spacing={2}>
            <Grid size={12} >
               <SectionHeader title='Patient Information' />
            </Grid>
            <Grid size={{
               xs: 12,
               sm: 4,
            }}>
               <form.AppField name="patientInformation.prefix" children={({ state, handleChange, handleBlur }) => {
                  return (
                     <Autocomplete
                        options={['Mr.', 'Mrs.', 'Ms.', 'Dr.']}
                        value={state.value}
                        onChange={(_event, newValue) =>
                           handleChange(newValue as string)
                        }
                        onBlur={handleBlur}
                        renderInput={(params) => (
                           <TextField
                              {...params}
                              label="Prefix"
                              fullWidth
                           />
                        )}
                     />
                  )
               }} />
            </Grid>
            <Grid size={{ xs: 0, sm: 8 }}></Grid>
            <Grid size={{
               xs: 12,
               sm: 4,
            }}>
               <form.AppField name="patientInformation.firstName" children={(field) => <field.AppTextField label="Legal First Name" required />} />
            </Grid>
            <Grid size={{ xs: 12, sm: 4 }}>
               <form.AppField name="patientInformation.middleName" children={(field) => <field.AppTextField label="Middle name or initial" />} />
            </Grid>
            <Grid size={{ xs: 12, sm: 4 }}>
               <form.AppField name="patientInformation.lastName" children={(field) => <field.AppTextField label="Legal Last Name" required />} />
            </Grid>
            <Grid size={{ xs: 12, sm: 4 }}>
               <form.AppField name="patientInformation.suffix" children={({ state, handleChange, handleBlur, getMeta }) => {
                  return (
                     <Autocomplete
                        options={['Jr.', 'Sr.', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X']}
                        value={state.value}
                        onChange={(_event, newValue) =>
                           handleChange(newValue ?? '')
                        }
                        onBlur={handleBlur}
                        renderInput={(params) => (
                           <TextField
                              {...params}
                              label="Suffix"
                              fullWidth
                           />
                        )}
                     />
                  )
               }
               } />
            </Grid>
            <Grid size={{ xs: 0, sm: 8 }}></Grid>
            <Grid size={{ xs: 12, md: 6 }}>
               <form.AppField name="patientInformation.ssn" children={(field) => <field.AppSsnField label={'Social Security number'} required />} />
            </Grid>
         </Grid>
      )
   }
})

export default GuarantorInformation;
