import {
  EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddStudy,
  EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddCareLocation,
  EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddPhysicians,
  EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_ReviewAndSubmitOrder
} from '@client/workflows';

export type AddStudy = EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddStudy;
export type AddCareLocation = EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddCareLocation;
export type AddPhysicians = EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_AddPhysicians;
export type ReviewAndSubmitOrder =
  EthosWorkflowsWorkflowAddNewOrderIAddNewOrderRequest_ReviewAndSubmitOrder;
