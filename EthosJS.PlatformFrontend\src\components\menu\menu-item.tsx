import { ComponentType, ReactElement, SVGProps } from 'react';

import {
	darken,
	lighten,
	ListItemButton,
	ListItemButtonProps,
	ListItemIcon,
	ListItemText,
	PaletteColor,
	styled,
	Theme,
} from '@mui/material';

import { LockIcon } from 'lucide-react';
import { Status } from '@config/status';

interface IMenuItem extends ListItemButtonProps {
	title: string;
	value?: string;
	subTitle?: string;
	icon?: ComponentType<SVGProps<SVGSVGElement>>;
	isDisabled?: boolean;
	disabledIcon?: ReactElement;
	status?: Status;
	size?: 'medium' | 'large';
	onClick?: () => void;
}

const STATUS_PALLETE_MAP: Record<Status, (theme: Theme) => PaletteColor> = {
	success: (theme) => theme.palette.success,
	warning: (theme) => theme.palette.warning,
	error: (theme) => theme.palette.error,
	notStarted: (theme) => ({
		main: theme.palette.grey[500],
		contrastText: theme.palette.common.white,
		light: theme.palette.grey[500],
		dark: theme.palette.grey[500],
	}),
	editing: (theme) => theme.palette.primary,
	process: (theme) => theme.palette.primary,
	default: (theme) => theme.palette.primary,
};

export const StatusListItemButton = styled(ListItemButton, {
	shouldForwardProp: (prop) => prop !== 'status',
})<{ status: Status; size: 'medium' | 'large' }>(({ theme, status, size }) => {
	const palette = STATUS_PALLETE_MAP[status](theme);

	return {
		backgroundColor: lighten(palette.light, 0.95),
		color: darken(palette.dark, 0.4),
		padding: 0,
		minHeight: size === 'medium' ? 44 : 52,
		borderRadius: `${theme.shape.borderRadius}px`,
		border: `1px solid ${lighten(palette.light, 0.8)}`,
		overflow: 'hidden',
		userSelect: 'none',
		gap: theme.spacing(1.5),
		svg: {
			width: size === 'medium' ? '1.25rem' : '1.5rem',
			height: size === 'medium' ? '1.25rem' : '1.5rem',
		},
		'& .leading-icon': {
			backgroundColor: lighten(palette.light, 0.1),
			color: palette.contrastText,
		},
		'&:hover, &.Mui-selected': {
			borderColor: darken(palette.main, 0.1),
			backgroundColor: lighten(palette.light, 0.95),
			borderWidth: 1.5,
			'& .leading-icon': {
				backgroundColor: darken(palette.main, 0.1),
				color: palette.contrastText,
			},
		},
		':not(:last-child)': {
			marginBottom: theme.spacing(0.85),
		},
	};
});

const StyledIcon = styled(ListItemIcon)(({ theme }) => ({
	color: theme.palette.grey[800],
	display: 'flex',
	alignItems: 'center',
	alignSelf: 'normal',
	width: theme.spacing(5.5),
	justifyContent: 'center',
	svg: {
		width: '1.25rem',
		height: '1.25rem',
	},
}));

const StyledText = styled(ListItemText)(() => ({
	flex: 1,
	overflow: 'hidden',
	textOverflow: 'ellipsis',
	whiteSpace: 'nowrap',
	height: '100%',
}));

const MenuItem: React.FC<IMenuItem> = (props) => {
	const {
		title,
		subTitle,
		icon: IconProp,
		disabledIcon: disabledIconProp,
		status = Status.Default,
		size = 'medium',
		disabled = false,
		...rest
	} = props;

	const disabledIcon = disabledIconProp ?? <LockIcon />;

	return (
		<StatusListItemButton
			status={status}
			size={size}
			disabled={disabled}
			{...rest}>
			{IconProp && (
				<StyledIcon className="leading-icon">
					<IconProp />
				</StyledIcon>
			)}
			<StyledText
				primary={title}
				secondary={subTitle}
				slotProps={{
					primary: {
						variant: 'subtitle2',
						sx: { fontWeight: 500 },
					},
					secondary: {
						variant: 'asapCondensed',
						sx: { fontWeight: 400, lineHeight: 1, fontSize: '0.75rem', color: 'inherit' },
					},
				}}
			/>
			{disabled && <StyledIcon>{disabledIcon}</StyledIcon>}
		</StatusListItemButton>
	);
};

export type { IMenuItem };

export default MenuItem;
