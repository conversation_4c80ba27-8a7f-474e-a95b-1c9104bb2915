import MainCardContainer from '@components/main-container/main-card-container';
import { AppFormType, withForm } from '@hooks/app-form';
import { AccessibilityNewOutlined } from '@mui/icons-material';
import { Grid2 as Grid } from '@mui/material';
import { formOptions, useStore } from '@tanstack/react-form';
import { formHasErrors } from '@utils/forms';

const options = formOptions({
	defaultValues: {
		physicalMeasurements: {
			heightInches: '',
			weightPounds: '',
			neckSize: '',
			bmi: 0,
		},
	},
});

const PhysicalMeasurementsForm = withForm({
	...options,
	render: function Render({ form }) {
		const { AppField: Field, setFieldValue, getFieldValue, setFieldMeta, getFieldMeta } = form;

		const { bmi } = useStore(form.store, ({ values }) => {
			return {
				bmi: values.physicalMeasurements?.bmi,
			};
		});

		const hasErrors = formHasErrors(form as AppFormType, 'physicalMeasurements');

		return (
			<MainCardContainer
				title="Physical Measurements"
				icon={<AccessibilityNewOutlined />}
				color={hasErrors ? 'error' : 'primary'}
				emphasis={hasErrors ? 'high' : 'low'}
				secondaryActionType="Chips"
				chipLabels={[{ label: `BMI: ${bmi ? bmi : '00.0'}`, icon: <AccessibilityNewOutlined /> }]}>
				<Grid
					container
					spacing={2}>
					<Grid size={{ xs: 12, md: 4 }}>
						<Field
							name="physicalMeasurements.heightInches"
							listeners={{
								onChange: ({ value }) => {
									let height: string | number = value;
									let weight: string | number = getFieldValue('physicalMeasurements.weightPounds');
									if (typeof height === 'string') {
										height = parseFloat(height);
									}
									if (typeof weight === 'string') {
										weight = parseFloat(weight);
									}
									if (isNaN(height) || isNaN(weight)) {
										setFieldValue('physicalMeasurements.bmi', 0);
									} else {
										const bmi = (weight / (height * height)) * 703;
										setFieldValue('physicalMeasurements.bmi', Number(bmi.toFixed(2)));
									}
									const fieldMeta = getFieldMeta('physicalMeasurements.bmi')!;
									setFieldMeta('physicalMeasurements.bmi', {
										...fieldMeta,
										errorMap: {},
									});
								},
							}}
							children={(field) => (
								<field.AppTextField
									label="Height (in) *"
									inputMode="numeric"
									data-testid="physicalMeasurements.heightInches"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, md: 4 }}>
						<Field
							name="physicalMeasurements.weightPounds"
							listeners={{
								onChange: ({ value }) => {
									let weight: string | number = value;
									let height: string | number = getFieldValue('physicalMeasurements.heightInches');
									if (typeof height === 'string') {
										height = parseFloat(height);
									}
									if (typeof weight === 'string') {
										weight = parseFloat(weight);
									}
									if (isNaN(height) || isNaN(weight)) {
										setFieldValue('physicalMeasurements.bmi', 0);
									} else {
										const bmi = (weight / (height * height)) * 703;
										setFieldValue('physicalMeasurements.bmi', Number(bmi.toFixed(2)));
									}
									const fieldMeta = getFieldMeta('physicalMeasurements.bmi')!;
									setFieldMeta('physicalMeasurements.bmi', {
										...fieldMeta,
										errorMap: {},
									});
								},
							}}
							children={(field) => (
								<field.AppTextField
									label="Weight (lbs) *"
									inputMode="numeric"
									data-testid="physicalMeasurements.weightPounds"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, md: 4 }}>
						<Field
							name="physicalMeasurements.neckSize"
							children={(field) => (
								<field.AppTextField
									label="Neck Size (in)"
									inputMode="numeric"
									data-testid="physicalMeasurements.neckSize"
								/>
							)}
						/>
					</Grid>
				</Grid>
			</MainCardContainer>
		);
	},
});

export default PhysicalMeasurementsForm;
