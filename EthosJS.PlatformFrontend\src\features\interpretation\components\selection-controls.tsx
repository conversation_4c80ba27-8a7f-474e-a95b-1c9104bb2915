import { PatientCreate, PatientRead } from '@auth/scopes';
import { postApiStudySearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import { Autocomplete, Stack, TextField } from '@mui/material';
import { useQuery } from '@tanstack/react-query';

interface SelectionControlsProps {
	patientId: string;
	orderId: string;
	studyId?: string;
	onSelect: (selectedIds: { orderId: string; studyId?: string }) => void;
}

export default function SelectionControls({
	patientId,
	orderId,
	studyId,
	onSelect,
}: SelectionControlsProps) {
	const { data: studies } = useQuery({
		...postApiStudySearchOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		}),
		enabled: !!patientId,
	});

	const visitDate =  [
		{
			label: '01/01/2025 — 11AM', value: '1'
		}
	]
	const onChangeOrderIdField = (requestedValues: string) => {
		onSelect?.({ orderId: requestedValues, studyId });
	};

	const onChangeStudyIdField = (requestedValues: string) => {
		onSelect?.({ orderId, studyId: requestedValues });
	};

	return (
		<Stack
			direction="column"
			gap={2}
			sx={{ overflow: 'auto', py: 2 }}>
			<Autocomplete
				disablePortal
				sx={{ minWidth: 200 }}
				value={orderId}
				onChange={(_, newValue) => {
					onChangeOrderIdField(newValue);
				}}
				defaultValue={orderId}
				disableClearable
				renderInput={(params) => (
					<TextField
						{...params}
						label="Order Id"
					/>
				)}
				options={[orderId] as string[]}
				filterOptions={(x) => x}
			/>
			<Autocomplete
				disablePortal
				sx={{ minWidth: 200 }}
				value={studyId ?? null}
				onChange={(_, newValue) => {
					onChangeStudyIdField(newValue ?? '');
				}}
				renderInput={(params) => (
					<TextField
						{...params}
						label="Study Id"
					/>
				)}
				options={studies?.items?.map((item) => item.id) ?? []}
				filterOptions={(x) => x}
			/>
			<Autocomplete
				disablePortal
				sx={{ minWidth: 200 }}
				value={studyId ?? null}
				onChange={(_, newValue) => {
					onChangeStudyIdField(newValue ?? '');
				}}
				renderInput={(params) => (
					<TextField
						{...params}
						label="Visit Date"
					/>
				)}
				options={visitDate?.map((item) => item.label) ?? []}
				filterOptions={(x) => x}
			/>
		</Stack>
	);
}
