import { CircularProgress, Box, Typography, styled, Color, Theme } from "@mui/material";
import { Palette } from "@mui/material/styles";


type CircularProgressProps = {
    type?: 'background' | 'foreground';
}

const StyledCircularProgress = styled(CircularProgress, { shouldForwardProp: prop => prop !== 'type' })<CircularProgressProps>(({ theme, type }) => ({
    "&.MuiCircularProgress-root": {
        transform: 'rotate(180deg) !important',
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        color: type === 'background' ? theme.palette.grey[theme.palette.mode === 'light' ? 200 : 800] + '!important' : undefined,
    },
}));

type AllowedColors = 'inherit' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'
type HalfCircleProgressProps = {
    value: number;           // 0 - 100
    size?: number;           // diameter in px
    thickness?: number;      // thickness of the circular track
    color?: AllowedColors
};

export default function HalfCircleProgress({ value, size = 50, thickness = 3, color = 'primary' }: HalfCircleProgressProps) {
    return (
        <Box sx={{ position: 'relative', display: 'inline-block', overflow: 'clip', width: size, height: size / 2, color: color !== 'inherit' ? `${color}.main` : undefined }}>
            <StyledCircularProgress
                variant="determinate"
                value={50}
                size={size}
                thickness={thickness}
                type="background"
            />
            <StyledCircularProgress
                variant="determinate"
                value={value / 2}
                size={size}
                thickness={thickness}
                type="foreground"
                color={color}
            />
            <Box
                sx={{
                    left: 0,
                    bottom: 0,
                    right: 0,
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'flex-end',
                    justifyContent: 'center',
                }}
            >
                <Typography
                    variant="caption"
                    component="span"
                    sx={{ lineHeight: 1, fontSize: 12, }}
                >{`${Math.round(value)}%`}</Typography>
            </Box>
        </Box>
    );
};
