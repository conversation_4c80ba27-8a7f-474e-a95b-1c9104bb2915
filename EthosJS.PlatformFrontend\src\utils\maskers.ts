function maskSocialSecurity(ssn: string): string {
  // Return empty string if ssn is falsy
  if (!ssn) {
    return '';
  }
  // Remove any non-digit characters
  const digits = ssn.replace(/\D/g, '');

  // Format based on length
  if (digits.length <= 3) {
    return '*'.repeat(digits.length);
  } else if (digits.length <= 5) {
    return '*'.repeat(3) + '-' + '*'.repeat(digits.length - 3);
  } else if (digits.length <= 8) {
    return '*'.repeat(3) + '-' + '*'.repeat(2) + '-' + '*'.repeat(digits.length - 5);
  }
  // Full SSN
  return '***-**-' + digits.slice(-4);
}

function maskPhoneNumber(phone: string): string {
  // Remove any non-digit characters from the phone number
  if (!phone) {
    return '';
  }
  const digits = phone.replace(/\D/g, '');

  // Format based on the number of digits entered
  if (digits.length === 0) {
    return '';
  } else if (digits.length <= 3) {
    return `(${digits}`;
  } else if (digits.length <= 6) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
  }
  // Format as (************* for 7-10 digits
  return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
}

function digitsOnly(value: string): string {
  if (!value) return '';
  return value.replace(/\D/g, '');
}

export { maskSocialSecurity, maskPhoneNumber, digitsOnly };
