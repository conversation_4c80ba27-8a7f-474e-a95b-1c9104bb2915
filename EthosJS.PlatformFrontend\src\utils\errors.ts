import { AxiosError } from 'axios';
import { MicrosoftAspNetCoreMvcProblemDetails } from '@client/workflows';

type NormalizedProblemDetails = MicrosoftAspNetCoreMvcProblemDetails & {
	detail: string;
	title: string;
	status: number;
};

export function extractProblemDetails(
	error: AxiosError<MicrosoftAspNetCoreMvcProblemDetails | Error>
): NormalizedProblemDetails {
	if (error.response?.data) {
		try {
			const data = error.response.data;
			const processedData: MicrosoftAspNetCoreMvcProblemDetails =
				typeof data === 'string' ? JSON.parse(data) : data;
			return {
				...processedData,
				detail: processedData.detail ?? 'Unknown error',
				title: processedData.title ?? 'Error',
				status: processedData.status ?? error.response?.status ?? 500,
			};
		} catch (e) {
			console.error('Error processing response data', e);
			return {
				detail: error.message || 'Error processing response data',
				status: error.response?.status || 500,
				title: error.name || 'Error',
			};
		}
	}
	return {
		detail: error.message || 'Unknown error',
		status: error.response?.status || 500,
		title: error.name || 'Error',
	};
}
