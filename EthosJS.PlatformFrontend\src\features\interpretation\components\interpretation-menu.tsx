import SecondaryMenu from "@components/menu/secondary-menu";
import MenuItem from '@components/menu/menu-item';
import { Calendar, User } from "lucide-react";
import { Status } from "@config/status";
import SelectionControls from "./selection-controls";

const menuItems = [
   {
      stateKey: 'review',
      title: 'Review',
      value: 'appointment-creation',
      icon: User,
      size: 'medium',
      path: '/review',
   },
   {
      stateKey: 'interpret',
      title: 'Interpret',
      value: 'follow-up-call',
      icon: User,
      size: 'medium',
      path: '/interpret',
   },
] as const;

interface InterPretationMenuProps {
   activePath: string
   onSelectMenu: (selectedPath: string) => void
   studyId: string
   patientId: string
   orderId: string
   onSelect: (selectedIds: { orderId: string, studyId?: string }) => void
}

const InterPretationMenu = (props: InterPretationMenuProps) => {

   const { studyId, patientId, orderId, onSelect, activePath, onSelectMenu } = props;

   return (
      <SecondaryMenu
         headerProps={{
            title: 'Interpretation',
            subtitle: undefined,
            icon: Calendar,
            showIcon: true,
            type: 'Workflow',
            progress: 0,
            color: 'success',
            description: 'Select Order ID, Study and Visit Date',
         }}
         topContainer={(
            <SelectionControls
               studyId={studyId}
               patientId={patientId}
               orderId={orderId}
               onSelect={onSelect}
            />
         )}
      >
         {menuItems.map((item) => (
            <MenuItem
               key={item.value}
               title={item.title}
               value={item.value}
               icon={item.icon}
               size={item.size}
               status={Status.NotStarted}
               selected={activePath.includes(item.path)}
               onClick={() => {
                  onSelectMenu?.(item.path);
               }}
            />
         ))}
      </SecondaryMenu>
   )
}

export default InterPretationMenu;