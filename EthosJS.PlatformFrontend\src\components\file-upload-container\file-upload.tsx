import {
  <PERSON><PERSON>,
  <PERSON>,
  CircularProgress,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Typography,
  lighten
} from '@mui/material';
import { UploadFile, Delete as DeleteIcon, CheckCircle } from '@mui/icons-material';
import { FileText } from 'lucide-react';
import { useRef, useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import {
  postApiFileRequestUploadTokenMutation,
  postApiFileUploadMutation
} from '@client/workflows/@tanstack/react-query.gen';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { UploadGuidelines } from './file-upload-guidelines';

export interface FileUploadProps {
  workflowId: string;
  onFileUpload: (fileId: string, fileName: string, fileSize: number) => void;
  allowMultiple?: boolean;
}

interface UploadingFile {
  file: File;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  fileId?: string;
  error?: string;
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`;
};

export default function FileUpload({
  workflowId,
  onFileUpload,
  allowMultiple = false
}: FileUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [isRequestingToken, setIsRequestingToken] = useState(false);

  const {
    mutateAsync: requestToken,
    isError: isTokenError,
    error: tokenError
  } = useMutation(
    postApiFileRequestUploadTokenMutation({
      scopes: [PatientCreate.value, PatientRead.value]
    })
  );

  const { mutateAsync: uploadFile } = useMutation(
    postApiFileUploadMutation({
      scopes: [PatientCreate.value, PatientRead.value]
    })
  );

  const triggerFileInput = () => {
    if (!isRequestingToken) {
      fileInputRef.current?.click();
    }
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (!files.length) return;

    const newUploadingFiles: UploadingFile[] = files.map((file) => ({
      file,
      progress: 0,
      status: 'uploading'
    }));

    setUploadingFiles((prev) => [...prev, ...newUploadingFiles]);

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileIndex = uploadingFiles.length + i;

      try {
        setIsRequestingToken(true);
        const tokenResponse = await requestToken({
          body: {
            contextEntityType: 'Workflow',
            contextEntityId: workflowId,
            purpose: 'Workflow'
          }
        });
        setIsRequestingToken(false);

        const { uploadToken, uploadUrl } = JSON.parse(tokenResponse as string);

        if (uploadToken && uploadUrl) {
          const fileData = new FormData();
          fileData.append('file', file, file.name);

          const uploadResponse = await uploadFile({
            body: fileData as any,
            headers: { 'Upload-Token': uploadToken },
            onUploadProgress: (e) => {
              const percent = Math.round((e.loaded * 100) / (e.total || 1));
              setUploadingFiles((prev) =>
                prev.map((f, index) => (index === fileIndex ? { ...f, progress: percent } : f))
              );
            }
          });

          const result = JSON.parse(uploadResponse as string);

          if (result?.fileId) {
            setUploadingFiles((prev) =>
              prev.map((f, index) =>
                index === fileIndex ? { ...f, status: 'completed', fileId: result.fileId } : f
              )
            );
            onFileUpload(result.fileId, file.name, file.size);
          }
        }
      } catch (error) {
        console.error('Upload error:', error);
        setUploadingFiles((prev) =>
          prev.map((f, index) =>
            index === fileIndex ? { ...f, status: 'error', error: 'Upload failed' } : f
          )
        );
        setIsRequestingToken(false);
      }
    }

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (isRequestingToken) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const event = { target: { files } } as React.ChangeEvent<HTMLInputElement>;
      handleFileChange(event);
    }
  };

  const removeFile = (index: number) => {
    setUploadingFiles((prev) => prev.filter((_, i) => i !== index));
  };

  return (
    <Box>
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf,.jpg,.jpeg,.png"
        multiple={allowMultiple}
        style={{ display: 'none' }}
        onChange={handleFileChange}
      />

      {isTokenError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {tokenError?.message || 'Failed to request upload token'}
        </Alert>
      )}

      {uploadingFiles.length === 0 && (
        <Box display="flex" flexDirection="column" gap={2}>
          <UploadGuidelines />
          <Box
            onClick={triggerFileInput}
            onDragOver={(e) => e.preventDefault()}
            onDrop={handleDrop}
            sx={(theme) => ({
              border: '2px dashed',
              borderColor: 'primary.main',
              borderRadius: 1,
              p: 3,
              backgroundColor: 'primary.lighter',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: isRequestingToken ? 'not-allowed' : 'pointer',
              opacity: isTokenError ? 0.6 : 1,
              pointerEvents: isTokenError ? 'none' : 'auto',
              transition: 'background-color 0.2s ease',
              '&:hover': {
                backgroundColor: lighten(theme.palette.primary.light, 0.85),
                opacity: isRequestingToken ? 1 : 0.9
              },
              position: 'relative'
            })}>
            {isRequestingToken && (
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: 'rgba(255, 255, 255, 0.7)',
                  zIndex: 1
                }}>
                <CircularProgress size={40} />
                <Typography variant="body2" sx={{ mt: 1 }}>
                  Preparing upload...
                </Typography>
              </Box>
            )}
            <UploadFile fontSize="large" color="primary" />
            <Box sx={{ mt: 1, textAlign: 'center' }}>
              <Typography fontWeight="bold" mb={0.5}>
                Drag and drop your {allowMultiple ? 'files' : 'file'} here
              </Typography>
              <Typography variant="body2">
                or{' '}
                <Box component="span" color="primary.main" sx={{ textDecoration: 'underline' }}>
                  browse
                </Box>{' '}
                to choose {allowMultiple ? 'files' : 'a file'}
              </Typography>
              <Typography variant="caption" mt={1} color="text.secondary">
                Supported formats: PDF, JPG, JPEG, PNG
                {allowMultiple && ' • Multiple files allowed'}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}

      {uploadingFiles.length > 0 && (
        <Stack gap={1}>
          {uploadingFiles.map((uploadFile, index) => (
            <Box
              key={index}
              display="flex"
              alignItems="center"
              justifyContent="space-between"
              gap={2}>
              <Box display="flex" alignItems="center" gap={2}>
                <FileText color="#5E35B1" />
                <Box>
                  <Typography variant="subtitle1" color="black">
                    {uploadFile.file.name}
                  </Typography>
                  <Typography variant="body2">
                    {formatFileSize(uploadFile.file.size)}{' '}
                    {uploadFile.status === 'completed' && '• completed'}
                    {uploadFile.status === 'error' && `• ${uploadFile.error}`}
                  </Typography>
                </Box>
              </Box>
              <Box display="flex" alignItems="center" gap={2}>
                <IconButton onClick={() => removeFile(index)} size="small" color="default">
                  <DeleteIcon />
                </IconButton>
                {uploadFile.status === 'completed' && (
                  <CheckCircle color="success" sx={{ height: 24, width: 24 }} />
                )}
              </Box>
            </Box>
          ))}
        </Stack>
      )}
    </Box>
  );
}
