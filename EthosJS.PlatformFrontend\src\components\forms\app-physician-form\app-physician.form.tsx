import { useEffect, useState } from 'react';
import { <PERSON>ack, TextField, Box, CircularProgress, Card, CardContent } from '@mui/material';
import { UserCircle } from 'lucide-react';

import MainCardContainer from '@components/main-container/main-card-container';
import RadioItem from '@components/radio-item';
import ChipSummary from '@components/chip-summary';
import InfoSection from '@components/info-section';

export interface AppPhysicianFormProps {
	title: string;
	attentionTitle?: string;
	isOptional?: boolean;
	value: string | null;
	options?: Array<{ title: string; id: string; description: string; meta: Record<string, string> }>;
	onSelect?: (selectedId: string | null) => void;
	onAdd: (selectedId: string | null) => void;
	onCancel?: () => void;
	onDelete?: () => void;
	searchTerm?: string;
	onSearchChange?: (searchTerm: string) => void;
	searchLoading?: boolean;
}

export default function AppPhysicianForm({
	title,
	attentionTitle,
	value,
	isOptional = false,
	onAdd,
	onCancel,
	options = [],
	searchTerm,
	onSearchChange,
	searchLoading,
}: AppPhysicianFormProps) {
	const [selectedId, setSelectedId] = useState<string | null>(value);
	const [readMode, setReadMode] = useState<boolean>(!!value);
	const [expanded, setExpanded] = useState<boolean>(false);

	const handlePrimaryAction = () => {
		if (value && !expanded) {
			setReadMode(false);
			setExpanded(true);
		} else {
			setExpanded((prev) => !prev);
		}
	};

	useEffect(() => {
		setSelectedId(value);
	}, [value]);

	const handleSelect = (id: string) => {
		const newSelected = id === selectedId ? null : id;
		setSelectedId(newSelected);
	};

	const handleAdd = () => {
		onAdd(selectedId);
		setReadMode(true);
		setExpanded(false);
	};

	const handleCancel = () => {
		setSelectedId(value);
		setReadMode(false);
		setExpanded(false);
	};

	const handleDelete = () => {
		onCancel?.();
		setReadMode(false);
		setExpanded(false);
	};

	const selectedPhysician = options.find((opt) => opt.id === selectedId);

	return (
		<MainCardContainer
			title={title}
			icon={<UserCircle size={24} />}
			emphasis={selectedPhysician ? 'high' : 'low'}
			color="primary"
			primaryActionType={expanded ? 'Delete' : value ? 'Edit' : 'Add'}
			onPrimaryAction={handlePrimaryAction}
			footerProps={
				expanded
					? {
							primaryButton1: !readMode
								? {
										label: value ? 'Update' : 'Add',
										onClick: handleAdd,
										disabled: !selectedId,
									}
								: undefined,
							primaryButton2: !readMode
								? {
										label: 'Cancel',
										onClick: handleCancel,
									}
								: undefined,
							secondaryButton1: value
								? {
										label: 'Delete',
										onClick: handleDelete,
									}
								: undefined,
						}
					: undefined
			}>
			{expanded ? (
				<Card sx={{ position: 'relative' }}>
					<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
						{readMode ? (
							<></>
						) : (
							<>
								<TextField
									placeholder="Search Physician"
									required={!isOptional}
									fullWidth
									value={searchTerm}
									onChange={(e) => onSearchChange?.(e.target.value)}
								/>
								{attentionTitle && <InfoSection title={attentionTitle} />}
								{searchLoading ? (
									<Box sx={{ py: 2, display: 'flex', justifyContent: 'center' }}>
										<CircularProgress />
									</Box>
								) : (
									<Stack gap={2}>
										{options.map((option) => (
											<RadioItem
												key={option.id}
												item={option}
												isMultiple={false}
												onClick={() => handleSelect(option.id)}
												isSelected={option.id === selectedId}
											/>
										))}
									</Stack>
								)}
							</>
						)}
					</CardContent>
				</Card>
			) : (
				value && (
					<Box>
						<ChipSummary
							items={[
								{
									label: 'Name',
									value: selectedPhysician?.title ?? '',
								},
								{
									label: 'NPI',
									value: selectedPhysician?.meta?.npi ?? '',
								},
								{
									label: 'Specialty',
									value: selectedPhysician?.description ?? '',
								},
							]}
						/>
					</Box>
				)
			)}
		</MainCardContainer>
	);
}
