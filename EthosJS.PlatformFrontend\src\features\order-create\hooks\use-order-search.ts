import { EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null } from '@client/workflows';
import { postApiOrderSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import { useQuery } from '@tanstack/react-query';
import { OrderQuery, Query } from '@utils/query-dsl';

export default function useOrderSearch({ patientId }: { patientId: string }) {
	const orderQuery = useQuery({
		...postApiOrderSearchOptions({
			responseType: 'json',
			body: Query.literal(
				OrderQuery.withPatientId(patientId)
			) as unknown as EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!patientId,
		initialData: {
			items: [],
		},
		select: (data) => data?.items ?? [],
	});

	return orderQuery;
}
