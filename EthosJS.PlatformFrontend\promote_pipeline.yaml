# promote-pipeline.yml - Separate pipeline for promoting releases
name: EthosJS.PlatformFrontend Promote Release Pipeline

trigger: none  # Manual trigger only

pool:
  name: 'Default'

variables:
  - name: envFilePathDev
    value: '$(Build.SourcesDirectory)/EthosJS.PlatformFrontend/external-assets/k8s-manifests/.env-dev'
  - name: envFilePath 
    value: '$(Build.SourcesDirectory)/EthosJS.PlatformFrontend/external-assets/k8s-manifests/.env-promote'
  - name: acrName
    value: 'ethoscrdev.azurecr.io'

stages:

  - stage: Build
    displayName: "Build and Test Projects"
    jobs:
      - job: Build_Ethos_Workflows
        displayName: "Build and Test Ethos.Workflows"
        # condition: contains(variables['Build.ChangedFiles'], 'EthosJS.PlatformFrontend/')
        steps:
          - script: |
              echo "Reading tag from .env-dev file..."
              
              # Check if .env-dev file exists
              if [ ! -f "EthosJS.PlatformFrontend/external-assets/k8s-manifests/.env-dev" ]; then
                echo ".env-dev file not found. Using default tag for first-time setup."
                echo "##vso[task.setvariable variable=tag]2025.05.2"
              else
                # Read TAG from .env-dev file
                TAG_VALUE=$(grep "^TAG=" EthosJS.PlatformFrontend/external-assets/k8s-manifests/.env-dev | cut -d'=' -f2 | tr -d '"' | tr -d "'")
              
                if [ -z "$TAG_VALUE" ]; then
                  echo "TAG not found in .env-dev file. Using default tag."
                  echo "##vso[task.setvariable variable=tag]2025.05.2"
                else
                  echo "Tag found: $TAG_VALUE"
                  echo "##vso[task.setvariable variable=tag]$TAG_VALUE"
                fi
              fi
              
              echo "Using tag: $(tag)"
            displayName: 'Read Tag from .env-dev File'

          - script: |
              echo "Building Docker image with tag: $(tag)"
            displayName: 'Display Tag Information'

          - task: Docker@2
            displayName: Build EthosJS.PlatformFrontend Image for dev
            inputs:
              command: build
              containerRegistry: 'acrdev-service-connection'
              repository: 'ethosjs-platformfrontend'
              dockerfile: './EthosJS.PlatformFrontend/Dockerfile'
              buildContext: './EthosJS.PlatformFrontend'
              arguments: '--build-arg ENV_NAME=dev' 
              tags: |
                $(tag).dev

          - task: Docker@2
            displayName: Build EthosJS.PlatformFrontend Image for test
            inputs:
              command: build
              containerRegistry: 'acrdev-service-connection'
              repository: 'ethosjs-platformfrontend'
              dockerfile: './EthosJS.PlatformFrontend/Dockerfile'
              buildContext: './EthosJS.PlatformFrontend'
              arguments: '--build-arg ENV_NAME=test' 
              tags: |
                $(tag).test

          - task: Docker@2
            displayName: Build EthosJS.PlatformFrontend Image for uat
            inputs:
              command: build
              containerRegistry: 'acrdev-service-connection'
              repository: 'ethosjs-platformfrontend'
              dockerfile: './EthosJS.PlatformFrontend/Dockerfile'
              buildContext: './EthosJS.PlatformFrontend'
              arguments: '--build-arg ENV_NAME=uat' 
              tags: |
                $(tag).uat

          - task: Docker@2
            condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
            displayName: Push EthosJS.PlatformFrontend Image
            inputs:
              command: push
              containerRegistry: 'acrdev-service-connection'
              repository: 'ethosjs-platformfrontend'
              tags: |
                $(tag).dev
                $(tag).test
                $(tag).uat

  - stage: Promote
    displayName: "Generate and Update Release Tag"
    jobs:
      - job: UpdateTag
        displayName: "Update Release Tag in .env-promote file"
        steps:
          - checkout: self
            persistCredentials: true
            displayName: 'Checkout Source Code'

          - script: |
              echo "Generating new release tag..."
              
              # Get current year and month
              CURRENT_YEAR=$(date +%Y)
              CURRENT_MONTH=$(date +%m)
              
              # Check if .env-promote file exists
              if [ ! -f "$(envFilePath)" ]; then
                echo "Creating new .env-promote file..."
                mkdir -p EthosJS.PlatformFrontend/External-assets/k8s-manifests
                echo "# Auto-generated environment file" > $(envFilePath)
                echo "# This file is updated automatically by the promote pipeline" >> $(envFilePath)
                echo "# DO NOT EDIT MANUALLY" >> $(envFilePath)
                echo "" >> $(envFilePath)
                echo "TAG=${CURRENT_YEAR}.${CURRENT_MONTH}.1" >> $(envFilePath)
                NEW_TAG="${CURRENT_YEAR}.${CURRENT_MONTH}.1"
              else
                echo "Reading existing .env-promote file..."
              
                # Read current tag from .env-promote file
                CURRENT_TAG=$(grep "^TAG=" $(envFilePath) | cut -d'=' -f2 | tr -d '"' | tr -d "'")
                echo "Current tag: $CURRENT_TAG"
              
                if [ -z "$CURRENT_TAG" ]; then
                  # No TAG found in .env-promote, create new one
                  echo "TAG=${CURRENT_YEAR}.${CURRENT_MONTH}.1" >> $(envFilePath)
                  NEW_TAG="${CURRENT_YEAR}.${CURRENT_MONTH}.1"
                else
                  # Parse current tag
                  IFS='.' read -r TAG_YEAR TAG_MONTH TAG_BUILD <<< "$CURRENT_TAG"
              
                  echo "Parsed - Year: $TAG_YEAR, Month: $TAG_MONTH, Build: $TAG_BUILD"
              
                  # Check if year/month changed
                  if [ "$TAG_YEAR" != "$CURRENT_YEAR" ] || [ "$TAG_MONTH" != "$CURRENT_MONTH" ]; then
                    echo "Year or month changed, resetting build number to 1"
                    NEW_TAG="${CURRENT_YEAR}.${CURRENT_MONTH}.1"
                  else
                    echo "Same year/month, incrementing build number"
                    NEW_BUILD=$((TAG_BUILD + 1))
                    NEW_TAG="${CURRENT_YEAR}.${CURRENT_MONTH}.${NEW_BUILD}"
                  fi

                  # Update the TAG line in .env-dev file
                  sed -i "s/^TAG=.*/TAG=$NEW_TAG/" $(envFilePathDev)
              
                  # Update the TAG line in .env-promote file
                  sed -i "s/^TAG=.*/TAG=$NEW_TAG/" $(envFilePath)
                fi
              fi
              
              echo "New tag generated: $NEW_TAG"
              echo "##vso[task.setvariable variable=newTag;isOutput=true]$NEW_TAG"

              # Display updated .env-dev file
              echo "Updated .env-dev file contents:"
              cat $(envFilePathDev)

              # Display updated .env-promote file
              echo "Updated .env-promote file contents:"
              cat $(envFilePath)

            displayName: 'Generate and Update Release Tag'
            name: 'generateTag'

          - script: |
              echo "Committing changes to repository..."
              git config --global user.email "<EMAIL>"
              git config --global user.name "Azure Pipelines"
              
              git add EthosJS.PlatformFrontend/external-assets/k8s-manifests/.env-dev
              git add EthosJS.PlatformFrontend/external-assets/k8s-manifests/.env-promote
              git commit -m "Auto-update EthosJS.PlatformFrontend release tag to $(generateTag.newTag)"
              git push origin HEAD:$(Build.SourceBranchName)

            displayName: 'Commit and Push Updated .env-promote File'

          - script: |
              echo "============================================"
              echo " PROMOTION COMPLETED SUCCESSFULLY! "
              echo "============================================"
              echo "New Release Tag: $(generateTag.newTag)"
              echo "Updated file: EthosJS.PlatformFrontend/external-assets/k8s-manifests/.env-dev"
              echo "Updated file: EthosJS.PlatformFrontend/external-assets/k8s-manifests/.env-promote"
              echo "Ready for deployment pipeline to use!"
              echo "============================================"
            displayName: 'Promotion Summary'