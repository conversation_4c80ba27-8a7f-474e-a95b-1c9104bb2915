import { Grid, GridProps } from "@mui/material";

import Info from './info';
import Contact from "./contact";
import EmergencyContact from "./emergency-contact";
import Guardian from "./guardian";

interface PatientInfoProp {
   title?: string
}

const GRID_PROPS: Partial<GridProps> = {
   item: true,
   xs: 12
}

const Profile = (props: PatientInfoProp) => {

   const { } = props;

   return (
      <Grid container spacing={2}>
         <Grid {...GRID_PROPS}>
            <Info
               title="Patient Information"
               values={{
                  summaryDetails: {
                     SSN: `***-**-3455`,
                     patientId: `#34197564`
                  },
                  demographics: {
                     dob: '06/01/1969',
                     sex: 'Male',
                     genderIdentity: 'He/Him',
                     maritalStatus: 'Merried',
                     race: 'Caucasian',
                     ethnicity: 'White',
                  },
                  physicalMeasurements: {
                     hieght: `5'9'`,
                     weight: '165',
                     neck: '16',
                     BMI: '18.5'
                  }
               }}
            />
         </Grid>
         <Grid {...GRID_PROPS}>
            <Contact
               title="Patient Contact"
               values={{
                  phoneNumbers: [
                     {
                        prefered: true,
                        data: {
                           phoneType: 'Mobile',
                           phoneNumber: '************',
                           contactMethod: 'SMS, Call',
                           preferredTime: '7:30 - 11:30 AM'
                        }
                     },
                     {
                        prefered: false,
                        data: {
                           phoneType: 'Mobile',
                           phoneNumber: '************',
                           contactMethod: 'SMS',
                           preferredTime: 'Anytime'
                        },
                     }
                  ],
                  emailAddresses: [
                     {
                        data: {
                           emailType: 'Personal',
                           emailAddress: '<EMAIL>',
                           sameAs: 'Residential Address',
                        },
                        extra: [
                           { value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335' }
                        ],
                        prefered: false
                     }
                  ],
                  billingAddresses: [
                     {
                        prefered: false,
                        data: {
                           type: 'PO Box'
                        },
                        extra: [
                           { value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335' }
                        ]
                     },
                  ],
                  physicalAddresses: [
                     {
                        prefered: false,
                        data: {
                           type: 'Residential'
                        },
                        extra: [
                           { value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335' }
                        ]
                     },
                     {
                        prefered: false,
                        data: {
                           type: 'Business'
                        },
                        extra: [
                           { value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335' }
                        ]
                     },
                     {
                        prefered: false,
                        data: {
                           type: 'PO Box'
                        },
                        extra: [
                           { value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335' }
                        ]
                     }
                  ],
                  deliveryAddresses: [
                     {
                        prefered: false,
                        data: {
                        },
                        extra: [
                           {
                              label: 'Same As',
                              value: 'Residential Address'
                           },
                           {
                              value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335'
                           }
                        ]
                     },
                     {
                        prefered: false,
                        data: { },
                        extra: [
                           {
                              value: 'Signature Required '
                           },
                           {
                              value: 'Keep Address Confidential'
                           },
                           {
                              value: 'Deliver to Partner Pharmacies  '
                           }
                        ]
                     }
                  ]
               }}
            />
         </Grid>
         <Grid {...GRID_PROPS}>
            <EmergencyContact
               title="Emergency Contact, Olivia Bishop, Daughter"
               values={{
                  phoneNumbers: [
                     {
                        prefered: true,
                        data: {
                           phoneType: 'Mobile',
                           phoneNumber: '************',
                           contactMethod: 'SMS, Call',
                           preferredTime: '7:30 - 11:30 AM'
                        }
                     },
                     {
                        prefered: false,
                        data: {
                           phoneType: 'Mobile',
                           phoneNumber: '************',
                           contactMethod: 'SMS',
                           preferredTime: 'Anytime'
                        },
                     }
                  ],
                  emailAddresses: [
                     {
                        data: {
                           emailType: 'Personal',
                           emailAddress: '<EMAIL>',
                           sameAs: 'Residential Address',
                        },
                        extra: [
                           { value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335' }
                        ],
                        prefered: false
                     }
                  ],
                  physicalAddresses: [
                     {
                        prefered: false,
                        data: {
                           type: 'Residential'
                        },
                        extra: [
                           { value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335' }
                        ]
                     },
                     {
                        prefered: false,
                        data: {
                           type: 'Business'
                        },
                        extra: [
                           { value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335' }
                        ]
                     },
                     {
                        prefered: false,
                        data: {
                           type: 'PO Box'
                        },
                        extra: [
                           { value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335' }
                        ]
                     }
                  ],
                  deliveryAddresses: [
                     {
                        prefered: false,
                        data: {
                        },
                        extra: [
                           {
                              label: 'Same As',
                              value: 'Residential Address'
                           },
                           {
                              value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335'
                           }
                        ]
                     },
                     {
                        prefered: false,
                        data: { },
                        extra: [
                           {
                              value: 'Signature Required '
                           },
                           {
                              value: 'Keep Address Confidential'
                           },
                           {
                              value: 'Deliver to Partner Pharmacies  '
                           }
                        ]
                     }
                  ]
               }}
            />
         </Grid>
         <Grid {...GRID_PROPS}>
            <Guardian
               title="Guardian, Peter Bishop, Son"
               values={{
                  phoneNumbers: [
                     {
                        prefered: true,
                        data: {
                           phoneType: 'Mobile',
                           phoneNumber: '************',
                           contactMethod: 'SMS, Call',
                           preferredTime: '7:30 - 11:30 AM'
                        }
                     },
                     {
                        prefered: false,
                        data: {
                           phoneType: 'Mobile',
                           phoneNumber: '************',
                           contactMethod: 'SMS',
                           preferredTime: 'Anytime'
                        },
                     }
                  ],
                  emailAddresses: [
                     {
                        data: {
                           emailType: 'Personal',
                           emailAddress: '<EMAIL>',
                           sameAs: 'Residential Address',
                        },
                        extra: [
                           { value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335' }
                        ],
                        prefered: false
                     }
                  ],
                  physicalAddresses: [
                     {
                        prefered: false,
                        data: {
                           type: 'Residential'
                        },
                        extra: [
                           { value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335' }
                        ]
                     },
                     {
                        prefered: false,
                        data: {
                           type: 'Business'
                        },
                        extra: [
                           { value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335' }
                        ]
                     },
                     {
                        prefered: false,
                        data: {
                           type: 'PO Box'
                        },
                        extra: [
                           { value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335' }
                        ]
                     }
                  ],
                  deliveryAddresses: [
                     {
                        prefered: false,
                        data: {
                        },
                        extra: [
                           {
                              label: 'Same As',
                              value: 'Residential Address'
                           },
                           {
                              value: '9874 McFrank Avenue, Unit 985, Gig Harbor, WA 98335'
                           }
                        ]
                     },
                     {
                        prefered: false,
                        data: { },
                        extra: [
                           {
                              value: 'Signature Required '
                           },
                           {
                              value: 'Keep Address Confidential'
                           },
                           {
                              value: 'Deliver to Partner Pharmacies  '
                           }
                        ]
                     }
                  ],
                  files: [
                     {
                        data: {
                           courtOrder: 'Peter_bishop_heealthcare_2025.pdf',
                           healthCarePowerOfAttorney: 'Peter_bishop_walter_bishop_cort_order_2025.pdf'
                        },
                        prefered: false,
                        extra: []
                     }
                  ]
               }}
            />
         </Grid>
      </Grid>
   )
};

export default Profile;