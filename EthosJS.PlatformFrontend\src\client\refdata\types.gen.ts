// This file is auto-generated by @hey-api/openapi-ts

export type CreationContext = 0 | 1 | 2 | 3;

export type KeySetValueSchemaMember = {
    displayName?: string | null;
    type?: string | null;
    source?: string | null;
};

export type KeyTriggerBehavior = 0 | 1 | 2;

export type ReferenceDataDto = {
    source?: string | null;
    version?: string | null;
    authority?: string | null;
    name?: string | null;
    key?: string | null;
    data?: Array<Array<ReferenceDataValueDto>> | null;
};

export type ReferenceDataImportJob = {
    jobId?: string;
    startTime?: string;
    endTime?: string | null;
    tenantId?: string | null;
    setId?: number | null;
    set?: ReferenceDataSet | null;
    error?: string | null;
};

export type ReferenceDataList = {
    id?: number;
    name?: string | null;
    type?: ReferenceDataListType;
    values?: Array<ReferenceDataListValue> | null;
    sets?: Array<ReferenceDataSet> | null;
};

export type ReferenceDataListDto = {
    name?: string | null;
    type?: string | null;
};

export type ReferenceDataListType = 0 | 1 | 2 | 3 | 4;

export type ReferenceDataListTypeDto = {
    typeName?: string | null;
    typeId?: number;
    defaultValue?: unknown;
};

export type ReferenceDataListTypeDtoPagedResponse = {
    items?: Array<ReferenceDataListTypeDto> | null;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type ReferenceDataListValue = {
    id?: number;
    listId?: number;
    value?: unknown;
    list?: ReferenceDataList | null;
    setValues?: Array<ReferenceDataSetValue> | null;
    setKeyValues?: Array<ReferenceDataSetKeyValue> | null;
};

export type ReferenceDataListValueDto = {
    valueId?: number | null;
    value?: unknown;
};

export type ReferenceDataSet = {
    id?: number;
    source?: string | null;
    authority?: string | null;
    version?: string | null;
    name?: string | null;
    keyListId?: number;
    keyList?: ReferenceDataList | null;
    tenantId?: string;
    setKeyValues?: Array<ReferenceDataSetKeyValue> | null;
    alternateKeyValues?: Array<ReferenceDataSetKeyValueAlternate> | null;
    setValues?: Array<ReferenceDataSetValue> | null;
    importJob?: ReferenceDataImportJob | null;
};

export type ReferenceDataSetAlternateBehaviorDto = {
    onSelectRequireNewValue?: boolean | null;
    onSelectAllowNewValue?: boolean | null;
    onNewValueRequireAllFields?: boolean | null;
    onNewValueRequireKeyValueOnly?: boolean | null;
};

export type ReferenceDataSetAlternateDto = {
    setId?: number | null;
    name?: string | null;
    version?: string | null;
    value?: unknown;
    behavior?: ReferenceDataSetAlternateBehaviorDto | null;
    data?: ReferenceDataSetKeyValueDto | null;
    creationSchema?: Array<KeySetValueSchemaMember> | null;
};

export type ReferenceDataSetAlternateProcessDto = {
    selectedValue?: unknown;
    schema?: Array<ReferenceDataValueDto> | null;
    tenantId?: string | null;
};

export type ReferenceDataSetDto = {
    source?: string | null;
    authority?: string | null;
    name?: string | null;
    version?: string | null;
    key?: string | null;
    keyType?: string | null;
};

export type ReferenceDataSetKeyDefinitionDto = {
    name?: string | null;
    value?: unknown;
};

export type ReferenceDataSetKeyValue = {
    id?: number;
    setId?: number;
    valueId?: number;
    enabled?: boolean;
    value?: ReferenceDataListValue | null;
    tenantId?: string;
    setValues?: Array<ReferenceDataSetValue> | null;
    set?: ReferenceDataSet | null;
    mappedSetValues?: Array<ReferenceDataSetValue> | null;
    creationContext?: CreationContext;
    keyTrigger?: ReferenceDataSetKeyValue | null;
    triggeredKeys?: Array<ReferenceDataSetKeyValue> | null;
    alternateKeys?: Array<ReferenceDataSetKeyValueAlternate> | null;
    keyTriggerId?: number | null;
};

export type ReferenceDataSetKeyValueAlternate = {
    setId?: number;
    tenantId?: string;
    keyValue?: ReferenceDataSetKeyValue | null;
    keyValueId?: number;
    triggerBehavior?: KeyTriggerBehavior;
    schemaBehavior?: SchemaBehavior;
    set?: ReferenceDataSet | null;
};

export type ReferenceDataSetKeyValueDto = {
    id?: number;
    key?: ReferenceDataSetKeyDefinitionDto | null;
    values?: {
        [key: string]: unknown;
    } | null;
};

export type ReferenceDataSetReferenceDto = {
    setId?: number | null;
    name?: string | null;
    version?: string | null;
};

export type ReferenceDataSetSummaryDto = {
    source?: string | null;
    authority?: string | null;
    name?: string | null;
    version?: string | null;
    setId?: number;
    count?: number;
};

export type ReferenceDataSetSummaryDtoPagedResponse = {
    items?: Array<ReferenceDataSetSummaryDto> | null;
    offset?: number;
    limit?: number;
    totalCount?: number;
    previous?: string | null;
    next?: string | null;
};

export type ReferenceDataSetValue = {
    valueId?: number;
    setId?: number;
    tenantId?: string;
    keyValueId?: number;
    alias?: string | null;
    mappedKeyValueId?: number | null;
    value?: ReferenceDataListValue | null;
    keyValue?: ReferenceDataSetKeyValue | null;
    mappedKeyValue?: ReferenceDataSetKeyValue | null;
    set?: ReferenceDataSet | null;
};

export type ReferenceDataValidationDetailDto = {
    setId?: number | null;
    setName?: string | null;
    version?: string | null;
    value?: unknown;
    validationType?: ReferenceDataValidationType;
    propertyName?: string | null;
};

export type ReferenceDataValidationType = 0 | 1 | 2;

export type ReferenceDataValueDto = {
    name?: string | null;
    value?: unknown;
    alias?: string | null;
    inSet?: ReferenceDataSetReferenceDto | null;
};

export type SchemaBehavior = 0 | 1;

export type PostApiReferenceBootstrapData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/reference/bootstrap';
};

export type PostApiReferenceBootstrapResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type DeleteApiReferenceSetsBySetIdData = {
    body?: never;
    path: {
        setId: number;
    };
    query?: never;
    url: '/api/reference/sets/{setId}';
};

export type DeleteApiReferenceSetsBySetIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiReferenceSetsBySetIdData = {
    body?: never;
    path: {
        setId: number;
    };
    query?: never;
    url: '/api/reference/sets/{setId}';
};

export type GetApiReferenceSetsBySetIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type DeleteApiReferenceSetsData = {
    body?: never;
    path?: never;
    query?: {
        setName?: string;
        version?: string;
    };
    url: '/api/reference/sets';
};

export type DeleteApiReferenceSetsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiReferenceSetsData = {
    body?: never;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
        setName?: string;
        version?: string;
    };
    url: '/api/reference/sets';
};

export type GetApiReferenceSetsResponses = {
    /**
     * OK
     */
    200: ReferenceDataSetSummaryDtoPagedResponse;
};

export type GetApiReferenceSetsResponse = GetApiReferenceSetsResponses[keyof GetApiReferenceSetsResponses];

export type PostApiReferenceSetsData = {
    body?: ReferenceDataSetDto;
    path?: never;
    query?: never;
    url: '/api/reference/sets';
};

export type PostApiReferenceSetsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiReferenceJobsByJobIdData = {
    body?: never;
    path: {
        jobId: string;
    };
    query?: never;
    url: '/api/reference/jobs/{jobId}';
};

export type GetApiReferenceJobsByJobIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiReferenceListTypesData = {
    body?: never;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/reference/listTypes';
};

export type GetApiReferenceListTypesResponses = {
    /**
     * OK
     */
    200: ReferenceDataListTypeDtoPagedResponse;
};

export type GetApiReferenceListTypesResponse = GetApiReferenceListTypesResponses[keyof GetApiReferenceListTypesResponses];

export type GetApiReferenceListsByListIdData = {
    body?: never;
    path: {
        listId: number;
    };
    query?: never;
    url: '/api/reference/lists/{listId}';
};

export type GetApiReferenceListsByListIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiReferenceListsData = {
    body?: ReferenceDataListDto;
    path?: never;
    query?: never;
    url: '/api/reference/lists';
};

export type PostApiReferenceListsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiReferenceListsByListIdValuesData = {
    body?: never;
    path: {
        listId: number;
    };
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/reference/lists/{listId}/values';
};

export type GetApiReferenceListsByListIdValuesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiReferenceListsByListIdValuesData = {
    body?: ReferenceDataListValueDto;
    path: {
        listId: number;
    };
    query?: never;
    url: '/api/reference/lists/{listId}/values';
};

export type PostApiReferenceListsByListIdValuesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PutApiReferenceListsByListIdValuesData = {
    body?: ReferenceDataListValueDto;
    path: {
        listId: number;
    };
    query?: never;
    url: '/api/reference/lists/{listId}/values';
};

export type PutApiReferenceListsByListIdValuesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiReferenceSetsBySetIdValuesData = {
    body?: never;
    path: {
        setId: number;
    };
    query?: {
        filter?: string;
        offset?: number;
        limit?: number;
        maxDepth?: number;
    };
    url: '/api/reference/sets/{setId}/values';
};

export type GetApiReferenceSetsBySetIdValuesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiReferenceSetsBySetIdValuesData = {
    body?: Array<ReferenceDataValueDto>;
    path: {
        setId: number;
    };
    query?: never;
    url: '/api/reference/sets/{setId}/values';
};

export type PostApiReferenceSetsBySetIdValuesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiReferenceSetsBySetIdAlternatesData = {
    body?: never;
    path: {
        setId: number;
    };
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/reference/sets/{setId}/alternates';
};

export type GetApiReferenceSetsBySetIdAlternatesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiReferenceSetsBySetIdAlternatesData = {
    body?: ReferenceDataSetAlternateDto;
    path: {
        setId: number;
    };
    query?: never;
    url: '/api/reference/sets/{setId}/alternates';
};

export type PostApiReferenceSetsBySetIdAlternatesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiReferenceSetsBySetIdAlternatesProcessData = {
    body?: ReferenceDataSetAlternateProcessDto;
    path: {
        setId: number;
    };
    query?: never;
    url: '/api/reference/sets/{setId}/alternates/process';
};

export type PostApiReferenceSetsBySetIdAlternatesProcessResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiReferenceSetsBySetIdValuesByValueIdData = {
    body?: never;
    path: {
        setId: number;
        valueId: number;
    };
    query?: {
        maxDepth?: number;
    };
    url: '/api/reference/sets/{setId}/values/{valueId}';
};

export type GetApiReferenceSetsBySetIdValuesByValueIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiReferenceSetsKeysByIdData = {
    body?: never;
    path: {
        id: number;
    };
    query?: {
        maxDepth?: number;
    };
    url: '/api/reference/sets/keys/{id}';
};

export type GetApiReferenceSetsKeysByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiReferenceSetsKeysData = {
    body?: never;
    path?: never;
    query?: {
        ids?: Array<number>;
        offset?: number;
        limit?: number;
        maxDepth?: number;
    };
    url: '/api/reference/sets/keys';
};

export type GetApiReferenceSetsKeysResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiReferenceSetsValuesData = {
    body?: never;
    path?: never;
    query?: {
        setName?: string;
        version?: string;
        filter?: string;
        offset?: number;
        limit?: number;
        maxDepth?: number;
    };
    url: '/api/reference/sets/values';
};

export type GetApiReferenceSetsValuesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiReferenceValidateData = {
    body?: Array<ReferenceDataValidationDetailDto>;
    path?: never;
    query?: never;
    url: '/api/reference/validate';
};

export type PostApiReferenceValidateResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiReferenceSearchKeysData = {
    body?: never;
    path?: never;
    query?: {
        listName?: string;
        value?: string;
        type?: string;
        offset?: number;
        limit?: number;
        maxDepth?: number;
    };
    url: '/api/reference/search/keys';
};

export type GetApiReferenceSearchKeysResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiReferenceSearchValuesData = {
    body?: never;
    path?: never;
    query?: {
        listName?: string;
        value?: string;
        type?: string;
        offset?: number;
        limit?: number;
        maxDepth?: number;
    };
    url: '/api/reference/search/values';
};

export type GetApiReferenceSearchValuesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiReferenceInternalImportCsvData = {
    body?: {
        csvFile?: Blob | File;
        setName?: string;
        setKey?: string;
        version?: string;
        source?: string;
        authority?: string;
        typeMappings?: {
            [key: string]: string;
        };
    };
    path?: never;
    query?: never;
    url: '/api/reference/internal/importCsv';
};

export type PostApiReferenceInternalImportCsvResponses = {
    /**
     * OK
     */
    200: ReferenceDataImportJob;
};

export type PostApiReferenceInternalImportCsvResponse = PostApiReferenceInternalImportCsvResponses[keyof PostApiReferenceInternalImportCsvResponses];

export type PostApiReferenceInternalIntakeData = {
    body?: ReferenceDataDto;
    path?: never;
    query?: never;
    url: '/api/reference/internal/intake';
};

export type PostApiReferenceInternalIntakeResponses = {
    /**
     * OK
     */
    200: ReferenceDataImportJob;
};

export type PostApiReferenceInternalIntakeResponse = PostApiReferenceInternalIntakeResponses[keyof PostApiReferenceInternalIntakeResponses];

export type ClientOptions = {
    baseURL: 'http://localhost:4004' | (string & {});
};