import { CSSProperties } from "react"


export const getRootStyle = () => {
   return {
      display: 'flex',
      minHeight: '4.75rem',
      justifyContent: 'space-between',
      alignItems: 'center',
      alignSelf: 'stretch',
      borderBottom: `1px solid`,
      borderColor: 'divider',
      backgroundColor: 'background.paper'
   }
}

export const getPopoverTriggerIconIndicatorStyle = (open: boolean): CSSProperties => {
   return { transform: open ? 'rotate(180deg)' : '', transition: 'transform 0.2s ease-in-out' }
}