import usePatient from '@features/patient-create/hooks/use-patient';
import { PatientState, StepPropsNew } from '../../types/state-types';
import AddressesForm from '@features/patient-create/forms/addresses.form';
import { useMemo, useState } from 'react';
import usePatientValidation from '@features/patient-create/hooks/use-patient-validation';
import { EthosWorkflowsApiCreatePatientInputDto } from '@client/workflows';
import { getNewPatientState } from '@features/patient-create/utils';
import NotificationSnackbar, { NotificationState } from '@components/notification-snackbar';
import dayjs from 'dayjs';
import NotificationBanner from '@components/notification-banner';

export default function AddAddressesStep({ patientId, successCallback }: StepPropsNew) {
	const { patientData, updatePatient, updatePatientError, resetUpdatePatientMutation, saveDraft } =
		usePatient({
			patientId,
		});

	const { validateFormatted } = usePatientValidation();

	const { data } = patientData ?? {};

	const [notification, setNotification] = useState<NotificationState>({
		message: '',
		severity: 'info',
	});

	const resetToast = () => {
		setNotification({
			message: '',
			severity: 'info',
		});
	};

	const contactInformation =
		data?.contactInformation as EthosWorkflowsApiCreatePatientInputDto['contactInformation'];

	const patientState = (data?._state as unknown as PatientState) ?? {};
	const stepStatus = patientState.stepState?.Addresses ?? 'NotStarted';

	const savedData = useMemo(() => {
		return {
			addresses: contactInformation?.addresses ?? [],
		};
	}, [contactInformation]);

	return (
		<>
			<NotificationSnackbar
				notification={notification}
				onCloseToast={resetToast}
			/>
			<NotificationBanner
				message={updatePatientError?.message}
				severity="error"
				scrollIntoView
				onClose={resetUpdatePatientMutation}
			/>
			<AddressesForm
				savedData={savedData}
				onSubmit={(newData) => {
					updatePatient(
						{
							...data,
							contactInformation: {
								...contactInformation,
								addresses: newData.addresses,
							} as EthosWorkflowsApiCreatePatientInputDto['contactInformation'],
						},
						getNewPatientState(patientState, 'Addresses', 'Insurances'),
						successCallback
					);
				}}
				onSaveDraft={(newData) => {
					saveDraft(
						{
							...data,
							contactInformation: {
								...contactInformation,
								addresses: newData.addresses,
							} as EthosWorkflowsApiCreatePatientInputDto['contactInformation'],
						},
						{
							flowState: {
								...patientState.flowState,
								lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
							},
							stepState: { ...patientState.stepState },
						},
						() => {
							setNotification({
								message: 'Draft saved successfully',
								severity: 'success',
							});
						}
					);
				}}
				onValidate={async (data) => {
					return validateFormatted({
						contactInformation: {
							...data,
						} as EthosWorkflowsApiCreatePatientInputDto['contactInformation'],
					});
				}}
				isUpdate={stepStatus === 'Complete'}
			/>
		</>
	);
}
