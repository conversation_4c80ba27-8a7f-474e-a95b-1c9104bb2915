import AppTextField from '@components/forms/fields/app-text.field';
import { createFormHook } from '@tanstack/react-form';
import { formContext, fieldContext } from '@hooks/form-context';
import AppSsnField from '@components/forms/fields/app-ssn.field';
import AppDateField from '@components/forms/fields/app-date.field';
import AppSelectField from '@components/forms/fields/app-select.field';
import AppPhoneNumberField from '@components/forms/fields/app-phone-number.field';
import AppEmailField from '@components/forms/fields/app-email.field';
import AppCheckboxField from '@components/forms/fields/app-checkbox.field';
import AppTimeRangeField from '@components/forms/fields/app-time-range.field';
import AppRadioField from '@components/forms/fields/app-radio.field';
import AppAutocompleteField from '@components/forms/fields/app-autocomplete.field';

export const { useAppForm, withForm } = createFormHook({
	fieldComponents: {
		AppTextField,
		AppSsnField,
		AppDateField,
		AppSelectField,
		AppPhoneNumberField,
		AppEmailField,
		AppCheckboxField,
		AppRadioField,
		AppTimeRangeField,
		AppAutocompleteField,
	},
	formComponents: {},
	fieldContext,
	formContext,
});

export type AppFormType = ReturnType<typeof useAppForm>;
