{
	"compilerOptions": {
		"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
		//"target": "ES2020",
		"useDefineForClassFields": true,
		"lib": ["ES2020", "DOM", "DOM.Iterable"],
		"module": "ESNext",
		"skipLibCheck": true,

		/* Bundler mode */
		"moduleResolution": "bundler",
		"allowImportingTsExtensions": true,
		"isolatedModules": true,
		"moduleDetection": "force",
		"noEmit": true,
		"jsx": "react-jsx",

		/* Linting */
		"strict": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"noFallthroughCasesInSwitch": true,
		"noUncheckedSideEffectImports": true,
		"baseUrl": ".",
		"paths": {
			"@components/*": ["src/components/*"],
			"@hooks/*": ["src/hooks/*"],
			"@utils/*": ["src/utils/*"],
			"@api/*": ["src/api/*"],
			"@auth/*": ["src/auth/*"],
			"@config/*": ["src/config/*"],
			"@schemas/*": ["src/schemas/*"],
			"@contexts/*": ["src/contexts/*"],
			"@workflows/*": ["src/workflows/*"],
			"@client/*": ["src/client/*"],
			"@app-types/*": ["src/app-types/*"],
			"@features/*": ["src/features/*"]
		}
	},
	"include": ["src", "src/routes/_dashboard/patients/_dqs/$workflowId.tsx"]
}
