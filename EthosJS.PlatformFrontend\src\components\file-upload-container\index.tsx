import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack, Grid2 as Grid, Box, Typography } from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, Edit } from '@mui/icons-material';
import { ArrowDownToLine, Eye, FileCheck2 } from 'lucide-react';
import MainCardContainer from '@components/main-container/main-card-container';
import List from '@components/list';
import { useAppForm } from '@hooks/app-form';
import { FileUploadContainerProps, UploadedFile } from './types';
import { FileUploadForm } from './file-upload-form';
import { FileItem } from './file-item';
import { FileFormData } from './types';

// Simple filter component
const FileListFilters: React.FC = () => {
  const form = useAppForm({
    defaultValues: {
      category: '',
      search: '',
      documentType: '',
      uploadedBy: '',
      uploadedTime: ''
    }
  });

  const categoryOptions = [
    { label: 'All Categories', value: 'All' },
    { label: 'Identity Documents', value: 'identity' },
    { label: 'Insurance Documents', value: 'insurance' },
    { label: 'Medical Forms', value: 'medical' },
    { label: 'Consent Forms', value: 'consent' }
  ];

  const documentTypeOptions = [
    { label: 'All Types', value: '' },
    { label: 'Photo ID', value: 'photo-id' },
    { label: 'Insurance Card', value: 'insurance-card' },
    { label: 'Pre-Study Questionnaire', value: 'pre-study-questionnaire' }
  ];

  const uploadedByOptions = [
    { label: 'All Users', value: '' },
    { label: 'John Scott', value: 'john-scott' },
    { label: 'Jane Doe', value: 'jane-doe' }
  ];

  const uploadedTimeOptions = [
    { label: 'All Time', value: '' },
    { label: 'Last 24 hours', value: '24h' },
    { label: 'Last 7 days', value: '7d' },
    { label: 'Last 30 days', value: '30d' }
  ];

  return (
    <Box sx={{ backgroundColor: '#F8F9FA', borderRadius: 1 }}>
      <Stack gap={2}>
        {/* First Row: Category and Search */}
        <Grid container spacing={2}>
          <Grid size={3}>
            <form.AppField
              name="category"
              children={(field) => (
                <field.AppSelectField
                  label="Category"
                  options={categoryOptions}
                  placeholder="All Categories"
                />
              )}
            />
          </Grid>
          <Grid size={9}>
            <form.AppField
              name="search"
              children={(field) => (
                <field.AppSelectField label="Search" options={uploadedByOptions} />
              )}
            />
          </Grid>
        </Grid>

        {/* Second Row: Document Type, Uploaded By, Uploaded Time */}
        <Grid container spacing={2}>
          <Grid size={4}>
            <form.AppField
              name="documentType"
              children={(field) => (
                <field.AppSelectField
                  label="Document Type"
                  options={documentTypeOptions}
                  placeholder="All Types"
                />
              )}
            />
          </Grid>
          <Grid size={4}>
            <form.AppField
              name="uploadedBy"
              children={(field) => (
                <field.AppSelectField
                  label="Uploaded By"
                  options={uploadedByOptions}
                  placeholder="All Users"
                />
              )}
            />
          </Grid>
          <Grid size={4}>
            <form.AppField
              name="uploadedTime"
              children={(field) => (
                <field.AppSelectField
                  label="Uploaded Time"
                  options={uploadedTimeOptions}
                  placeholder="All Time"
                />
              )}
            />
          </Grid>
        </Grid>
      </Stack>
    </Box>
  );
};

const FileUploadContainer: React.FC<FileUploadContainerProps> = ({
  title,
  icon,
  children,
  onAddFile,
  onDeleteFile,
  onEditFile,
  workflowId,
  emphasis = 'low',
  color = 'primary',
  descriptionSubheader,
  descriptionText,
  footerProps,
  customAction
}) => {
  // Always enable these features
  const allowAdd = true;
  const allowDelete = true;
  const allowEdit = true;
  const [isAddMode, setIsAddMode] = useState(false);
  const [editingFileId, setEditingFileId] = useState<string | null>(null);

  const handleAddClick = () => {
    setIsAddMode(true);
    setEditingFileId(null);
  };

  const handleSave = (fileData: FileFormData, fileId?: string) => {
    if (editingFileId && onEditFile) {
      onEditFile(editingFileId, fileData);
      setEditingFileId(null);
    } else if (onAddFile) {
      onAddFile(fileData);
      setIsAddMode(false);
    }
  };

  const handleCancel = () => {
    setIsAddMode(false);
    setEditingFileId(null);
  };

  const handleEditFile = (fileId: string) => {
    setEditingFileId(fileId);
    setIsAddMode(false);
  };

  const handleDeleteFile = (fileId: string) => {
    if (onDeleteFile) {
      onDeleteFile(fileId);
    }
    setEditingFileId(null);
  };

  const enhancedChildren = React.Children.map(children, (child) => {
    if (React.isValidElement(child) && child.type === FileItem) {
      const childProps = child.props as any;
      return React.cloneElement(child, {
        ...childProps,
        isEditing: editingFileId === childProps.file?.id,
        onStartEdit: () => handleEditFile(childProps.file?.id),
        onCancelEdit: () => setEditingFileId(null),
        onSave: (fileData: FileFormData) => handleSave(fileData, childProps.file?.id),
        onDelete:
          allowDelete && onDeleteFile
            ? () => handleDeleteFile(childProps.file?.id)
            : childProps.onDelete,
        workflowId,
        containerTitle: title,
        icon
      });
    }
    return child;
  });

  // Filter children to show only the editing file when in edit mode
  const filteredChildren = editingFileId
    ? React.Children.toArray(enhancedChildren).filter((child) => {
        if (React.isValidElement(child) && child.type === FileItem) {
          const childProps = child.props as any;
          return childProps.file?.id === editingFileId;
        }
        return false;
      })
    : enhancedChildren;

  // If in add mode, show only the add form
  if (isAddMode) {
    return (
      <FileUploadForm
        title={title}
        icon={icon}
        onSave={(fileData) => handleSave(fileData)}
        onCancel={handleCancel}
        workflowId={workflowId}
      />
    );
  }

  // If in edit mode, show only the editing file
  if (editingFileId) {
    return <>{filteredChildren}</>;
  }

  // Convert children to file array for list display
  const fileItems = React.Children.toArray(enhancedChildren)
    .filter((child) => React.isValidElement(child) && child.type === FileItem)
    .map((child) => {
      const element = child as React.ReactElement<any>;
      const file = element.props.file as UploadedFile;
      return {
        icon: <FileCheck2 size="30px" />,
        title: '',
        subTitle: '',
        value: file.id,
        extra: (
          <Stack direction="row" gap="4px">
            <IconButton size="small" aria-label="download document">
              <ArrowDownToLine />
            </IconButton>
            <IconButton size="small" aria-label="view document">
              <Eye />
            </IconButton>
            <IconButton size="small" onClick={() => handleEditFile(file.id)}>
              <Edit />
            </IconButton>
          </Stack>
        ),
        meta: file
      };
    });

  // Normal view - different behavior based on whether files exist
  if (fileItems.length === 0) {
    // No files uploaded - show only MainCardContainer with emphasis="low"
    return (
      <MainCardContainer
        title={title}
        icon={icon}
        emphasis="low"
        color={color}
        descriptionSubheader={descriptionSubheader}
        descriptionText={descriptionText}
        primaryActionType={allowAdd && !isAddMode && !editingFileId ? 'Add' : 'none'}
        onPrimaryAction={handleAddClick}
        footerProps={footerProps}>
        {/* Empty body - only headers and add button */}
      </MainCardContainer>
    );
  }

  // Files exist - show filters and file list
  return (
    <MainCardContainer
      title={title}
      icon={icon}
      emphasis={emphasis}
      color={color}
      descriptionSubheader={descriptionSubheader}
      descriptionText={descriptionText}
      primaryActionType={allowAdd && !isAddMode && !editingFileId ? 'Add' : 'none'}
      onPrimaryAction={handleAddClick}
      footerProps={footerProps}>
      <Stack gap={2}>
        {/* Filter Controls */}
        <FileListFilters />

        {/* File List */}
        <List<FileFormData>
          items={fileItems}
          selectable={false}
          size="large"
          renderTitle={(item) => (
            <Box>
              <Typography fontSize={'14px'} fontWeight="semiBold" sx={{ mb: '2px' }} color="primary.dark">
                {item?.meta?.documentType}
              </Typography>
              <Typography fontSize={'12px'} fontWeight={'medium'} color="primary.dark">
                {item?.meta?.fileName}
              </Typography>
              <Typography fontSize={'12px'} fontWeight={'medium'} color="primary.dark">
                Uploaded by {'Ashley W.'} • {'10 minutes ago'}
              </Typography>
            </Box>
          )}
        />
      </Stack>
    </MainCardContainer>
  );
};

export default FileUploadContainer;
