import MainCardContainer, {
	MainCardContainerProps,
} from '@components/main-container/main-card-container';
import { useState } from 'react';
import { Stack } from '@mui/material';
import { ValidationErrors } from '@app-types/validation';
import ChipSummary, { ChipData } from '@components/chip-summary';
import DetailsReviewContainer from '@components/details-review-container';
import { map } from 'lodash';

interface BaseArrayFormContainerProps<Data extends Record<string, unknown>> {
	pushValue: (value: Data) => void;
	removeValue: (index: number) => void;
	replaceValue: (index: number, value: Data) => void;
	state: {
		value: Data[];
	};
	onValidate: (data: Data, index: number) => Promise<ValidationErrors | undefined>;
	renderForm: (props: {
		formValues: Data;
		onAdd: (data: Data) => void;
		onCancel: (hasValues: boolean) => void;
		onDelete: () => void;
		onValidate: (data: Data) => Promise<ValidationErrors | undefined>;
		isUpdate?: boolean;
		containerProps: MainCardContainerProps;
	}) => React.ReactNode;
	isUpdate?: boolean;
	defaultValues: Data;
	formatSummary: (data: Data) => Array<ChipData>;
	mainCardContainerProps?: MainCardContainerProps;
	initialOpen?: boolean;
	dataTestId?: string;
}

interface BaseTabItem {
	name: string;
	value: string;
}

interface TabItemDefaultMode<Data> extends BaseTabItem {
	mode?: 'default';
	formatTabSummary: (data: Data) => Array<ChipData>;
}

interface TabItemArrayMode<Data> extends BaseTabItem {
	mode: 'array';
	formatTabSummary: (data: Data) => Array<Array<ChipData>>;
}

interface TabsArrayFormContainerProps<Data extends Record<string, unknown>>
	extends Omit<BaseArrayFormContainerProps<Data>, 'formatSummary'> {
	displayMode: 'tab';
	tabItems: Array<TabItemDefaultMode<Data> | TabItemArrayMode<Data>>;
	tabContainerHeaderProps: (data: Data) => MainCardContainerProps;
	formatSummary?: never;
}

interface DefaultArrayFormContainerProps<Data extends Record<string, unknown>>
	extends BaseArrayFormContainerProps<Data> {
	displayMode?: 'default';
	tabItems?: never;
	tabContainerHeaderProps?: never;
}

export type ArrayFormContainerProps<Data extends Record<string, unknown>> =
	| DefaultArrayFormContainerProps<Data>
	| TabsArrayFormContainerProps<Data>;

export default function ArrayFormContainer<Data extends Record<string, unknown>>({
	pushValue,
	removeValue,
	replaceValue,
	state,
	onValidate,
	isUpdate,
	renderForm,
	defaultValues,
	formatSummary,
	mainCardContainerProps = {},
	initialOpen = false,
	displayMode = 'default',
	tabItems = [],
	tabContainerHeaderProps,
	dataTestId = 'array-form-container',
}: ArrayFormContainerProps<Data>) {
	const [editIndex, setEditIndex] = useState<number | null>(
		initialOpen && state.value.length ? 0 : null
	);

	if (editIndex !== null) {
		return renderForm({
			formValues: state.value?.[editIndex],
			onAdd: (data) => {
				replaceValue(editIndex, data);
				setEditIndex(null);
			},
			onCancel: (hasValues) => {
				if (!hasValues) {
					removeValue(editIndex);
				}
				setEditIndex(null);
			},
			onDelete: () => {
				removeValue(editIndex);
				setEditIndex(null);
			},
			onValidate: async (data) => {
				const res = await onValidate(data, editIndex);
				return res;
			},
			isUpdate,
			containerProps: {
				emphasis: isUpdate ? 'high' : 'low',
			},
		});
	}

	if (displayMode === 'tab') {
		return (
			<MainCardContainer
				primaryActionType="Add"
				color="primary"
				emphasis={state.value.length ? 'high' : 'low'}
				dataTestId={dataTestId}
				onPrimaryAction={() => {
					setEditIndex(state.value.length);
					pushValue(defaultValues);
				}}
				{...mainCardContainerProps}>
				{state.value.length
					? map(state.value, (values, i) => {
							return (
								<DetailsReviewContainer
									key={i}
									mode="tab"
									mainCardContainerProps={{
										emphasis: isUpdate ? 'high' : 'low',
										primaryActionType: 'Edit',
										onPrimaryAction: () => setEditIndex(i),
										...(tabContainerHeaderProps ? tabContainerHeaderProps(values) : {}),
									}}
									tabItems={map(tabItems, (item) => {
										const chipSummaryData = item.formatTabSummary(values);
										if (item.mode === 'array') {
											return {
												name: item.name,
												value: item.value,
												mode: item.mode,
												chipSummaryData: chipSummaryData as Array<Array<ChipData>>,
											};
										}
										return {
											name: item.name,
											value: item.value,
											mode: item.mode ?? 'default',
											chipSummaryData: chipSummaryData as Array<ChipData>,
										};
									})}
									defaultValue={tabItems[0]?.value}
								/>
							);
						})
					: null}
			</MainCardContainer>
		);
	}

	return (
		<MainCardContainer
			primaryActionType="Add"
			color="primary"
			emphasis={state.value.length ? 'high' : 'low'}
			dataTestId={dataTestId}
			onPrimaryAction={() => {
				setEditIndex(state.value.length);
				pushValue(defaultValues);
			}}
			{...mainCardContainerProps}>
			{state.value.length ? (
				<Stack spacing={2}>
					{state.value.map((values, i) => (
						<ChipSummary
							key={i}
							items={formatSummary ? formatSummary(values) : []}
							onEdit={() => setEditIndex(i)}
						/>
					))}
				</Stack>
			) : null}
		</MainCardContainer>
	);
}
