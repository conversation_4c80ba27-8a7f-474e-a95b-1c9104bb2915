import { z } from 'zod';

const optionalRefData = z.preprocess((value) => value || null, z.number().nullable());

const requiredRefData = z
	.number()
	.nullable()
	.optional()
	.transform((value) => value || null);

const optionalString = z.preprocess((value) => value || '', z.string().optional());

const requiredNumber = z.preprocess(
	(value) => value || 0,
	z
		.string()
		.optional()
		.transform((value) => (typeof value === 'string' ? Number(value) : 0))
);

export { optionalRefData, requiredRefData, optionalString, requiredNumber };
