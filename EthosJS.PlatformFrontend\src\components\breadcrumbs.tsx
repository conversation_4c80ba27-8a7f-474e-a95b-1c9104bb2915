import { Link as RouterLink, useLocation } from "@tanstack/react-router"
import { Breadcrumbs as MUIBreadcrumbs, Link, Typography, styled } from "@mui/material"
import HomeIcon from "@mui/icons-material/Home"
import { ChevronRight } from "@mui/icons-material"


const BreadcrumbsContainer = styled(MUIBreadcrumbs, {
    shouldForwardProp: (prop) => prop !== "sx",
})(({ theme }) => ({
    backgroundColor: theme.palette.background.paper,
    padding: theme.spacing(2),
    borderRadius: 0,
    borderBottom: `1px solid ${theme.palette.grey[200]}`,
}))

export default function Breadcrumbs() {
    const location = useLocation()

    const breadcrumbItems = location.pathname.split("/")
        .filter((segment) => segment.length > 0);

    return (
        <BreadcrumbsContainer aria-label="breadcrumb" separator={<ChevronRight fontSize="small" />}>
            <Link component={RouterLink} to="/" color="inherit" sx={{ display: "flex", alignItems: "center", textDecoration: "none" }}>
                <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
                Home
            </Link>
            {breadcrumbItems.map((item, index) => {
                const path = "/" + breadcrumbItems.slice(0, index + 1).join("/");
                const isLast = index === breadcrumbItems.length - 1;
                return isLast ? (
                    <Typography key={path} color="text.primary">
                        {formatSegment(item)}
                    </Typography>
                ) : (
                    <Link key={path} component={RouterLink} to={path} color="inherit">
                        {formatSegment(item)}
                    </Link>
                )
            })}
        </BreadcrumbsContainer>
    )
}

const formatSegment = (segment: string) =>
    segment.replace(/-/g, " ").replace(/\b\w/g, (c) => c.toUpperCase());
