import React, { useRef, useMemo, useTransition, JSX } from 'react';
import usePreStudyStore from '@hooks/use-pre-study-store';
import { useStore } from '@tanstack/react-store';
import { Activity, BedDouble, FilePlus2, LoaderIcon, UserCheck } from 'lucide-react';
import { Box, Chip } from '@mui/material';
import { useForm } from '@tanstack/react-form';
import { useNavigate } from '@tanstack/react-router';
import MainCardContainer from '@components/main-container/main-card-container';

import PatientChartStep from './-patient-chart-step';
import RoomSetupStep from './-room-setup-step';
import PatientCheckinStep from './-patient-checkin-step';
import VitalSignsStep from './-vital-signs-step';

const STEP_COMPONENT_MAP: Record<
	number,
	{
		title: string;
		subTitle?: string;
		icon: React.ReactNode;
		component: (props: {
			formRef: React.RefObject<ReturnType<typeof useForm> | null>;
			studyId: string;
			patientId: string;
			patientWfId: string;
		}) => JSX.Element;
	}
> = {
	0: {
		title: 'Patient Chart Review',
		subTitle: 'Review patient files and verify study information',
		icon: <FilePlus2 />,
		component: PatientChartStep,
	},
	1: {
		title: 'Room Setup & Assignment',
		subTitle: 'Prepare room and assign technician before patient arrival',
		icon: <BedDouble />,
		component: RoomSetupStep,
	},
	2: {
		title: 'Patient Check-in',
		subTitle: 'Verify patient identity and complete arrival process',
		icon: <UserCheck />,
		component: PatientCheckinStep,
	},
	3: {
		title: 'Pre-Study Vitals',
		subTitle: 'Record patient vitals and health information prior to the study (manual entry)',
		icon: <Activity />,
		component: VitalSignsStep,
	},
};

interface StepsProps {
	studyId: string;
	patientId: string;
	onCancel: () => void;
}

const Steps = ({ studyId, patientId, onCancel }: StepsProps) => {
	const navigate = useNavigate();
	const [isPending, startTransition] = useTransition();

	const { store, actions } = usePreStudyStore();
	const { totalStep, activeStep, isLastStep, values } = useStore(store, (state) => state);

	const currentStepContext = STEP_COMPONENT_MAP[activeStep];
	const StepComponent = currentStepContext?.component;

	const formRef = useRef<ReturnType<typeof useForm> | null>(null);

	const isValidNext = useMemo(() => {
		switch (activeStep) {
			case 0:
				return values.step1Completed;
			case 1:
				return values.roomSetup?.assignedRoom && values.roomSetup?.technicianAssignment;
			case 2:
				const requiredForms = values.patientCheckin?.requiredFormsUploaded;
				const allFormsUploaded =
					requiredForms?.preStudyQuestionnaire &&
					requiredForms?.sleepStudyConsent &&
					requiredForms?.hipaaAcknowledgment &&
					requiredForms?.videoRecordingConsent;
				return (
					values.patientCheckin?.patientIdentityVerified &&
					values.patientCheckin?.insuranceCardVerified &&
					values.patientCheckin?.actualArrivalTime &&
					allFormsUploaded
				);
			case 3:
				const vitalSigns = values.vitalSigns;
				return vitalSigns?.unit && vitalSigns?.bodyTemperature;
			default:
				return true;
		}
	}, [activeStep, values]);

	const onNext = () => {
		if (!isLastStep) {
			if (activeStep === 0) {
				// For step 0 (Patient Chart Review), just move to next step
				actions.moveNext();
			} else if (activeStep === 1) {
				// For step 1 (Room Setup), submit form first
				formRef.current?.handleSubmit();
			} else if (activeStep === 3) {
				// For step 3 (Vital Signs), submit form first
				formRef.current?.handleSubmit();
			} else {
				// For other steps, just move to next step
				actions.moveNext();
			}
		} else {
			navigate({
				to: '/patients/$patientId/visit',
				params: { patientId },
				search: (prev) => ({
					orderId: prev?.orderId || '',
					studyId: prev?.studyId,
				}),
			});
		}
	};

	const onBack = () => {
		startTransition(actions.moveBack);
	};

	if (!StepComponent) {
		return <div>Step not found</div>;
	}

	return (
		<MainCardContainer
			title={currentStepContext?.title || 'Pre-Study Preparation'}
			emphasis="high"
			icon={currentStepContext?.icon}
			descriptionSubheader={currentStepContext?.subTitle || ''}
			customAction={
				<Chip
					sx={{
						p: '10px',
						bgcolor: 'white',
						color: 'black',
						borderRadius: '20px',
						fontSize: '0.8125rem',
						lineHeight: '18px',
						fontWeight: 'regular',
					}}
					variant="outlined"
					label={`Step : ${activeStep + 1}/${totalStep}`}
				/>
			}
			footerProps={{
				primaryButton1: {
					label: isPending ? 'Loading...' : !isLastStep ? 'Next' : 'Save',
					'data-testid': 'pre-study-step-next',
					disabled: !isValidNext,
					onClick: onNext,
				},
				primaryButton2: {
					label: 'Save Draft',
					'data-testid': 'pre-study-step-draft',
					onClick: onBack,
				},
				secondaryButton1:
					activeStep > 0
						? {
								label: 'Back',
								'data-testid': 'pre-study-step-back',
								onClick: onBack,
							}
						: undefined,
			}}>
			{isPending ? (
				<Box sx={styles.centered}>
					<LoaderIcon />
				</Box>
			) : (
				<StepComponent
					formRef={formRef}
					studyId={studyId}
					patientId={patientId}
					patientWfId={patientWfId}
				/>
			)}
		</MainCardContainer>
	);
};

const styles = {
	centered: {
		display: 'flex',
		justifyContent: 'center',
		alignItems: 'center',
	},
};

export default Steps;
