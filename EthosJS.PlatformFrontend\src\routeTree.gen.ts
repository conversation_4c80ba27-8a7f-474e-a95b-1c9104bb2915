/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as DashboardRouteImport } from './routes/_dashboard'
import { Route as IndexRouteImport } from './routes/index'
import { Route as DashboardDashboardRouteImport } from './routes/_dashboard/_dashboard'
import { Route as DashboardTechnicianIndexRouteImport } from './routes/_dashboard/technician/index'
import { Route as DashboardScheduleIndexRouteImport } from './routes/_dashboard/schedule/index'
import { Route as DashboardPatientsIndexRouteImport } from './routes/_dashboard/patients/index'
import { Route as DashboardOrdersIndexRouteImport } from './routes/_dashboard/orders/index'
import { Route as DashboardPatientsPatientIdRouteRouteImport } from './routes/_dashboard/patients/$patientId/route'
import { Route as DashboardPatientsPatientIdVisitRouteRouteImport } from './routes/_dashboard/patients/$patientId/visit/route'
import { Route as DashboardPatientsPatientIdScoringRouteRouteImport } from './routes/_dashboard/patients/$patientId/scoring/route'
import { Route as DashboardPatientsPatientIdScheduleRouteRouteImport } from './routes/_dashboard/patients/$patientId/schedule/route'
import { Route as DashboardPatientsPatientIdPatientInformationRouteRouteImport } from './routes/_dashboard/patients/$patientId/patient-information/route'
import { Route as DashboardPatientsPatientIdOrderRouteRouteImport } from './routes/_dashboard/patients/$patientId/order/route'
import { Route as DashboardPatientsPatientIdInterpretationRouteRouteImport } from './routes/_dashboard/patients/$patientId/interpretation/route'
import { Route as DashboardPatientsPatientIdInsuranceRouteRouteImport } from './routes/_dashboard/patients/$patientId/insurance/route'
import { Route as DashboardPatientsPatientIdScheduleFollowUpCallRouteImport } from './routes/_dashboard/patients/$patientId/schedule/follow-up-call'
import { Route as DashboardPatientsPatientIdPatientInformationSchedulePreferencesRouteImport } from './routes/_dashboard/patients/$patientId/patient-information/schedule-preferences'
import { Route as DashboardPatientsPatientIdPatientInformationPatientChartsRouteImport } from './routes/_dashboard/patients/$patientId/patient-information/patient-charts'
import { Route as DashboardPatientsPatientIdPatientInformationInsurancesRouteImport } from './routes/_dashboard/patients/$patientId/patient-information/insurances'
import { Route as DashboardPatientsPatientIdPatientInformationGuardiansRouteImport } from './routes/_dashboard/patients/$patientId/patient-information/guardians'
import { Route as DashboardPatientsPatientIdPatientInformationContactInformationRouteImport } from './routes/_dashboard/patients/$patientId/patient-information/contact-information'
import { Route as DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRouteImport } from './routes/_dashboard/patients/$patientId/patient-information/clinical-considerations'
import { Route as DashboardPatientsPatientIdPatientInformationBasicInformationRouteImport } from './routes/_dashboard/patients/$patientId/patient-information/basic-information'
import { Route as DashboardPatientsPatientIdPatientInformationAddressesRouteImport } from './routes/_dashboard/patients/$patientId/patient-information/addresses'
import { Route as DashboardPatientsPatientIdOrderStudyRouteImport } from './routes/_dashboard/patients/$patientId/order/study'
import { Route as DashboardPatientsPatientIdOrderPhysiciansRouteImport } from './routes/_dashboard/patients/$patientId/order/physicians'
import { Route as DashboardPatientsPatientIdOrderConfirmationRouteImport } from './routes/_dashboard/patients/$patientId/order/confirmation'
import { Route as DashboardPatientsPatientIdOrderAssociatedInsuranceRouteImport } from './routes/_dashboard/patients/$patientId/order/associated-insurance'
import { Route as DashboardPatientsPatientIdInsurancePriorAuthorizationRouteImport } from './routes/_dashboard/patients/$patientId/insurance/prior-authorization'
import { Route as DashboardPatientsPatientIdInsuranceInsuranceVerificationRouteImport } from './routes/_dashboard/patients/$patientId/insurance/insurance-verification'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/route'
import { Route as DashboardPatientsPatientIdVisitPreStudyIndexRouteImport } from './routes/_dashboard/patients/$patientId/visit/pre-study/index'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRouteImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/notifications'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationNotesRouteImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/notes'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationFilesRouteImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/files'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/route'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRouteImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/index'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRouteImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview'
import { Route as DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRouteImport } from './routes/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create'

const DashboardRoute = DashboardRouteImport.update({
  id: '/_dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardDashboardRoute = DashboardDashboardRouteImport.update({
  id: '/_dashboard',
  getParentRoute: () => DashboardRoute,
} as any)
const DashboardTechnicianIndexRoute =
  DashboardTechnicianIndexRouteImport.update({
    id: '/technician/',
    path: '/technician/',
    getParentRoute: () => DashboardRoute,
  } as any)
const DashboardScheduleIndexRoute = DashboardScheduleIndexRouteImport.update({
  id: '/schedule/',
  path: '/schedule/',
  getParentRoute: () => DashboardRoute,
} as any)
const DashboardPatientsIndexRoute = DashboardPatientsIndexRouteImport.update({
  id: '/patients/',
  path: '/patients/',
  getParentRoute: () => DashboardRoute,
} as any)
const DashboardOrdersIndexRoute = DashboardOrdersIndexRouteImport.update({
  id: '/orders/',
  path: '/orders/',
  getParentRoute: () => DashboardRoute,
} as any)
const DashboardPatientsPatientIdRouteRoute =
  DashboardPatientsPatientIdRouteRouteImport.update({
    id: '/patients/$patientId',
    path: '/patients/$patientId',
    getParentRoute: () => DashboardRoute,
  } as any)
const DashboardPatientsPatientIdVisitRouteRoute =
  DashboardPatientsPatientIdVisitRouteRouteImport.update({
    id: '/visit',
    path: '/visit',
    getParentRoute: () => DashboardPatientsPatientIdRouteRoute,
  } as any)
const DashboardPatientsPatientIdScoringRouteRoute =
  DashboardPatientsPatientIdScoringRouteRouteImport.update({
    id: '/scoring',
    path: '/scoring',
    getParentRoute: () => DashboardPatientsPatientIdRouteRoute,
  } as any)
const DashboardPatientsPatientIdScheduleRouteRoute =
  DashboardPatientsPatientIdScheduleRouteRouteImport.update({
    id: '/schedule',
    path: '/schedule',
    getParentRoute: () => DashboardPatientsPatientIdRouteRoute,
  } as any)
const DashboardPatientsPatientIdPatientInformationRouteRoute =
  DashboardPatientsPatientIdPatientInformationRouteRouteImport.update({
    id: '/patient-information',
    path: '/patient-information',
    getParentRoute: () => DashboardPatientsPatientIdRouteRoute,
  } as any)
const DashboardPatientsPatientIdOrderRouteRoute =
  DashboardPatientsPatientIdOrderRouteRouteImport.update({
    id: '/order',
    path: '/order',
    getParentRoute: () => DashboardPatientsPatientIdRouteRoute,
  } as any)
const DashboardPatientsPatientIdInterpretationRouteRoute =
  DashboardPatientsPatientIdInterpretationRouteRouteImport.update({
    id: '/interpretation',
    path: '/interpretation',
    getParentRoute: () => DashboardPatientsPatientIdRouteRoute,
  } as any)
const DashboardPatientsPatientIdInsuranceRouteRoute =
  DashboardPatientsPatientIdInsuranceRouteRouteImport.update({
    id: '/insurance',
    path: '/insurance',
    getParentRoute: () => DashboardPatientsPatientIdRouteRoute,
  } as any)
const DashboardPatientsPatientIdScheduleFollowUpCallRoute =
  DashboardPatientsPatientIdScheduleFollowUpCallRouteImport.update({
    id: '/follow-up-call',
    path: '/follow-up-call',
    getParentRoute: () => DashboardPatientsPatientIdScheduleRouteRoute,
  } as any)
const DashboardPatientsPatientIdPatientInformationSchedulePreferencesRoute =
  DashboardPatientsPatientIdPatientInformationSchedulePreferencesRouteImport.update(
    {
      id: '/schedule-preferences',
      path: '/schedule-preferences',
      getParentRoute: () =>
        DashboardPatientsPatientIdPatientInformationRouteRoute,
    } as any,
  )
const DashboardPatientsPatientIdPatientInformationPatientChartsRoute =
  DashboardPatientsPatientIdPatientInformationPatientChartsRouteImport.update({
    id: '/patient-charts',
    path: '/patient-charts',
    getParentRoute: () =>
      DashboardPatientsPatientIdPatientInformationRouteRoute,
  } as any)
const DashboardPatientsPatientIdPatientInformationInsurancesRoute =
  DashboardPatientsPatientIdPatientInformationInsurancesRouteImport.update({
    id: '/insurances',
    path: '/insurances',
    getParentRoute: () =>
      DashboardPatientsPatientIdPatientInformationRouteRoute,
  } as any)
const DashboardPatientsPatientIdPatientInformationGuardiansRoute =
  DashboardPatientsPatientIdPatientInformationGuardiansRouteImport.update({
    id: '/guardians',
    path: '/guardians',
    getParentRoute: () =>
      DashboardPatientsPatientIdPatientInformationRouteRoute,
  } as any)
const DashboardPatientsPatientIdPatientInformationContactInformationRoute =
  DashboardPatientsPatientIdPatientInformationContactInformationRouteImport.update(
    {
      id: '/contact-information',
      path: '/contact-information',
      getParentRoute: () =>
        DashboardPatientsPatientIdPatientInformationRouteRoute,
    } as any,
  )
const DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute =
  DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRouteImport.update(
    {
      id: '/clinical-considerations',
      path: '/clinical-considerations',
      getParentRoute: () =>
        DashboardPatientsPatientIdPatientInformationRouteRoute,
    } as any,
  )
const DashboardPatientsPatientIdPatientInformationBasicInformationRoute =
  DashboardPatientsPatientIdPatientInformationBasicInformationRouteImport.update(
    {
      id: '/basic-information',
      path: '/basic-information',
      getParentRoute: () =>
        DashboardPatientsPatientIdPatientInformationRouteRoute,
    } as any,
  )
const DashboardPatientsPatientIdPatientInformationAddressesRoute =
  DashboardPatientsPatientIdPatientInformationAddressesRouteImport.update({
    id: '/addresses',
    path: '/addresses',
    getParentRoute: () =>
      DashboardPatientsPatientIdPatientInformationRouteRoute,
  } as any)
const DashboardPatientsPatientIdOrderStudyRoute =
  DashboardPatientsPatientIdOrderStudyRouteImport.update({
    id: '/study',
    path: '/study',
    getParentRoute: () => DashboardPatientsPatientIdOrderRouteRoute,
  } as any)
const DashboardPatientsPatientIdOrderPhysiciansRoute =
  DashboardPatientsPatientIdOrderPhysiciansRouteImport.update({
    id: '/physicians',
    path: '/physicians',
    getParentRoute: () => DashboardPatientsPatientIdOrderRouteRoute,
  } as any)
const DashboardPatientsPatientIdOrderConfirmationRoute =
  DashboardPatientsPatientIdOrderConfirmationRouteImport.update({
    id: '/confirmation',
    path: '/confirmation',
    getParentRoute: () => DashboardPatientsPatientIdOrderRouteRoute,
  } as any)
const DashboardPatientsPatientIdOrderAssociatedInsuranceRoute =
  DashboardPatientsPatientIdOrderAssociatedInsuranceRouteImport.update({
    id: '/associated-insurance',
    path: '/associated-insurance',
    getParentRoute: () => DashboardPatientsPatientIdOrderRouteRoute,
  } as any)
const DashboardPatientsPatientIdInsurancePriorAuthorizationRoute =
  DashboardPatientsPatientIdInsurancePriorAuthorizationRouteImport.update({
    id: '/prior-authorization',
    path: '/prior-authorization',
    getParentRoute: () => DashboardPatientsPatientIdInsuranceRouteRoute,
  } as any)
const DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute =
  DashboardPatientsPatientIdInsuranceInsuranceVerificationRouteImport.update({
    id: '/insurance-verification',
    path: '/insurance-verification',
    getParentRoute: () => DashboardPatientsPatientIdInsuranceRouteRoute,
  } as any)
const DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteImport.update({
    id: '/appointment-creation',
    path: '/appointment-creation',
    getParentRoute: () => DashboardPatientsPatientIdScheduleRouteRoute,
  } as any)
const DashboardPatientsPatientIdVisitPreStudyIndexRoute =
  DashboardPatientsPatientIdVisitPreStudyIndexRouteImport.update({
    id: '/pre-study/',
    path: '/pre-study/',
    getParentRoute: () => DashboardPatientsPatientIdVisitRouteRoute,
  } as any)
const DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRouteImport.update(
    {
      id: '/notifications',
      path: '/notifications',
      getParentRoute: () =>
        DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute,
    } as any,
  )
const DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationNotesRouteImport.update({
    id: '/notes',
    path: '/notes',
    getParentRoute: () =>
      DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute,
  } as any)
const DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationFilesRouteImport.update({
    id: '/files',
    path: '/files',
    getParentRoute: () =>
      DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute,
  } as any)
const DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteImport.update(
    {
      id: '/dashboard',
      path: '/dashboard',
      getParentRoute: () =>
        DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute,
    } as any,
  )
const DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRouteImport.update(
    {
      id: '/',
      path: '/',
      getParentRoute: () =>
        DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute,
    } as any,
  )
const DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRouteImport.update(
    {
      id: '/preview',
      path: '/preview',
      getParentRoute: () =>
        DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute,
    } as any,
  )
const DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute =
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRouteImport.update(
    {
      id: '/create',
      path: '/create',
      getParentRoute: () =>
        DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute,
    } as any,
  )

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/patients/$patientId': typeof DashboardPatientsPatientIdRouteRouteWithChildren
  '/orders': typeof DashboardOrdersIndexRoute
  '/patients': typeof DashboardPatientsIndexRoute
  '/schedule': typeof DashboardScheduleIndexRoute
  '/technician': typeof DashboardTechnicianIndexRoute
  '/patients/$patientId/insurance': typeof DashboardPatientsPatientIdInsuranceRouteRouteWithChildren
  '/patients/$patientId/interpretation': typeof DashboardPatientsPatientIdInterpretationRouteRoute
  '/patients/$patientId/order': typeof DashboardPatientsPatientIdOrderRouteRouteWithChildren
  '/patients/$patientId/patient-information': typeof DashboardPatientsPatientIdPatientInformationRouteRouteWithChildren
  '/patients/$patientId/schedule': typeof DashboardPatientsPatientIdScheduleRouteRouteWithChildren
  '/patients/$patientId/scoring': typeof DashboardPatientsPatientIdScoringRouteRoute
  '/patients/$patientId/visit': typeof DashboardPatientsPatientIdVisitRouteRouteWithChildren
  '/patients/$patientId/schedule/appointment-creation': typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteWithChildren
  '/patients/$patientId/insurance/insurance-verification': typeof DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute
  '/patients/$patientId/insurance/prior-authorization': typeof DashboardPatientsPatientIdInsurancePriorAuthorizationRoute
  '/patients/$patientId/order/associated-insurance': typeof DashboardPatientsPatientIdOrderAssociatedInsuranceRoute
  '/patients/$patientId/order/confirmation': typeof DashboardPatientsPatientIdOrderConfirmationRoute
  '/patients/$patientId/order/physicians': typeof DashboardPatientsPatientIdOrderPhysiciansRoute
  '/patients/$patientId/order/study': typeof DashboardPatientsPatientIdOrderStudyRoute
  '/patients/$patientId/patient-information/addresses': typeof DashboardPatientsPatientIdPatientInformationAddressesRoute
  '/patients/$patientId/patient-information/basic-information': typeof DashboardPatientsPatientIdPatientInformationBasicInformationRoute
  '/patients/$patientId/patient-information/clinical-considerations': typeof DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute
  '/patients/$patientId/patient-information/contact-information': typeof DashboardPatientsPatientIdPatientInformationContactInformationRoute
  '/patients/$patientId/patient-information/guardians': typeof DashboardPatientsPatientIdPatientInformationGuardiansRoute
  '/patients/$patientId/patient-information/insurances': typeof DashboardPatientsPatientIdPatientInformationInsurancesRoute
  '/patients/$patientId/patient-information/patient-charts': typeof DashboardPatientsPatientIdPatientInformationPatientChartsRoute
  '/patients/$patientId/patient-information/schedule-preferences': typeof DashboardPatientsPatientIdPatientInformationSchedulePreferencesRoute
  '/patients/$patientId/schedule/follow-up-call': typeof DashboardPatientsPatientIdScheduleFollowUpCallRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteWithChildren
  '/patients/$patientId/schedule/appointment-creation/files': typeof DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute
  '/patients/$patientId/schedule/appointment-creation/notes': typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute
  '/patients/$patientId/schedule/appointment-creation/notifications': typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute
  '/patients/$patientId/visit/pre-study': typeof DashboardPatientsPatientIdVisitPreStudyIndexRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard/create': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard/preview': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard/': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/patients/$patientId': typeof DashboardPatientsPatientIdRouteRouteWithChildren
  '/orders': typeof DashboardOrdersIndexRoute
  '/patients': typeof DashboardPatientsIndexRoute
  '/schedule': typeof DashboardScheduleIndexRoute
  '/technician': typeof DashboardTechnicianIndexRoute
  '/patients/$patientId/insurance': typeof DashboardPatientsPatientIdInsuranceRouteRouteWithChildren
  '/patients/$patientId/interpretation': typeof DashboardPatientsPatientIdInterpretationRouteRoute
  '/patients/$patientId/order': typeof DashboardPatientsPatientIdOrderRouteRouteWithChildren
  '/patients/$patientId/patient-information': typeof DashboardPatientsPatientIdPatientInformationRouteRouteWithChildren
  '/patients/$patientId/schedule': typeof DashboardPatientsPatientIdScheduleRouteRouteWithChildren
  '/patients/$patientId/scoring': typeof DashboardPatientsPatientIdScoringRouteRoute
  '/patients/$patientId/visit': typeof DashboardPatientsPatientIdVisitRouteRouteWithChildren
  '/patients/$patientId/schedule/appointment-creation': typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteWithChildren
  '/patients/$patientId/insurance/insurance-verification': typeof DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute
  '/patients/$patientId/insurance/prior-authorization': typeof DashboardPatientsPatientIdInsurancePriorAuthorizationRoute
  '/patients/$patientId/order/associated-insurance': typeof DashboardPatientsPatientIdOrderAssociatedInsuranceRoute
  '/patients/$patientId/order/confirmation': typeof DashboardPatientsPatientIdOrderConfirmationRoute
  '/patients/$patientId/order/physicians': typeof DashboardPatientsPatientIdOrderPhysiciansRoute
  '/patients/$patientId/order/study': typeof DashboardPatientsPatientIdOrderStudyRoute
  '/patients/$patientId/patient-information/addresses': typeof DashboardPatientsPatientIdPatientInformationAddressesRoute
  '/patients/$patientId/patient-information/basic-information': typeof DashboardPatientsPatientIdPatientInformationBasicInformationRoute
  '/patients/$patientId/patient-information/clinical-considerations': typeof DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute
  '/patients/$patientId/patient-information/contact-information': typeof DashboardPatientsPatientIdPatientInformationContactInformationRoute
  '/patients/$patientId/patient-information/guardians': typeof DashboardPatientsPatientIdPatientInformationGuardiansRoute
  '/patients/$patientId/patient-information/insurances': typeof DashboardPatientsPatientIdPatientInformationInsurancesRoute
  '/patients/$patientId/patient-information/patient-charts': typeof DashboardPatientsPatientIdPatientInformationPatientChartsRoute
  '/patients/$patientId/patient-information/schedule-preferences': typeof DashboardPatientsPatientIdPatientInformationSchedulePreferencesRoute
  '/patients/$patientId/schedule/follow-up-call': typeof DashboardPatientsPatientIdScheduleFollowUpCallRoute
  '/patients/$patientId/schedule/appointment-creation/files': typeof DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute
  '/patients/$patientId/schedule/appointment-creation/notes': typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute
  '/patients/$patientId/schedule/appointment-creation/notifications': typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute
  '/patients/$patientId/visit/pre-study': typeof DashboardPatientsPatientIdVisitPreStudyIndexRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard/create': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard/preview': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute
  '/patients/$patientId/schedule/appointment-creation/dashboard': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/_dashboard': typeof DashboardRouteWithChildren
  '/_dashboard/_dashboard': typeof DashboardDashboardRoute
  '/_dashboard/patients/$patientId': typeof DashboardPatientsPatientIdRouteRouteWithChildren
  '/_dashboard/orders/': typeof DashboardOrdersIndexRoute
  '/_dashboard/patients/': typeof DashboardPatientsIndexRoute
  '/_dashboard/schedule/': typeof DashboardScheduleIndexRoute
  '/_dashboard/technician/': typeof DashboardTechnicianIndexRoute
  '/_dashboard/patients/$patientId/insurance': typeof DashboardPatientsPatientIdInsuranceRouteRouteWithChildren
  '/_dashboard/patients/$patientId/interpretation': typeof DashboardPatientsPatientIdInterpretationRouteRoute
  '/_dashboard/patients/$patientId/order': typeof DashboardPatientsPatientIdOrderRouteRouteWithChildren
  '/_dashboard/patients/$patientId/patient-information': typeof DashboardPatientsPatientIdPatientInformationRouteRouteWithChildren
  '/_dashboard/patients/$patientId/schedule': typeof DashboardPatientsPatientIdScheduleRouteRouteWithChildren
  '/_dashboard/patients/$patientId/scoring': typeof DashboardPatientsPatientIdScoringRouteRoute
  '/_dashboard/patients/$patientId/visit': typeof DashboardPatientsPatientIdVisitRouteRouteWithChildren
  '/_dashboard/patients/$patientId/schedule/appointment-creation': typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteWithChildren
  '/_dashboard/patients/$patientId/insurance/insurance-verification': typeof DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute
  '/_dashboard/patients/$patientId/insurance/prior-authorization': typeof DashboardPatientsPatientIdInsurancePriorAuthorizationRoute
  '/_dashboard/patients/$patientId/order/associated-insurance': typeof DashboardPatientsPatientIdOrderAssociatedInsuranceRoute
  '/_dashboard/patients/$patientId/order/confirmation': typeof DashboardPatientsPatientIdOrderConfirmationRoute
  '/_dashboard/patients/$patientId/order/physicians': typeof DashboardPatientsPatientIdOrderPhysiciansRoute
  '/_dashboard/patients/$patientId/order/study': typeof DashboardPatientsPatientIdOrderStudyRoute
  '/_dashboard/patients/$patientId/patient-information/addresses': typeof DashboardPatientsPatientIdPatientInformationAddressesRoute
  '/_dashboard/patients/$patientId/patient-information/basic-information': typeof DashboardPatientsPatientIdPatientInformationBasicInformationRoute
  '/_dashboard/patients/$patientId/patient-information/clinical-considerations': typeof DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute
  '/_dashboard/patients/$patientId/patient-information/contact-information': typeof DashboardPatientsPatientIdPatientInformationContactInformationRoute
  '/_dashboard/patients/$patientId/patient-information/guardians': typeof DashboardPatientsPatientIdPatientInformationGuardiansRoute
  '/_dashboard/patients/$patientId/patient-information/insurances': typeof DashboardPatientsPatientIdPatientInformationInsurancesRoute
  '/_dashboard/patients/$patientId/patient-information/patient-charts': typeof DashboardPatientsPatientIdPatientInformationPatientChartsRoute
  '/_dashboard/patients/$patientId/patient-information/schedule-preferences': typeof DashboardPatientsPatientIdPatientInformationSchedulePreferencesRoute
  '/_dashboard/patients/$patientId/schedule/follow-up-call': typeof DashboardPatientsPatientIdScheduleFollowUpCallRoute
  '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteWithChildren
  '/_dashboard/patients/$patientId/schedule/appointment-creation/files': typeof DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute
  '/_dashboard/patients/$patientId/schedule/appointment-creation/notes': typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute
  '/_dashboard/patients/$patientId/schedule/appointment-creation/notifications': typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute
  '/_dashboard/patients/$patientId/visit/pre-study/': typeof DashboardPatientsPatientIdVisitPreStudyIndexRoute
  '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute
  '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute
  '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/': typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/patients/$patientId'
    | '/orders'
    | '/patients'
    | '/schedule'
    | '/technician'
    | '/patients/$patientId/insurance'
    | '/patients/$patientId/interpretation'
    | '/patients/$patientId/order'
    | '/patients/$patientId/patient-information'
    | '/patients/$patientId/schedule'
    | '/patients/$patientId/scoring'
    | '/patients/$patientId/visit'
    | '/patients/$patientId/schedule/appointment-creation'
    | '/patients/$patientId/insurance/insurance-verification'
    | '/patients/$patientId/insurance/prior-authorization'
    | '/patients/$patientId/order/associated-insurance'
    | '/patients/$patientId/order/confirmation'
    | '/patients/$patientId/order/physicians'
    | '/patients/$patientId/order/study'
    | '/patients/$patientId/patient-information/addresses'
    | '/patients/$patientId/patient-information/basic-information'
    | '/patients/$patientId/patient-information/clinical-considerations'
    | '/patients/$patientId/patient-information/contact-information'
    | '/patients/$patientId/patient-information/guardians'
    | '/patients/$patientId/patient-information/insurances'
    | '/patients/$patientId/patient-information/patient-charts'
    | '/patients/$patientId/patient-information/schedule-preferences'
    | '/patients/$patientId/schedule/follow-up-call'
    | '/patients/$patientId/schedule/appointment-creation/dashboard'
    | '/patients/$patientId/schedule/appointment-creation/files'
    | '/patients/$patientId/schedule/appointment-creation/notes'
    | '/patients/$patientId/schedule/appointment-creation/notifications'
    | '/patients/$patientId/visit/pre-study'
    | '/patients/$patientId/schedule/appointment-creation/dashboard/create'
    | '/patients/$patientId/schedule/appointment-creation/dashboard/preview'
    | '/patients/$patientId/schedule/appointment-creation/dashboard/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/patients/$patientId'
    | '/orders'
    | '/patients'
    | '/schedule'
    | '/technician'
    | '/patients/$patientId/insurance'
    | '/patients/$patientId/interpretation'
    | '/patients/$patientId/order'
    | '/patients/$patientId/patient-information'
    | '/patients/$patientId/schedule'
    | '/patients/$patientId/scoring'
    | '/patients/$patientId/visit'
    | '/patients/$patientId/schedule/appointment-creation'
    | '/patients/$patientId/insurance/insurance-verification'
    | '/patients/$patientId/insurance/prior-authorization'
    | '/patients/$patientId/order/associated-insurance'
    | '/patients/$patientId/order/confirmation'
    | '/patients/$patientId/order/physicians'
    | '/patients/$patientId/order/study'
    | '/patients/$patientId/patient-information/addresses'
    | '/patients/$patientId/patient-information/basic-information'
    | '/patients/$patientId/patient-information/clinical-considerations'
    | '/patients/$patientId/patient-information/contact-information'
    | '/patients/$patientId/patient-information/guardians'
    | '/patients/$patientId/patient-information/insurances'
    | '/patients/$patientId/patient-information/patient-charts'
    | '/patients/$patientId/patient-information/schedule-preferences'
    | '/patients/$patientId/schedule/follow-up-call'
    | '/patients/$patientId/schedule/appointment-creation/files'
    | '/patients/$patientId/schedule/appointment-creation/notes'
    | '/patients/$patientId/schedule/appointment-creation/notifications'
    | '/patients/$patientId/visit/pre-study'
    | '/patients/$patientId/schedule/appointment-creation/dashboard/create'
    | '/patients/$patientId/schedule/appointment-creation/dashboard/preview'
    | '/patients/$patientId/schedule/appointment-creation/dashboard'
  id:
    | '__root__'
    | '/'
    | '/_dashboard'
    | '/_dashboard/_dashboard'
    | '/_dashboard/patients/$patientId'
    | '/_dashboard/orders/'
    | '/_dashboard/patients/'
    | '/_dashboard/schedule/'
    | '/_dashboard/technician/'
    | '/_dashboard/patients/$patientId/insurance'
    | '/_dashboard/patients/$patientId/interpretation'
    | '/_dashboard/patients/$patientId/order'
    | '/_dashboard/patients/$patientId/patient-information'
    | '/_dashboard/patients/$patientId/schedule'
    | '/_dashboard/patients/$patientId/scoring'
    | '/_dashboard/patients/$patientId/visit'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation'
    | '/_dashboard/patients/$patientId/insurance/insurance-verification'
    | '/_dashboard/patients/$patientId/insurance/prior-authorization'
    | '/_dashboard/patients/$patientId/order/associated-insurance'
    | '/_dashboard/patients/$patientId/order/confirmation'
    | '/_dashboard/patients/$patientId/order/physicians'
    | '/_dashboard/patients/$patientId/order/study'
    | '/_dashboard/patients/$patientId/patient-information/addresses'
    | '/_dashboard/patients/$patientId/patient-information/basic-information'
    | '/_dashboard/patients/$patientId/patient-information/clinical-considerations'
    | '/_dashboard/patients/$patientId/patient-information/contact-information'
    | '/_dashboard/patients/$patientId/patient-information/guardians'
    | '/_dashboard/patients/$patientId/patient-information/insurances'
    | '/_dashboard/patients/$patientId/patient-information/patient-charts'
    | '/_dashboard/patients/$patientId/patient-information/schedule-preferences'
    | '/_dashboard/patients/$patientId/schedule/follow-up-call'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/files'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/notes'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/notifications'
    | '/_dashboard/patients/$patientId/visit/pre-study/'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview'
    | '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DashboardRoute: typeof DashboardRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_dashboard': {
      id: '/_dashboard'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof DashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_dashboard/_dashboard': {
      id: '/_dashboard/_dashboard'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof DashboardDashboardRouteImport
      parentRoute: typeof DashboardRoute
    }
    '/_dashboard/technician/': {
      id: '/_dashboard/technician/'
      path: '/technician'
      fullPath: '/technician'
      preLoaderRoute: typeof DashboardTechnicianIndexRouteImport
      parentRoute: typeof DashboardRoute
    }
    '/_dashboard/schedule/': {
      id: '/_dashboard/schedule/'
      path: '/schedule'
      fullPath: '/schedule'
      preLoaderRoute: typeof DashboardScheduleIndexRouteImport
      parentRoute: typeof DashboardRoute
    }
    '/_dashboard/patients/': {
      id: '/_dashboard/patients/'
      path: '/patients'
      fullPath: '/patients'
      preLoaderRoute: typeof DashboardPatientsIndexRouteImport
      parentRoute: typeof DashboardRoute
    }
    '/_dashboard/orders/': {
      id: '/_dashboard/orders/'
      path: '/orders'
      fullPath: '/orders'
      preLoaderRoute: typeof DashboardOrdersIndexRouteImport
      parentRoute: typeof DashboardRoute
    }
    '/_dashboard/patients/$patientId': {
      id: '/_dashboard/patients/$patientId'
      path: '/patients/$patientId'
      fullPath: '/patients/$patientId'
      preLoaderRoute: typeof DashboardPatientsPatientIdRouteRouteImport
      parentRoute: typeof DashboardRoute
    }
    '/_dashboard/patients/$patientId/visit': {
      id: '/_dashboard/patients/$patientId/visit'
      path: '/visit'
      fullPath: '/patients/$patientId/visit'
      preLoaderRoute: typeof DashboardPatientsPatientIdVisitRouteRouteImport
      parentRoute: typeof DashboardPatientsPatientIdRouteRoute
    }
    '/_dashboard/patients/$patientId/scoring': {
      id: '/_dashboard/patients/$patientId/scoring'
      path: '/scoring'
      fullPath: '/patients/$patientId/scoring'
      preLoaderRoute: typeof DashboardPatientsPatientIdScoringRouteRouteImport
      parentRoute: typeof DashboardPatientsPatientIdRouteRoute
    }
    '/_dashboard/patients/$patientId/schedule': {
      id: '/_dashboard/patients/$patientId/schedule'
      path: '/schedule'
      fullPath: '/patients/$patientId/schedule'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleRouteRouteImport
      parentRoute: typeof DashboardPatientsPatientIdRouteRoute
    }
    '/_dashboard/patients/$patientId/patient-information': {
      id: '/_dashboard/patients/$patientId/patient-information'
      path: '/patient-information'
      fullPath: '/patients/$patientId/patient-information'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationRouteRouteImport
      parentRoute: typeof DashboardPatientsPatientIdRouteRoute
    }
    '/_dashboard/patients/$patientId/order': {
      id: '/_dashboard/patients/$patientId/order'
      path: '/order'
      fullPath: '/patients/$patientId/order'
      preLoaderRoute: typeof DashboardPatientsPatientIdOrderRouteRouteImport
      parentRoute: typeof DashboardPatientsPatientIdRouteRoute
    }
    '/_dashboard/patients/$patientId/interpretation': {
      id: '/_dashboard/patients/$patientId/interpretation'
      path: '/interpretation'
      fullPath: '/patients/$patientId/interpretation'
      preLoaderRoute: typeof DashboardPatientsPatientIdInterpretationRouteRouteImport
      parentRoute: typeof DashboardPatientsPatientIdRouteRoute
    }
    '/_dashboard/patients/$patientId/insurance': {
      id: '/_dashboard/patients/$patientId/insurance'
      path: '/insurance'
      fullPath: '/patients/$patientId/insurance'
      preLoaderRoute: typeof DashboardPatientsPatientIdInsuranceRouteRouteImport
      parentRoute: typeof DashboardPatientsPatientIdRouteRoute
    }
    '/_dashboard/patients/$patientId/schedule/follow-up-call': {
      id: '/_dashboard/patients/$patientId/schedule/follow-up-call'
      path: '/follow-up-call'
      fullPath: '/patients/$patientId/schedule/follow-up-call'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleFollowUpCallRouteImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleRouteRoute
    }
    '/_dashboard/patients/$patientId/patient-information/schedule-preferences': {
      id: '/_dashboard/patients/$patientId/patient-information/schedule-preferences'
      path: '/schedule-preferences'
      fullPath: '/patients/$patientId/patient-information/schedule-preferences'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationSchedulePreferencesRouteImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteRoute
    }
    '/_dashboard/patients/$patientId/patient-information/patient-charts': {
      id: '/_dashboard/patients/$patientId/patient-information/patient-charts'
      path: '/patient-charts'
      fullPath: '/patients/$patientId/patient-information/patient-charts'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationPatientChartsRouteImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteRoute
    }
    '/_dashboard/patients/$patientId/patient-information/insurances': {
      id: '/_dashboard/patients/$patientId/patient-information/insurances'
      path: '/insurances'
      fullPath: '/patients/$patientId/patient-information/insurances'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationInsurancesRouteImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteRoute
    }
    '/_dashboard/patients/$patientId/patient-information/guardians': {
      id: '/_dashboard/patients/$patientId/patient-information/guardians'
      path: '/guardians'
      fullPath: '/patients/$patientId/patient-information/guardians'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationGuardiansRouteImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteRoute
    }
    '/_dashboard/patients/$patientId/patient-information/contact-information': {
      id: '/_dashboard/patients/$patientId/patient-information/contact-information'
      path: '/contact-information'
      fullPath: '/patients/$patientId/patient-information/contact-information'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationContactInformationRouteImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteRoute
    }
    '/_dashboard/patients/$patientId/patient-information/clinical-considerations': {
      id: '/_dashboard/patients/$patientId/patient-information/clinical-considerations'
      path: '/clinical-considerations'
      fullPath: '/patients/$patientId/patient-information/clinical-considerations'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRouteImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteRoute
    }
    '/_dashboard/patients/$patientId/patient-information/basic-information': {
      id: '/_dashboard/patients/$patientId/patient-information/basic-information'
      path: '/basic-information'
      fullPath: '/patients/$patientId/patient-information/basic-information'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationBasicInformationRouteImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteRoute
    }
    '/_dashboard/patients/$patientId/patient-information/addresses': {
      id: '/_dashboard/patients/$patientId/patient-information/addresses'
      path: '/addresses'
      fullPath: '/patients/$patientId/patient-information/addresses'
      preLoaderRoute: typeof DashboardPatientsPatientIdPatientInformationAddressesRouteImport
      parentRoute: typeof DashboardPatientsPatientIdPatientInformationRouteRoute
    }
    '/_dashboard/patients/$patientId/order/study': {
      id: '/_dashboard/patients/$patientId/order/study'
      path: '/study'
      fullPath: '/patients/$patientId/order/study'
      preLoaderRoute: typeof DashboardPatientsPatientIdOrderStudyRouteImport
      parentRoute: typeof DashboardPatientsPatientIdOrderRouteRoute
    }
    '/_dashboard/patients/$patientId/order/physicians': {
      id: '/_dashboard/patients/$patientId/order/physicians'
      path: '/physicians'
      fullPath: '/patients/$patientId/order/physicians'
      preLoaderRoute: typeof DashboardPatientsPatientIdOrderPhysiciansRouteImport
      parentRoute: typeof DashboardPatientsPatientIdOrderRouteRoute
    }
    '/_dashboard/patients/$patientId/order/confirmation': {
      id: '/_dashboard/patients/$patientId/order/confirmation'
      path: '/confirmation'
      fullPath: '/patients/$patientId/order/confirmation'
      preLoaderRoute: typeof DashboardPatientsPatientIdOrderConfirmationRouteImport
      parentRoute: typeof DashboardPatientsPatientIdOrderRouteRoute
    }
    '/_dashboard/patients/$patientId/order/associated-insurance': {
      id: '/_dashboard/patients/$patientId/order/associated-insurance'
      path: '/associated-insurance'
      fullPath: '/patients/$patientId/order/associated-insurance'
      preLoaderRoute: typeof DashboardPatientsPatientIdOrderAssociatedInsuranceRouteImport
      parentRoute: typeof DashboardPatientsPatientIdOrderRouteRoute
    }
    '/_dashboard/patients/$patientId/insurance/prior-authorization': {
      id: '/_dashboard/patients/$patientId/insurance/prior-authorization'
      path: '/prior-authorization'
      fullPath: '/patients/$patientId/insurance/prior-authorization'
      preLoaderRoute: typeof DashboardPatientsPatientIdInsurancePriorAuthorizationRouteImport
      parentRoute: typeof DashboardPatientsPatientIdInsuranceRouteRoute
    }
    '/_dashboard/patients/$patientId/insurance/insurance-verification': {
      id: '/_dashboard/patients/$patientId/insurance/insurance-verification'
      path: '/insurance-verification'
      fullPath: '/patients/$patientId/insurance/insurance-verification'
      preLoaderRoute: typeof DashboardPatientsPatientIdInsuranceInsuranceVerificationRouteImport
      parentRoute: typeof DashboardPatientsPatientIdInsuranceRouteRoute
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation'
      path: '/appointment-creation'
      fullPath: '/patients/$patientId/schedule/appointment-creation'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleRouteRoute
    }
    '/_dashboard/patients/$patientId/visit/pre-study/': {
      id: '/_dashboard/patients/$patientId/visit/pre-study/'
      path: '/pre-study'
      fullPath: '/patients/$patientId/visit/pre-study'
      preLoaderRoute: typeof DashboardPatientsPatientIdVisitPreStudyIndexRouteImport
      parentRoute: typeof DashboardPatientsPatientIdVisitRouteRoute
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/notifications': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/notifications'
      path: '/notifications'
      fullPath: '/patients/$patientId/schedule/appointment-creation/notifications'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRouteImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/notes': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/notes'
      path: '/notes'
      fullPath: '/patients/$patientId/schedule/appointment-creation/notes'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotesRouteImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/files': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/files'
      path: '/files'
      fullPath: '/patients/$patientId/schedule/appointment-creation/files'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationFilesRouteImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard'
      path: '/dashboard'
      fullPath: '/patients/$patientId/schedule/appointment-creation/dashboard'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/'
      path: '/'
      fullPath: '/patients/$patientId/schedule/appointment-creation/dashboard/'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRouteImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview'
      path: '/preview'
      fullPath: '/patients/$patientId/schedule/appointment-creation/dashboard/preview'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRouteImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute
    }
    '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create': {
      id: '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/create'
      path: '/create'
      fullPath: '/patients/$patientId/schedule/appointment-creation/dashboard/create'
      preLoaderRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRouteImport
      parentRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute
    }
  }
}

interface DashboardPatientsPatientIdInsuranceRouteRouteChildren {
  DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute: typeof DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute
  DashboardPatientsPatientIdInsurancePriorAuthorizationRoute: typeof DashboardPatientsPatientIdInsurancePriorAuthorizationRoute
}

const DashboardPatientsPatientIdInsuranceRouteRouteChildren: DashboardPatientsPatientIdInsuranceRouteRouteChildren =
  {
    DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute:
      DashboardPatientsPatientIdInsuranceInsuranceVerificationRoute,
    DashboardPatientsPatientIdInsurancePriorAuthorizationRoute:
      DashboardPatientsPatientIdInsurancePriorAuthorizationRoute,
  }

const DashboardPatientsPatientIdInsuranceRouteRouteWithChildren =
  DashboardPatientsPatientIdInsuranceRouteRoute._addFileChildren(
    DashboardPatientsPatientIdInsuranceRouteRouteChildren,
  )

interface DashboardPatientsPatientIdOrderRouteRouteChildren {
  DashboardPatientsPatientIdOrderAssociatedInsuranceRoute: typeof DashboardPatientsPatientIdOrderAssociatedInsuranceRoute
  DashboardPatientsPatientIdOrderConfirmationRoute: typeof DashboardPatientsPatientIdOrderConfirmationRoute
  DashboardPatientsPatientIdOrderPhysiciansRoute: typeof DashboardPatientsPatientIdOrderPhysiciansRoute
  DashboardPatientsPatientIdOrderStudyRoute: typeof DashboardPatientsPatientIdOrderStudyRoute
}

const DashboardPatientsPatientIdOrderRouteRouteChildren: DashboardPatientsPatientIdOrderRouteRouteChildren =
  {
    DashboardPatientsPatientIdOrderAssociatedInsuranceRoute:
      DashboardPatientsPatientIdOrderAssociatedInsuranceRoute,
    DashboardPatientsPatientIdOrderConfirmationRoute:
      DashboardPatientsPatientIdOrderConfirmationRoute,
    DashboardPatientsPatientIdOrderPhysiciansRoute:
      DashboardPatientsPatientIdOrderPhysiciansRoute,
    DashboardPatientsPatientIdOrderStudyRoute:
      DashboardPatientsPatientIdOrderStudyRoute,
  }

const DashboardPatientsPatientIdOrderRouteRouteWithChildren =
  DashboardPatientsPatientIdOrderRouteRoute._addFileChildren(
    DashboardPatientsPatientIdOrderRouteRouteChildren,
  )

interface DashboardPatientsPatientIdPatientInformationRouteRouteChildren {
  DashboardPatientsPatientIdPatientInformationAddressesRoute: typeof DashboardPatientsPatientIdPatientInformationAddressesRoute
  DashboardPatientsPatientIdPatientInformationBasicInformationRoute: typeof DashboardPatientsPatientIdPatientInformationBasicInformationRoute
  DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute: typeof DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute
  DashboardPatientsPatientIdPatientInformationContactInformationRoute: typeof DashboardPatientsPatientIdPatientInformationContactInformationRoute
  DashboardPatientsPatientIdPatientInformationGuardiansRoute: typeof DashboardPatientsPatientIdPatientInformationGuardiansRoute
  DashboardPatientsPatientIdPatientInformationInsurancesRoute: typeof DashboardPatientsPatientIdPatientInformationInsurancesRoute
  DashboardPatientsPatientIdPatientInformationPatientChartsRoute: typeof DashboardPatientsPatientIdPatientInformationPatientChartsRoute
  DashboardPatientsPatientIdPatientInformationSchedulePreferencesRoute: typeof DashboardPatientsPatientIdPatientInformationSchedulePreferencesRoute
}

const DashboardPatientsPatientIdPatientInformationRouteRouteChildren: DashboardPatientsPatientIdPatientInformationRouteRouteChildren =
  {
    DashboardPatientsPatientIdPatientInformationAddressesRoute:
      DashboardPatientsPatientIdPatientInformationAddressesRoute,
    DashboardPatientsPatientIdPatientInformationBasicInformationRoute:
      DashboardPatientsPatientIdPatientInformationBasicInformationRoute,
    DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute:
      DashboardPatientsPatientIdPatientInformationClinicalConsiderationsRoute,
    DashboardPatientsPatientIdPatientInformationContactInformationRoute:
      DashboardPatientsPatientIdPatientInformationContactInformationRoute,
    DashboardPatientsPatientIdPatientInformationGuardiansRoute:
      DashboardPatientsPatientIdPatientInformationGuardiansRoute,
    DashboardPatientsPatientIdPatientInformationInsurancesRoute:
      DashboardPatientsPatientIdPatientInformationInsurancesRoute,
    DashboardPatientsPatientIdPatientInformationPatientChartsRoute:
      DashboardPatientsPatientIdPatientInformationPatientChartsRoute,
    DashboardPatientsPatientIdPatientInformationSchedulePreferencesRoute:
      DashboardPatientsPatientIdPatientInformationSchedulePreferencesRoute,
  }

const DashboardPatientsPatientIdPatientInformationRouteRouteWithChildren =
  DashboardPatientsPatientIdPatientInformationRouteRoute._addFileChildren(
    DashboardPatientsPatientIdPatientInformationRouteRouteChildren,
  )

interface DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteChildren {
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute
}

const DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteChildren: DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteChildren =
  {
    DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationDashboardCreateRoute,
    DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationDashboardPreviewRoute,
    DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationDashboardIndexRoute,
  }

const DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteWithChildren =
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute._addFileChildren(
    DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteChildren,
  )

interface DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteChildren {
  DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteWithChildren
  DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute
  DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute
  DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute
}

const DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteChildren: DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteChildren =
  {
    DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationDashboardRouteRouteWithChildren,
    DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationFilesRoute,
    DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationNotesRoute,
    DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationNotificationsRoute,
  }

const DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteWithChildren =
  DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute._addFileChildren(
    DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteChildren,
  )

interface DashboardPatientsPatientIdScheduleRouteRouteChildren {
  DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute: typeof DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteWithChildren
  DashboardPatientsPatientIdScheduleFollowUpCallRoute: typeof DashboardPatientsPatientIdScheduleFollowUpCallRoute
}

const DashboardPatientsPatientIdScheduleRouteRouteChildren: DashboardPatientsPatientIdScheduleRouteRouteChildren =
  {
    DashboardPatientsPatientIdScheduleAppointmentCreationRouteRoute:
      DashboardPatientsPatientIdScheduleAppointmentCreationRouteRouteWithChildren,
    DashboardPatientsPatientIdScheduleFollowUpCallRoute:
      DashboardPatientsPatientIdScheduleFollowUpCallRoute,
  }

const DashboardPatientsPatientIdScheduleRouteRouteWithChildren =
  DashboardPatientsPatientIdScheduleRouteRoute._addFileChildren(
    DashboardPatientsPatientIdScheduleRouteRouteChildren,
  )

interface DashboardPatientsPatientIdVisitRouteRouteChildren {
  DashboardPatientsPatientIdVisitPreStudyIndexRoute: typeof DashboardPatientsPatientIdVisitPreStudyIndexRoute
}

const DashboardPatientsPatientIdVisitRouteRouteChildren: DashboardPatientsPatientIdVisitRouteRouteChildren =
  {
    DashboardPatientsPatientIdVisitPreStudyIndexRoute:
      DashboardPatientsPatientIdVisitPreStudyIndexRoute,
  }

const DashboardPatientsPatientIdVisitRouteRouteWithChildren =
  DashboardPatientsPatientIdVisitRouteRoute._addFileChildren(
    DashboardPatientsPatientIdVisitRouteRouteChildren,
  )

interface DashboardPatientsPatientIdRouteRouteChildren {
  DashboardPatientsPatientIdInsuranceRouteRoute: typeof DashboardPatientsPatientIdInsuranceRouteRouteWithChildren
  DashboardPatientsPatientIdInterpretationRouteRoute: typeof DashboardPatientsPatientIdInterpretationRouteRoute
  DashboardPatientsPatientIdOrderRouteRoute: typeof DashboardPatientsPatientIdOrderRouteRouteWithChildren
  DashboardPatientsPatientIdPatientInformationRouteRoute: typeof DashboardPatientsPatientIdPatientInformationRouteRouteWithChildren
  DashboardPatientsPatientIdScheduleRouteRoute: typeof DashboardPatientsPatientIdScheduleRouteRouteWithChildren
  DashboardPatientsPatientIdScoringRouteRoute: typeof DashboardPatientsPatientIdScoringRouteRoute
  DashboardPatientsPatientIdVisitRouteRoute: typeof DashboardPatientsPatientIdVisitRouteRouteWithChildren
}

const DashboardPatientsPatientIdRouteRouteChildren: DashboardPatientsPatientIdRouteRouteChildren =
  {
    DashboardPatientsPatientIdInsuranceRouteRoute:
      DashboardPatientsPatientIdInsuranceRouteRouteWithChildren,
    DashboardPatientsPatientIdInterpretationRouteRoute:
      DashboardPatientsPatientIdInterpretationRouteRoute,
    DashboardPatientsPatientIdOrderRouteRoute:
      DashboardPatientsPatientIdOrderRouteRouteWithChildren,
    DashboardPatientsPatientIdPatientInformationRouteRoute:
      DashboardPatientsPatientIdPatientInformationRouteRouteWithChildren,
    DashboardPatientsPatientIdScheduleRouteRoute:
      DashboardPatientsPatientIdScheduleRouteRouteWithChildren,
    DashboardPatientsPatientIdScoringRouteRoute:
      DashboardPatientsPatientIdScoringRouteRoute,
    DashboardPatientsPatientIdVisitRouteRoute:
      DashboardPatientsPatientIdVisitRouteRouteWithChildren,
  }

const DashboardPatientsPatientIdRouteRouteWithChildren =
  DashboardPatientsPatientIdRouteRoute._addFileChildren(
    DashboardPatientsPatientIdRouteRouteChildren,
  )

interface DashboardRouteChildren {
  DashboardDashboardRoute: typeof DashboardDashboardRoute
  DashboardPatientsPatientIdRouteRoute: typeof DashboardPatientsPatientIdRouteRouteWithChildren
  DashboardOrdersIndexRoute: typeof DashboardOrdersIndexRoute
  DashboardPatientsIndexRoute: typeof DashboardPatientsIndexRoute
  DashboardScheduleIndexRoute: typeof DashboardScheduleIndexRoute
  DashboardTechnicianIndexRoute: typeof DashboardTechnicianIndexRoute
}

const DashboardRouteChildren: DashboardRouteChildren = {
  DashboardDashboardRoute: DashboardDashboardRoute,
  DashboardPatientsPatientIdRouteRoute:
    DashboardPatientsPatientIdRouteRouteWithChildren,
  DashboardOrdersIndexRoute: DashboardOrdersIndexRoute,
  DashboardPatientsIndexRoute: DashboardPatientsIndexRoute,
  DashboardScheduleIndexRoute: DashboardScheduleIndexRoute,
  DashboardTechnicianIndexRoute: DashboardTechnicianIndexRoute,
}

const DashboardRouteWithChildren = DashboardRoute._addFileChildren(
  DashboardRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DashboardRoute: DashboardRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
