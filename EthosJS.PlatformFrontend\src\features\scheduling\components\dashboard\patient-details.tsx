import ChipSummary from '@components/chip-summary';
import CollapsibleMainCardContainer from '@components/collapsible-main-card-container';
import MainCardContainer from '@components/main-container/main-card-container';
import useDetails from '@features/scheduling/hooks/use-patient-details';
import { AccountCircleOutlined, ListAltOutlined } from '@mui/icons-material';
import { Stack } from '@mui/material';
import { Contact, PhoneCall } from 'lucide-react';

export default function PatientDetails({ patientId }: { patientId: string }) {
	const { patientDetails, isFetchingPatientDetails, fetchPatientDetailsError } = useDetails({
		patientId,
	});
	if (isFetchingPatientDetails) {
		return <div>Loading...</div>;
	}
	if (fetchPatientDetailsError) {
		return <div>Error loading patient details</div>;
	}

	return (
		<CollapsibleMainCardContainer
			mainContainerProps={{
				title: 'Patient Details',
				icon: <AccountCircleOutlined />,
			}}
			defaultCollapse>
			<Stack gap={2}>
				<MainCardContainer
					color="gray"
					emphasis="low"
					icon={<Contact />}
					title="Patient Information">
					<ChipSummary items={patientDetails.patientInformation} />
				</MainCardContainer>
				<MainCardContainer
					color="gray"
					emphasis="low"
					icon={<PhoneCall />}
					title="Contact Information">
					<ChipSummary items={patientDetails.contactInformation} />
				</MainCardContainer>
				<MainCardContainer
					color="gray"
					emphasis="low"
					icon={<ListAltOutlined />}
					title="Patient Preferences & Requirements">
					<ChipSummary
						hideSeperator
						items={patientDetails.preferences}
					/>
				</MainCardContainer>
			</Stack>
		</CollapsibleMainCardContainer>
	);
}
