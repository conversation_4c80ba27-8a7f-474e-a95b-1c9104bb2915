import { EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null } from '@client/workflows';
import { postApiDraftSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import { useQuery } from '@tanstack/react-query';
import { QueryDto, DraftQ } from '@utils/query-dsl';

interface OrderDraftSearchProps {
	query: QueryDto<DraftQ>;
}

export default function useOrderDraftSearch({ query }: OrderDraftSearchProps) {
	const {
		data: orderDrafts,
		isFetching: isFetchingOrderDrafts,
		error: fetchOrderDraftsError,
	} = useQuery({
		...postApiDraftSearchOptions({
			responseType: 'json',
			body: query as unknown as EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!query,
		initialData: {
			items: [],
		},
		select: (data) => data?.items ?? [],
	});
	return {
		orderDrafts,
		isFetchingOrderDrafts,
		fetchOrderDraftsError,
	};
}
