import { useEffect, useMemo, useState } from 'react';
import { Step } from 'src/workflows/Step';
import {
	IAliasTypeDef,
	IListOf,
	IOneOf,
	IRefType,
	IStructTypeDef,
	IType,
} from 'src/workflows/TypeDef';
import StepFormGenerator, { Field } from './step-form-generator';
import { useMutation } from '@tanstack/react-query';
import client from '@api/init';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { pascalToKebabCase } from '@utils/generator-helper';
import { queryClient } from '../../App';
import { getWorkflowStateQuery } from '@api/workflows/query-options';
import { Alert, Box, Snackbar } from '@mui/material';
import { PredefinedFormMap } from '@config/forms';
import { useStore } from '@tanstack/react-store';
import { getErrorMap } from 'zod';

/**
 * Builds form definitions for a given data structure
 */
function buildFormDefinitions(
	fieldKey: string,
	fieldConfig: IType | IStructTypeDef | IRefType,
	getType: (key: string) => IType | undefined
): Field {
	const { type } = fieldConfig;
	const validation = 'validation' in fieldConfig ? fieldConfig.validation : [];

	switch (type) {
		case 'RefType': {
			const { name } = fieldConfig as IRefType;
			const fieldType = getType(name);
			if (fieldType) {
				return buildFormDefinitions(fieldKey, fieldType, getType);
			}
			// Map basic types to form fields
			switch (name) {
				case 'Boolean':
					return {
						type: 'Checkbox',
						name: fieldKey,
						label: fieldKey,
						validation,
					};
				case 'SSN':
				case 'PhoneNumber':
				case 'Date':
					return {
						type: 'Basic',
						name: fieldKey,
						label: fieldKey,
						fieldType: name,
						validation,
					};
				default:
					return {
						type: 'Basic',
						name: fieldKey,
						label: fieldKey,
						fieldType: 'Text',
						validation,
					};
			}
		}

		case 'StructTypeDef': {
			const { name, fields } = fieldConfig as IStructTypeDef;
			if (name === 'TimeRange') {
				return {
					type: 'Basic',
					name: fieldKey,
					label: name,
					fieldType: 'TimeRange',
					validation,
				};
			}
			return {
				type: 'Form',
				name: fieldKey,
				label: name,
				fields: Object.entries(fields).map(([key, value]) =>
					buildFormDefinitions(key, value, getType)
				),
				validation,
			};
		}

		case 'AliasTypeDef': {
			const { is_categorical, target, name } = fieldConfig as IAliasTypeDef;
			if (is_categorical) {
				return {
					type: 'Options',
					name: fieldKey,
					label: fieldKey,
					options: (fieldConfig as IAliasTypeDef).default_values,
					validation,
				};
			}
			switch (name) {
				case 'Boolean':
					return {
						type: 'Checkbox',
						name: fieldKey,
						label: fieldKey,
						validation,
					};
				case 'SSN':
				case 'PhoneNumber':
				case 'Date':
					return {
						type: 'Basic',
						name: fieldKey,
						label: fieldKey,
						fieldType: name,
						validation,
					};
			}
			//@ts-ignore
			if (
				target.type === 'RefType' &&
				Object(target).hasOwnProperty('name') &&
				target.name === 'String'
			) {
				return {
					type: 'Basic',
					name: fieldKey,
					label: fieldKey,
					fieldType: 'Text',
					validation,
				};
			}
			if (target.type === 'RefType') {
				return buildFormDefinitions(fieldKey, target as IRefType, getType);
			}
			break;
		}
		//TODO: Handle ONEOF Union Type
		case 'OneOf': {
			const { types } = fieldConfig as IOneOf;
			return {
				type: 'Options',
				name: fieldKey,
				label: fieldKey,
				options: types.map((type) =>
					type.type === 'RefType' ? (type as IRefType).name : 'Unknown'
				),
				validation,
			};
		}

		case 'ListOf': {
			const { inner } = fieldConfig as IListOf;
			return {
				type: 'Array',
				name: fieldKey,
				label: fieldKey,
				innerType: buildFormDefinitions(fieldKey, inner, getType),
				validation,
			};
		}

		case 'NullableOf': {
			const { inner } = fieldConfig as IListOf;
			return buildFormDefinitions(fieldKey, inner, getType);
		}
	}

	// Default fallback
	return {
		type: 'Basic',
		name: fieldKey,
		label: fieldKey,
		fieldType: 'Text',
		validation,
	};
}

export default function StepComponent({
	step,
	workflowId,
	workflowName,
	startNextFlow,
}: {
	step: Step;
	workflowId: string;
	workflowName: string;
	startNextFlow: () => void;
}) {
	const [showToast, setShowToast] = useState(false);

	const { stepData } = useStore(step.stepStore, ({ stepData }) => ({ stepData }));

	const formDefinition: Field[] = useMemo(() => {
		const dataStructure = step.getDataStructure();
		if (dataStructure) {
			const { fields } = dataStructure as IStructTypeDef;
			return Object.entries(fields).map(([key, value]) =>
				buildFormDefinitions(key, value, step.flowRef.getStepDataStructure.bind(step.flowRef))
			);
		}
		return [];
	}, [step]);

	const {
		mutate: onSubmit,
		isPending,
		error: submitError,
		isError: isErrorSubmit,
	} = useMutation({
		mutationFn: async (data: any) => {
			return await client.post(
				`/${workflowName}/${pascalToKebabCase(step.name)}`,
				{
					inputData: data,
					instanceId: workflowId,
				},
				{
					scopes: [PatientCreate.value, PatientRead.value],
				}
			);
		},
		onSettled(data, error) {
			if (!error && data) {
				if (step.isFinal) {
					startNextFlow();
				} else {
					queryClient.invalidateQueries(getWorkflowStateQuery(workflowId, workflowName));
				}
			}
		},
	});

	const {
		mutate: onSaveDraft,
		isPending: isSavingDraft,
		isError: isErrorDraft,
		error: draftError,
		isSuccess: isSuccessDraft,
	} = useMutation({
		mutationFn: async (data: any) => {
			return await client.post(
				`/${workflowName}/${pascalToKebabCase(step.name)}/draft`,
				{
					inputData: data,
					instanceId: workflowId,
				},
				{
					scopes: [PatientCreate.value, PatientRead.value],
				}
			);
		},
		onSettled(data, error) {
			if (!error && data) {
				queryClient.invalidateQueries(getWorkflowStateQuery(workflowId, workflowName));
			}
		},
	});

	const notification = useMemo(() => {
		if (isErrorDraft) {
			setShowToast(true);
			return {
				message: (draftError as any).message,
				state: 'isErrorDraft',
			};
		}
		if (isSuccessDraft) {
			setShowToast(true);
			return {
				message: 'Draft saved successfully',
				state: 'isSuccessDraft',
			};
		}
		return {
			message: '',
			state: null,
		};
	}, [isErrorDraft, draftError, isSuccessDraft]);

	const StepPredefinedForm = PredefinedFormMap[step?.data];

	return (
		<>
			<Snackbar
				open={showToast}
				autoHideDuration={5000}
				anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
				onClose={() => setShowToast(false)}>
				<Alert
					onClose={() => setShowToast(false)}
					severity={notification.state === 'isErrorDraft' ? 'error' : 'success'}
					variant="filled"
					sx={{ width: '100%' }}>
					{notification.message}
				</Alert>
			</Snackbar>
			{StepPredefinedForm ? (
				<StepPredefinedForm
					savedData={stepData}
					errors={isErrorSubmit ? submitError?.response?.data.errors : {}}
					onSubmit={onSubmit}
					onSaveDraft={onSaveDraft}
					isLoading={isPending || isSavingDraft}
					workflowId={workflowId}
					isFinal={step.isFinal}
				/>
			) : (
				<Box sx={{ pt: 2 }}>
					<StepFormGenerator
						savedData={stepData}
						errors={isErrorSubmit ? submitError?.response?.data.errors : {}}
						formFields={formDefinition}
						onSubmit={onSubmit}
						onSaveDraft={onSaveDraft}
						isLoading={isPending || isSavingDraft}
						isFinal={step.isFinal}
					/>
				</Box>
			)}
		</>
	);
}
