import MainCardContainer from '@components/main-container/main-card-container';
import { Button, Stack } from '@mui/material';
import { Shield, User, File } from 'lucide-react';
import { useState } from 'react';
import OrderSummary from './order-summary';
import PatientSummary from './patient-summary';
import InsuranceSummary from './insurance-summary';

interface SummaryProps {
	patientId: string;
	orderId: string;
	studyId: string;
	insuranceId?: string;
}

export default function Summary({ patientId, orderId, studyId }: SummaryProps) {
	const [activeTab, setActiveTab] = useState('insurance');

	return (
		<MainCardContainer
			title="Summary"
			color="primary"
			emphasis="high"
			descriptionSubheader="Verify patient insurance information"
			descriptionText="Select the insurance to verify and submit for electronic verification.">
			<Stack
				gap={2}
				direction="column">
				<Stack
					direction="row"
					gap={2}>
					<Button
						variant={activeTab === 'insurance' ? 'contained' : 'outlined'}
						startIcon={<Shield />}
						onClick={() => setActiveTab('insurance')}>
						Insurance
					</Button>
					<Button
						variant={activeTab === 'order-information' ? 'contained' : 'outlined'}
						startIcon={<File />}
						onClick={() => setActiveTab('order-information')}>
						Order Information
					</Button>
					<Button
						variant={activeTab === 'patient-information' ? 'contained' : 'outlined'}
						startIcon={<User />}
						onClick={() => setActiveTab('patient-information')}>
						Patient Information
					</Button>
				</Stack>
				{activeTab === 'insurance' && <InsuranceSummary studyId={studyId!} />}
				{activeTab === 'order-information' && <OrderSummary orderId={orderId!} />}
				{activeTab === 'patient-information' && <PatientSummary patientId={patientId!} />}
			</Stack>
		</MainCardContainer>
	);
}
