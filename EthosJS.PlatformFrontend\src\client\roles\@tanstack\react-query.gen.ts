// This file is auto-generated by @hey-api/openapi-ts

import { type Options, getApiRoles, postApiRoles, getApiRolesBuiltin, deleteApiRolesByRoleId, getApiRolesByRoleId, putApiRolesByRoleId, deleteApiRolesByRoleIdAssignByUserId, postApiRolesByRoleIdAssignByUserId, getApiRolesAssignmentsByUserId, deleteApiRolesScopes, getApiRolesScopes, postApiRolesScopes, putApiRolesScopesByScope, deleteApiRolesByRoleIdScopes, postApiRolesByRoleIdScopes } from '../sdk.gen';
import { queryOptions, infiniteQueryOptions, type InfiniteData, type DefaultError, type UseMutationOptions } from '@tanstack/react-query';
import type { GetApiRolesData, PostApiRolesData, GetApiRolesBuiltinData, DeleteApiRolesByRoleIdData, GetApiRolesByRoleIdData, PutApiRolesByRoleIdData, DeleteApiRolesByRoleIdAssignByUserIdData, PostApiRolesByRoleIdAssignByUserIdData, GetApiRolesAssignmentsByUserIdData, DeleteApiRolesScopesData, GetApiRolesScopesData, PostApiRolesScopesData, PutApiRolesScopesByScopeData, DeleteApiRolesByRoleIdScopesData, PostApiRolesByRoleIdScopesData } from '../types.gen';
import type { AxiosError } from 'axios';
import { client as _heyApiClient } from '../client.gen';

export type QueryKey<TOptions extends Options> = [
    Pick<TOptions, 'baseURL' | 'body' | 'headers' | 'path' | 'query'> & {
        _id: string;
        _infinite?: boolean;
    }
];

const createQueryKey = <TOptions extends Options>(id: string, options?: TOptions, infinite?: boolean): [
    QueryKey<TOptions>[0]
] => {
    const params: QueryKey<TOptions>[0] = { _id: id, baseURL: (options?.client ?? _heyApiClient).getConfig().baseURL } as QueryKey<TOptions>[0];
    if (infinite) {
        params._infinite = infinite;
    }
    if (options?.body) {
        params.body = options.body;
    }
    if (options?.headers) {
        params.headers = options.headers;
    }
    if (options?.path) {
        params.path = options.path;
    }
    if (options?.query) {
        params.query = options.query;
    }
    return [
        params
    ];
};

export const getApiRolesQueryKey = (options?: Options<GetApiRolesData>) => createQueryKey('getApiRoles', options);

export const getApiRolesOptions = (options?: Options<GetApiRolesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiRoles({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiRolesQueryKey(options)
    });
};

const createInfiniteParams = <K extends Pick<QueryKey<Options>[0], 'body' | 'headers' | 'path' | 'query'>>(queryKey: QueryKey<Options>, page: K) => {
    const params = queryKey[0];
    if (page.body) {
        params.body = {
            ...queryKey[0].body as any,
            ...page.body as any
        };
    }
    if (page.headers) {
        params.headers = {
            ...queryKey[0].headers,
            ...page.headers
        };
    }
    if (page.path) {
        params.path = {
            ...queryKey[0].path as any,
            ...page.path as any
        };
    }
    if (page.query) {
        params.query = {
            ...queryKey[0].query as any,
            ...page.query as any
        };
    }
    return params as unknown as typeof page;
};

export const getApiRolesInfiniteQueryKey = (options?: Options<GetApiRolesData>): QueryKey<Options<GetApiRolesData>> => createQueryKey('getApiRoles', options, true);

export const getApiRolesInfiniteOptions = (options?: Options<GetApiRolesData>) => {
    return infiniteQueryOptions<unknown, AxiosError<DefaultError>, InfiniteData<unknown>, QueryKey<Options<GetApiRolesData>>, number | Pick<QueryKey<Options<GetApiRolesData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiRolesData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    offset: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiRoles({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiRolesInfiniteQueryKey(options)
    });
};

export const postApiRolesQueryKey = (options?: Options<PostApiRolesData>) => createQueryKey('postApiRoles', options);

export const postApiRolesOptions = (options?: Options<PostApiRolesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiRoles({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiRolesQueryKey(options)
    });
};

export const postApiRolesMutation = (options?: Partial<Options<PostApiRolesData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PostApiRolesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiRoles({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiRolesBuiltinQueryKey = (options?: Options<GetApiRolesBuiltinData>) => createQueryKey('getApiRolesBuiltin', options);

export const getApiRolesBuiltinOptions = (options?: Options<GetApiRolesBuiltinData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiRolesBuiltin({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiRolesBuiltinQueryKey(options)
    });
};

export const getApiRolesBuiltinInfiniteQueryKey = (options?: Options<GetApiRolesBuiltinData>): QueryKey<Options<GetApiRolesBuiltinData>> => createQueryKey('getApiRolesBuiltin', options, true);

export const getApiRolesBuiltinInfiniteOptions = (options?: Options<GetApiRolesBuiltinData>) => {
    return infiniteQueryOptions<unknown, AxiosError<DefaultError>, InfiniteData<unknown>, QueryKey<Options<GetApiRolesBuiltinData>>, number | Pick<QueryKey<Options<GetApiRolesBuiltinData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiRolesBuiltinData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    offset: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiRolesBuiltin({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiRolesBuiltinInfiniteQueryKey(options)
    });
};

export const deleteApiRolesByRoleIdMutation = (options?: Partial<Options<DeleteApiRolesByRoleIdData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<DeleteApiRolesByRoleIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiRolesByRoleId({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiRolesByRoleIdQueryKey = (options: Options<GetApiRolesByRoleIdData>) => createQueryKey('getApiRolesByRoleId', options);

export const getApiRolesByRoleIdOptions = (options: Options<GetApiRolesByRoleIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiRolesByRoleId({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiRolesByRoleIdQueryKey(options)
    });
};

export const putApiRolesByRoleIdMutation = (options?: Partial<Options<PutApiRolesByRoleIdData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PutApiRolesByRoleIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await putApiRolesByRoleId({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const deleteApiRolesByRoleIdAssignByUserIdMutation = (options?: Partial<Options<DeleteApiRolesByRoleIdAssignByUserIdData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<DeleteApiRolesByRoleIdAssignByUserIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiRolesByRoleIdAssignByUserId({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const postApiRolesByRoleIdAssignByUserIdQueryKey = (options: Options<PostApiRolesByRoleIdAssignByUserIdData>) => createQueryKey('postApiRolesByRoleIdAssignByUserId', options);

export const postApiRolesByRoleIdAssignByUserIdOptions = (options: Options<PostApiRolesByRoleIdAssignByUserIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiRolesByRoleIdAssignByUserId({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiRolesByRoleIdAssignByUserIdQueryKey(options)
    });
};

export const postApiRolesByRoleIdAssignByUserIdMutation = (options?: Partial<Options<PostApiRolesByRoleIdAssignByUserIdData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PostApiRolesByRoleIdAssignByUserIdData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiRolesByRoleIdAssignByUserId({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiRolesAssignmentsByUserIdQueryKey = (options: Options<GetApiRolesAssignmentsByUserIdData>) => createQueryKey('getApiRolesAssignmentsByUserId', options);

export const getApiRolesAssignmentsByUserIdOptions = (options: Options<GetApiRolesAssignmentsByUserIdData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiRolesAssignmentsByUserId({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiRolesAssignmentsByUserIdQueryKey(options)
    });
};

export const deleteApiRolesScopesMutation = (options?: Partial<Options<DeleteApiRolesScopesData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<DeleteApiRolesScopesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiRolesScopes({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const getApiRolesScopesQueryKey = (options?: Options<GetApiRolesScopesData>) => createQueryKey('getApiRolesScopes', options);

export const getApiRolesScopesOptions = (options?: Options<GetApiRolesScopesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await getApiRolesScopes({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiRolesScopesQueryKey(options)
    });
};

export const getApiRolesScopesInfiniteQueryKey = (options?: Options<GetApiRolesScopesData>): QueryKey<Options<GetApiRolesScopesData>> => createQueryKey('getApiRolesScopes', options, true);

export const getApiRolesScopesInfiniteOptions = (options?: Options<GetApiRolesScopesData>) => {
    return infiniteQueryOptions<unknown, AxiosError<DefaultError>, InfiniteData<unknown>, QueryKey<Options<GetApiRolesScopesData>>, number | Pick<QueryKey<Options<GetApiRolesScopesData>>[0], 'body' | 'headers' | 'path' | 'query'>>(
    // @ts-ignore
    {
        queryFn: async ({ pageParam, queryKey, signal }) => {
            // @ts-ignore
            const page: Pick<QueryKey<Options<GetApiRolesScopesData>>[0], 'body' | 'headers' | 'path' | 'query'> = typeof pageParam === 'object' ? pageParam : {
                query: {
                    offset: pageParam
                }
            };
            const params = createInfiniteParams(queryKey, page);
            const { data } = await getApiRolesScopes({
                ...options,
                ...params,
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: getApiRolesScopesInfiniteQueryKey(options)
    });
};

export const postApiRolesScopesQueryKey = (options?: Options<PostApiRolesScopesData>) => createQueryKey('postApiRolesScopes', options);

export const postApiRolesScopesOptions = (options?: Options<PostApiRolesScopesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiRolesScopes({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiRolesScopesQueryKey(options)
    });
};

export const postApiRolesScopesMutation = (options?: Partial<Options<PostApiRolesScopesData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PostApiRolesScopesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiRolesScopes({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const putApiRolesScopesByScopeMutation = (options?: Partial<Options<PutApiRolesScopesByScopeData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PutApiRolesScopesByScopeData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await putApiRolesScopesByScope({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const deleteApiRolesByRoleIdScopesMutation = (options?: Partial<Options<DeleteApiRolesByRoleIdScopesData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<DeleteApiRolesByRoleIdScopesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await deleteApiRolesByRoleIdScopes({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};

export const postApiRolesByRoleIdScopesQueryKey = (options: Options<PostApiRolesByRoleIdScopesData>) => createQueryKey('postApiRolesByRoleIdScopes', options);

export const postApiRolesByRoleIdScopesOptions = (options: Options<PostApiRolesByRoleIdScopesData>) => {
    return queryOptions({
        queryFn: async ({ queryKey, signal }) => {
            const { data } = await postApiRolesByRoleIdScopes({
                ...options,
                ...queryKey[0],
                signal,
                throwOnError: true
            });
            return data;
        },
        queryKey: postApiRolesByRoleIdScopesQueryKey(options)
    });
};

export const postApiRolesByRoleIdScopesMutation = (options?: Partial<Options<PostApiRolesByRoleIdScopesData>>) => {
    const mutationOptions: UseMutationOptions<unknown, AxiosError<DefaultError>, Options<PostApiRolesByRoleIdScopesData>> = {
        mutationFn: async (localOptions) => {
            const { data } = await postApiRolesByRoleIdScopes({
                ...options,
                ...localOptions,
                throwOnError: true
            });
            return data;
        }
    };
    return mutationOptions;
};