import { Box, lighten, styled } from "@mui/material";

const IconContainer = styled(Box)(({ theme }) => ({
    padding: 0.85,
    width: 57.6,
    height: 57.6,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'inherit',
    backgroundColor: lighten(theme.palette.primary.light, 1),
    borderRadius: theme.spacing(1),
    border: `1px solid ${lighten(theme.palette.primary.main, 0.85)}`,
    ...theme.applyStyles('dark', {
        backgroundColor: lighten(theme.palette.primary.light, 0.2),
        border: `1px solid ${lighten(theme.palette.primary.main, 0.3)}`,
    }),
}));

export default IconContainer;