import AssociatedInsuranceStep from '@features/order-create/components/steps/associated-insurance-step';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute('/_dashboard/patients/$patientId/order/associated-insurance')({
	component: RouteComponent,
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const navigate = Route.useNavigate();

	const onSuccessCallback = () => {
		navigate({
			to: '/patients/$patientId/order/physicians',
			params: { patientId },
			search: { orderId, studyId },
		});
	};
	return (
		<AssociatedInsuranceStep
			patientId={patientId}
			orderId={orderId!}
			studyId={studyId!}
			successCallback={onSuccessCallback}
		/>
	);
}
