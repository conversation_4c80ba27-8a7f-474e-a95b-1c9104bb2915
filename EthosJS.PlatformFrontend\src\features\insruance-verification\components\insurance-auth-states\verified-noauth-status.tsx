import MainCardContainer from '@components/main-container/main-card-container';
import { Chip } from '@mui/material';
import { ShieldCheck } from 'lucide-react';

export default function VerifiedNoAuthStatus() {
	return (
		<MainCardContainer
			title="Insurance Verification"
			headerSize="medium"
			icon={<ShieldCheck />}
			color="success"
			emphasis="high"
			descriptionSubheader="Insurance verification is complete. No prior authorization is required."
			customAction={
				<Chip
					label="Verified"
					sx={{ borderRadius: 2, color: 'success.dark', backgroundColor: 'white' }}
				/>
			}
		/>
	);
}
