import { Box, darken, lighten, styled, Typography } from "@mui/material";

const Container = styled(Box)(({ theme }) => ({
    padding: theme.spacing(2),
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    backgroundColor: lighten(theme.palette.primary.light, 0.85),
    color: darken(theme.palette.primary.dark, 0.25),
    '&:not(:last-child)': {
        borderBottom: `1px solid ${lighten(theme.palette.primary.main, 0.85)}`,
    },
}));

interface CardInfoProps {
    title: string;
    subtitle?: string;
}

export default function CardInfo({ title, subtitle }: CardInfoProps) {
    return (
        <Container>
            <Typography variant="h6" color="inherit">{title}</Typography>
            {subtitle && <Typography variant="body1" color="inherit">{subtitle}</Typography>}
        </Container>
    )
}