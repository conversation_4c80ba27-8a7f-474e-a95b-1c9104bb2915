import AddAddressesStep from '@features/patient-create/components/steps/addresses.step';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/patient-information/addresses'
)({
	component: RouteComponent,
});

function RouteComponent() {
	const { patientId } = Route.useParams();

	const navigate = Route.useNavigate();

	return (
		<AddAddressesStep
			patientId={patientId}
			successCallback={() => {
				navigate({
					to: '/patients/$patientId/patient-information/insurances',
					params: { patientId },
				});
			}}
		/>
	);
}
