import { createRootRouteWithContext, Outlet } from "@tanstack/react-router";
import type { QueryClient } from '@tanstack/react-query'
import { PublicClientApplication } from "@azure/msal-browser";

export const Route = createRootRouteWithContext<{
    queryClient: QueryClient,
    msalInstance: PublicClientApplication,
}>()({
    component: RootLayout,
});

function RootLayout() {
    return (
        <Outlet />
    )
}