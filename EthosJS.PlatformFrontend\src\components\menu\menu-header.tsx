import { styled, Typography, Stack, Box, Theme, PaletteColor } from '@mui/material';
import { ComponentType, SVGProps } from 'react';

const StyledHeader = styled(Box)(({ theme }) => ({
	flex: 1,
	display: 'flex',
	whiteSpace: 'pre',
	gap: theme.spacing(1.5),
}));

type IconVariant = 'text' | 'contained' | 'outlined';
type IconSize = 'small' | 'medium' | 'large';
type IconColor = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'gray';

const ICON_PALLETE_MAP: Record<IconColor, (theme: Theme) => PaletteColor> = {
	primary: (theme) => theme.palette.primary,
	secondary: (theme) => theme.palette.secondary,
	success: (theme) => theme.palette.success,
	warning: (theme) => theme.palette.warning,
	error: (theme) => theme.palette.error,
	gray: (theme) =>
		theme.palette.mode === 'light'
			? {
					main: theme.palette.grey[500],
					contrastText: theme.palette.common.white,
					light: theme.palette.grey[500],
					dark: theme.palette.grey[500],
				}
			: {
					main: theme.palette.grey[700],
					contrastText: theme.palette.common.white,
					light: theme.palette.grey[700],
					dark: theme.palette.grey[700],
				},
};

const IconContainer = styled(Box, {
	shouldForwardProp: (prop) => prop !== 'variant' && prop !== 'size' && prop !== 'iconColor',
})<{
	variant: IconVariant;
	size: IconSize;
	iconColor: IconColor;
}>(({ theme, variant, size, iconColor }) => {
	const palette = ICON_PALLETE_MAP[iconColor](theme);

	const sizeMap = {
		small: 32,
		medium: 40,
		large: 48,
	};

	const baseStyles = {
		display: 'flex',
		alignItems: 'center',
		justifyContent: 'center',
		width: sizeMap[size],
		height: sizeMap[size],
		borderRadius: theme.shape.borderRadius,
	};

	switch (variant) {
		case 'contained':
			return {
				...baseStyles,
				backgroundColor: palette.main,
				color: palette.contrastText,
				...theme.applyStyles('dark', {
					backgroundColor: palette.main,
					color: palette.contrastText,
				}),
			};
		case 'outlined':
			return {
				...baseStyles,
				backgroundColor: 'transparent',
				color: palette.main,
				border: `1px solid ${palette.main}`,
				...theme.applyStyles('dark', {
					color: palette.light || palette.main,
					borderColor: palette.light || palette.main,
				}),
			};
		case 'text':
		default:
			return {
				...baseStyles,
				backgroundColor: 'transparent',
				color: palette.main,
				...theme.applyStyles('dark', {
					backgroundColor: theme.palette.grey[700],
					color: palette.light || palette.main,
				}),
			};
	}
});

export interface MenuHeaderProps {
	title: string;
	subtitle: string | undefined;
	icon: ComponentType<SVGProps<SVGSVGElement>> | undefined;
	iconVariant?: IconVariant;
	iconSize?: IconSize;
	iconColor?: IconColor;
	showIcon?: boolean;
}

export default function MenuHeader({
	title,
	subtitle,
	icon: Icon,
	showIcon = true,
	iconVariant = 'contained',
	iconSize = 'medium',
	iconColor = 'primary',
}: MenuHeaderProps) {
	return (
		<StyledHeader>
			{showIcon ? (
				Icon ? (
					<IconContainer
						variant={iconVariant}
						size={iconSize}
						iconColor={iconColor}>
						<Icon
							style={{
								width: '1.5rem',
								height: '1.5rem',
								flexShrink: 0,
								color: 'inherit',
							}}
						/>
					</IconContainer>
				) : (
					<IconContainer
						variant={iconVariant}
						size={iconSize}
						iconColor={iconColor}>
						<Typography
							variant="h6"
							sx={{ fontWeight: 600, fontSize: '1.125rem' }}>
							{title.slice(0, 1)?.toUpperCase()}
						</Typography>
					</IconContainer>
				)
			) : null}
			<Stack
				maxHeight="40px"
				flex={1}
				justifyContent={'center'}
				gap={1}>
				<Typography sx={{ fontWeight: 600, fontSize: '1rem', lineHeight: 1 }}>{title}</Typography>
				{subtitle && (
					<Typography
						variant="asapCondensed"
						sx={{ fontWeight: 500, fontSize: '0.75rem', lineHeight: 'normal' }}>
						{subtitle}
					</Typography>
				)}
			</Stack>
		</StyledHeader>
	);
}
