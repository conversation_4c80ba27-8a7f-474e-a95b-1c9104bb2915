import { SchedulingState } from '../types/state-types';

const defaultSchedulingState: SchedulingState = {
    flowState: {
        progress: 0,
        status: 'NotStarted',
        lastUpdate: '',
        currentStep: 'AppointmentSelection'
    },
    stepState: {
        AppointmentSelection: 'NotStarted',
        InstructionsAndDocuments: 'NotStarted',
        ReviewAndConfirm: 'NotStarted',
        FollowUpCall: 'NotStarted',
        AppointmentConfirmation: 'NotStarted'
    },
    appointmentData: {
        selectedAppointment: {
            id: null,
            date: null,
            roomId: null,
            careLocationShiftId: null
        },
        documents: [],
        documentDeliveryMethod: [],
        followUpStatus: 'none',
        isConfirmed: false
    }
};

export { defaultSchedulingState };