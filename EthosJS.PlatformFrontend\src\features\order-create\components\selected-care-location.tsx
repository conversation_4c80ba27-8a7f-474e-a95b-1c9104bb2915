import { getApiCareLocationByIdOptions } from '@client/workflows/@tanstack/react-query.gen';
import ChipSummary from '@components/chip-summary';
import MainCardContainer from '@components/main-container/main-card-container';
import { useQuery } from '@tanstack/react-query';
import { Hospital } from 'lucide-react';

interface SelectedCareLocationProps {
	careLocationId: string;
	onEdit: () => void;
}

export default function SelectedCareLocation({
	careLocationId,
	onEdit,
}: SelectedCareLocationProps) {
	const { data: careLocation, isFetching: isFetchingCareLocation } = useQuery({
		...getApiCareLocationByIdOptions({
			path: { id: careLocationId! },
			responseType: 'json',
		}),
		enabled: !!careLocationId,
	});

	if (isFetchingCareLocation) {
		return (
			<MainCardContainer
				icon={<Hospital />}
				title="Loading Care Location..."
			/>
		);
	}

	return (
		<MainCardContainer
			title={careLocation?.name ?? ''}
			icon={<Hospital />}
			emphasis="high"
			color="primary"
			primaryActionType="Edit"
			onPrimaryAction={onEdit}>
			<ChipSummary
				variant="outlined"
				items={[
					{
						label: 'Phone',
						value: careLocation?.contactDetail?.phoneNumbers?.[0]?.phoneNumber ?? '',
					},
					{
						label: 'Email',
						value: careLocation?.contactDetail?.emails?.[0]?.email ?? '',
					},
					{
						label: 'Address',
						value: careLocation?.contactDetail?.addresses?.[0]?.address?.line1 ?? '',
					},
				]}
			/>
		</MainCardContainer>
	);
}
