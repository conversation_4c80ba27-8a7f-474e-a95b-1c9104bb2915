import MenuItem from '@components/menu/menu-item';
import SecondaryMenu from '@components/menu/secondary-menu';
import { ShieldCheck, User } from 'lucide-react';
import PriorAuthMenuItem from './prior-auth-menu-item';

interface InsuranceVerificationMenuProps {
	patientId: string;
	orderId: string;
	studyId: string;
	activePath: string;
	onClick: (path: string, orderId: string, studyId: string) => void;
}

export default function InsuranceVerificationMenu({
	patientId,
	orderId,
	studyId,
	activePath,
	onClick,
}: InsuranceVerificationMenuProps) {
	const onChange = (orderId: string, studyId?: string) => {
		onClick(`/insurance-verification`, orderId, studyId!);
	};

	return (
		<SecondaryMenu
			headerProps={{
				title: 'Insurance Verification',
				subtitle: undefined,
				icon: User,
				type: 'Workflow',
				showIcon: false,
				color: 'success',
				progress: 0,
				description: 'Complete all the patient profile details.',
			}}
			// topContainer={
			// 	<SelectionControls
			// 		patientId={patientId}
			// 		orderId={orderId}
			// 		studyId={studyId}
			// 		onChange={onChange}
			// 	/>
			// }
		>
			<MenuItem
				title="Insurance Verification"
				value="insurance-verification"
				icon={ShieldCheck}
				size="medium"
				selected={activePath.includes('/insurance-verification')}
				onClick={() => {
					onClick('/insurance-verification', orderId, studyId);
				}}
			/>
			<PriorAuthMenuItem
				studyId={studyId}
				selected={activePath.includes('/prior-authorization')}
				onClick={() => {
					onClick('/prior-authorization', orderId, studyId);
				}}
			/>
		</SecondaryMenu>
	);
}
