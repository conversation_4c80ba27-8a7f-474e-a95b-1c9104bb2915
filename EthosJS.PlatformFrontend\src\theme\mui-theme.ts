import type {} from '@mui/x-data-grid-pro/themeAugmentation';
import { CSSProperties } from '@mui/material/styles/createMixins';
import { colors, ColorSystemOptions, createTheme, ThemeOptions } from '@mui/material';

const baseThemeOptions: ThemeOptions = {
	spacing: 8, // Explicitly set the default spacing unit to 8px
	shape: {
		borderRadius: 8,
	},
	typography: {
		fontFamily: ['Plus Jakarta Sans', 'sans-serif'].join(','),
		fontSize: 12,
		fontWeightRegular: 500,
		asapCondensed: {
			fontFamily: 'Asap Condensed, sans-serif',
			fontWeight: 500,
		},
	},
	components: {
		MuiButton: {
			styleOverrides: {
				root: {
					borderRadius: 8,
					textTransform: 'none',
				} as CSSProperties,
			},
		},
		MuiChip: {
			styleOverrides: {
				root: {
					borderRadius: 4,
				},
			},
		},
		MuiButtonBase: {
			styleOverrides: {
				root: {
					borderRadius: 8,
				},
			},
		},
		MuiDataGrid: {
			styleOverrides: {
				root: ({ theme }) => {
					return {
						backgroundColor: theme.palette.background.default,
					};
				},
				columnHeader: {
					backgroundColor: '#F4EBFF',
					color: '#422F7D',
					fontWeight: 500,
				},
				'container--top': {
					backgroundColor: '#F4EBFF',
				},
			},
		},
		MuiFormControl: {
			styleOverrides: {
				root: {
					borderRadius: 8,
				},
			},
		},
		MuiTextField: {
			defaultProps: {
				variant: 'outlined',
			},
			styleOverrides: {
				root: {
					borderRadius: 8,
				},
			},
		},
	},
};

// Light theme options (using the "light" scheme)
const lightThemeOptions: ColorSystemOptions = {
	palette: {
		mode: 'light',
		primary: {
			main: '#7E56D8', // light.primary
		},
		secondary: {
			main: '#BF56D8', // light.secondary
		},
		error: {
			main: '#AF101A', // light.error
		},
		warning: {
			main: '#EF6C00', // extendedColors.Warning.color
		},
		background: {
			default: '#F9FAFB',
		},
		text: {
			primary: colors.grey[600], // light.onBackground
		},
		// Custom tertiary key (non-standard, so added as an extension)
		success: {
			main: '#12B669', // light.tertiary
			contrastText: '#FFFFFF', // light.onTertiary
		},
	},
};

// Dark theme options (using the "dark" scheme)
const darkThemeOptions: ColorSystemOptions = {
	palette: {
		mode: 'dark',
		primary: {
			main: '#D1BCFF', // dark.primary
			contrastText: '#3D0090', // dark.onPrimary
		},
		secondary: {
			main: '#F3BCFF', // dark.secondary
			contrastText: '#560068', // dark.onSecondary
		},
		error: {
			main: '#FFB3AC', // dark.error
			contrastText: '#680008', // dark.onError
		},
		warning: {
			main: '#EF6C00', // same warning color as light
		},
		background: {
			default: '#15121A', // dark.background
			paper: '#141313', // dark.surface
		},
		text: {
			primary: '#E7E0EB', // dark.onBackground
			secondary: '#E5E2E1', // dark.onSurface
		},
		// Custom tertiary key
		success: {
			main: '#88D982', // dark.tertiary
			contrastText: '#003909', // dark.onTertiary
		},
		// Extended neutral colors (same as light)
	},
};

const theme = createTheme({
	colorSchemes: {
		light: lightThemeOptions,
		// dark: darkThemeOptions,
	},
	...baseThemeOptions,
});
// export const darkTheme = createTheme({ ...darkThemeOptions, ...baseThemeOptions });

export default theme;
