import { Paper } from "@mui/material";
import { PropsWithChildren } from "react";
import StyledCardFooter from "./card-footer";

export default function StepCardControl({ children }: PropsWithChildren) {
    return (
        <StyledCardFooter
            sx={{
                p: 2,
                display: 'flex',
                justifyContent: 'flex-end',
                gap: 1.75,
                position: 'absolute',
                bottom: 0,
                left: 0,
                width: '100%',
            }}
        >
            {children}
        </StyledCardFooter>
    )
}