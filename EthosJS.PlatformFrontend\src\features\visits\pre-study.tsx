import ChipSummary from '@components/chip-summary';
import CollapsibleMainCardContainer from '@components/collapsible-main-card-container';
import MainCardContainer, {
  MainCardContainerProps
} from '@components/main-container/main-card-container';
import List from '@components/list';
import { ListAltOutlined } from '@mui/icons-material';
import { Stack, IconButton, Box, Typography } from '@mui/material';
import { Contact, MapPin, CalendarCheck, Eye, ArrowDownToLine, FileCheck2 } from 'lucide-react';
import MonitorHeartOutlinedIcon from '@mui/icons-material/MonitorHeartOutlined';
import { useQuery } from '@tanstack/react-query';
import {
  getApiPatientByIdOptions,
  getApiStudyByIdOptions
} from '@client/workflows/@tanstack/react-query.gen';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { useMemo } from 'react';
import AssignmentIndOutlinedIcon from '@mui/icons-material/AssignmentIndOutlined';

interface IDashboard {
  studyId: string;
  patientId: string;
}

type PatientDocument = {
  id: string;
  title: string;
  fileName: string;
  uploadedBy: string;
  uploadedTime: string;
};

const PATIENT_DOCUMENTS: PatientDocument[] = [
  {
    id: '1',
    title: 'Sleep Study Order & Prescription',
    fileName: 'PSG_Study_Order_Dr_Sarah_Brown_01-01-2025.pdf',
    uploadedBy: 'Dr. Sarah Brown',
    uploadedTime: '1 day ago'
  },
  {
    id: '2',
    title: 'Insurance Authorization',
    fileName: 'Insurance_Auth_Blue_Cross_12-28-2024.pdf',
    uploadedBy: 'John Scott',
    uploadedTime: '3 days ago'
  },
  {
    id: '3',
    title: 'Patient Consent Form',
    fileName: 'Patient_Consent_Sleep_Study_12-20-2024.pdf',
    uploadedBy: 'Dr. Sarah Brown',
    uploadedTime: '1 week ago'
  },
  {
    id: '4',
    title: 'Medical History Questionnaire',
    fileName: 'Medical_History_Form_12-15-2024.pdf',
    uploadedBy: 'Nurse Williams',
    uploadedTime: '2 weeks ago'
  }
];

const Dashboard = ({ studyId, patientId }: IDashboard) => {
  return (
    <Stack gap={2}>
      <PatientChart />
      <StudyDetails studyId={studyId} patientId={patientId} />
    </Stack>
  );
};

const subContainerProps: Partial<MainCardContainerProps> = {
  color: 'gray',
  emphasis: 'low'
};

export const StudyDetails = ({ studyId, patientId }: IDashboard) => {
  const { data: patientDetails } = useQuery({
    ...getApiPatientByIdOptions({
      responseType: 'json',
      scopes: [PatientCreate.value, PatientRead.value],
      path: { id: patientId }
    })
  });
  const { data: studyDetails } = useQuery({
    ...getApiStudyByIdOptions({
      responseType: 'json',
      scopes: [PatientCreate.value, PatientRead.value],
      path: { id: studyId }
    }),
    enabled: !!studyId
  });

  const identityName = useMemo(() => patientDetails?.names?.at(0), [patientDetails?.names]);

  return (
    <CollapsibleMainCardContainer
      mainContainerProps={{
        title: 'Study Details',
        icon: <MonitorHeartOutlinedIcon />
      }}
      defaultCollapse>
      <Stack gap="12px">
        <MainCardContainer {...subContainerProps} icon={<Contact />} title="Patient Details">
          <ChipSummary
            variant="outlined"
            items={[
              {
                label: 'Full Name',
                value: mergeString([identityName?.firstName, identityName?.lastName])
              },
              {
                label: 'Patient ID',
                value: patientDetails?.id?.toString() ?? ''
              },
              {
                label: 'DOB',
                value: '06/01/1969'
              }
            ]}
          />
        </MainCardContainer>
        <MainCardContainer
          {...subContainerProps}
          icon={<MonitorHeartOutlinedIcon />}
          title="Study Details">
          <ChipSummary
            variant="outlined"
            items={[
              {
                label: 'Provider',
                value: 'Dr. Sarah Brown'
              },
              {
                label: 'Encounter Type',
                value: 'Sleep'
              },
              {
                label: 'Study Type',
                value: 'PSG (Polysomnography)'
              }
            ]}
          />
        </MainCardContainer>
        <MainCardContainer
          {...subContainerProps}
          icon={<CalendarCheck />}
          title="Appointment Details">
          <ChipSummary
            variant="outlined"
            items={[
              {
                label: 'Date',
                value: 'October 22, 2025'
              },
              {
                label: 'Time',
                value: '11:00 AM - 5:00 PM'
              },
              {
                label: 'Scheduled By',
                value: 'John Scott'
              }
            ]}
          />
        </MainCardContainer>
        <MainCardContainer {...subContainerProps} icon={<MapPin />} title="Location Details">
          <ChipSummary
            variant="outlined"
            items={[
              {
                label: 'Care Location',
                value: 'Northwest Sleep Center'
              },
              {
                label: 'Phone',
                value: '(*************'
              },
              {
                label: 'Fax',
                value: '(*************'
              },
              {
                label: 'Address',
                value: '1708 S Yakima Avenue, Ste 105 Tacoma WA 98405-5307'
              }
            ]}
          />
        </MainCardContainer>
        <MainCardContainer
          {...subContainerProps}
          icon={<ListAltOutlined />}
          title="Patient Preferences & Requirements">
          <ChipSummary
            variant="outlined"
            hideSeperator
            items={[
              {
                value: 'Female technician preferred'
              },
              {
                value: 'Patient needs interpreter (Specify: Spanish)'
              },
              {
                value: 'Special accommodations required'
              },
              {
                value: 'Wheelchair accessible room needed'
              }
            ]}
          />
        </MainCardContainer>
      </Stack>
    </CollapsibleMainCardContainer>
  );
};

const mergeString = (strings: (string | undefined | null)[], devider: string = ' ') => {
  return strings.filter(Boolean).join(devider);
};

export const PatientChart = () => {
  const documentItems = PATIENT_DOCUMENTS.map((doc) => ({
    icon: <FileCheck2 />,
    title: '',
    subTitle: '',
    extra: (
      <Stack direction="row" gap={'4px'}>
        <IconButton size="small" aria-label="download document">
          <ArrowDownToLine />
        </IconButton>
        <IconButton size="small" aria-label="view document">
          <Eye />
        </IconButton>
      </Stack>
    ),
    meta: doc
  }));

  return (
    <CollapsibleMainCardContainer
      mainContainerProps={{
        title: 'Patient Chart Documents',
        icon: <AssignmentIndOutlinedIcon />
      }}
      defaultCollapse>
      <List<PatientDocument>
        items={documentItems}
        selectable={false}
        size="large"
        renderTitle={(item) => (
          <Box gap="8px">
            <Typography style={{ fontSize: '14px', fontWeight: 'semibold' }} color="primary.dark">
              {item.meta?.title}
            </Typography>
            <>
              <Typography style={{ fontSize: '12px', fontWeight: 'medium' }} color="primary.dark">
                {item.meta?.fileName}
              </Typography>
              <Typography style={{ fontSize: '12px', fontWeight: 'medium' }} color="primary.dark">
                Uploaded by {item.meta?.uploadedBy} · {item.meta?.uploadedTime}
              </Typography>
            </>
          </Box>
        )}
      />
    </CollapsibleMainCardContainer>
  );
};

export default Dashboard;
