import { ValidationErrors } from '@app-types/validation';
import { AppFormType } from '@hooks/app-form';
import { filter } from 'lodash';

export function formHasErrors(form: AppFormType, subFormName?: string) {
	const errors = form.getAllErrors().fields;
	const formErrors = filter(Object.keys(errors), (fieldNames) => {
		if (subFormName) {
			return fieldNames.startsWith(subFormName);
		}
		return fieldNames;
	});
	return formErrors.length > 0 && form.state.isDirty;
}

export function getErrorsForIndexField(
	fieldName: string,
	errorMap?: ValidationErrors
): ValidationErrors {
	if (!errorMap) return;
	const { fields } = errorMap;
	if (!fields) return;
	const fieldErrors = Object.keys(fields).reduce(
		(acc, cur) => {
			if (cur.includes(fieldName)) {
				const newKey = cur.replace(`${fieldName}.`, '');
				if (acc && !acc.fields[newKey]) {
					acc.fields[newKey] = [];
					acc.fields[newKey].push(...fields[cur]);
				}
				return acc;
			}
			return acc;
		},
		{
			fields: {},
		} as ValidationErrors
	);
	return fieldErrors;
}
