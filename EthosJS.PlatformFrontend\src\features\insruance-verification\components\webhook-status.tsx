import { <PERSON>, Chip, Stack, Typography } from '@mui/material'
import { <PERSON><PERSON>heck, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'
import MainCardContainer from '@components/main-container/main-card-container'
import { Status } from '@components/left-menu'
import type {
    AuthRequiredStatus,
    RequestStatus,
    RequestWebhook
} from '@app-types/insurance-webhook'

interface WebhookStatusProps {
    webhookData?: RequestWebhook
    isLoading?: boolean
}

const getStatusFromAuthRequired = (
    authRequired: AuthRequiredStatus,
    status: RequestStatus
): {
    status: Status
    title: string
    description: string
    icon: React.ReactNode
    color: 'primary' | 'success' | 'warning' | 'error' | 'gray'
    emphasis: 'high' | 'medium' | 'low'
} => {
    switch (authRequired) {
        case 'noData':
            return {
                status: Status.None,
                title: 'Verification Pending',
                description: 'Insurance verification is in progress. Status will update when complete.',
                icon: <ShieldCheck />,
                color: 'primary',
                emphasis: 'low'
            }
        case 'required':
            return {
                status: Status.Warning,
                title: 'Authorization Required',
                description: 'Prior authorization is required before the procedure can be scheduled.',
                icon: <AlertTriangle />,
                color: 'warning',
                emphasis: 'medium'
            }
        case 'notRequired':
            return {
                status: Status.Completed,
                title: 'No Authorization Required',
                description: 'No prior authorization is required for this procedure.',
                icon: <CheckCircle />,
                color: 'success',
                emphasis: 'low'
            }
        case 'approved':
            return {
                status: Status.Completed,
                title: 'Authorization Approved',
                description: `Authorization has been approved. Status: ${status}`,
                icon: <CheckCircle />,
                color: 'success',
                emphasis: 'low'
            }
        case 'denied':
            return {
                status: Status.Failed,
                title: 'Authorization Denied',
                description: 'The authorization request has been denied by the insurance provider.',
                icon: <XCircle />,
                color: 'error',
                emphasis: 'high'
            }
        case 'withdrawn':
            return {
                status: Status.Warning,
                title: 'Authorization Withdrawn',
                description: 'The authorization request has been withdrawn.',
                icon: <AlertTriangle />,
                color: 'warning',
                emphasis: 'low'
            }
        default:
            return {
                status: Status.None,
                title: 'Unknown Status',
                description: 'Unable to determine authorization status.',
                icon: <ShieldCheck />,
                color: 'gray',
                emphasis: 'low'
            }
    }
}

export default function WebhookStatus({ webhookData, isLoading = false }: WebhookStatusProps) {
    if (isLoading) {
        return (
            <MainCardContainer
                title="Insurance Verification"
                headerSize="medium"
                icon={<ShieldCheck />}
                color="gray"
                emphasis="low"
                descriptionSubheader="Loading verification status..."
            />
        )
    }

    if (!webhookData) {
        return (
            <MainCardContainer
                title="Insurance Verification"
                headerSize="medium"
                icon={<ShieldCheck />}
                color="gray"
                emphasis="low"
                descriptionSubheader="No verification data available"
            />
        )
    }

    const { authRequired, status, coverageStatus } = webhookData.data
    const statusInfo = getStatusFromAuthRequired(authRequired, status)

    return (
        <MainCardContainer
            title="Insurance Verification"
            headerSize="medium"
            icon={statusInfo.icon}
            color={statusInfo.color}
            emphasis={statusInfo.emphasis}
            descriptionSubheader={statusInfo.description}
            customAction={
                <Chip
                    label={statusInfo.title}
                    sx={{ borderRadius: 2 }}
                    color={statusInfo.color !== 'gray' ? statusInfo.color : undefined}
                />
            }
        >
            <Stack spacing={2} sx={{ p: 2 }}>
                <Box>
                    <Typography variant="subtitle1" fontWeight="medium">Insurance Details</Typography>
                    <Typography variant="body2">
                        Coverage Status: <strong>{coverageStatus === 'active_Coverage' ? 'Active' : 'Inactive'}</strong>
                    </Typography>
                    <Typography variant="body2">
                        Auth ID: <strong>{webhookData.data.authId || 'Not Available'}</strong>
                    </Typography>
                    {webhookData.data.caseId && (
                        <Typography variant="body2">
                            Case ID: <strong>{webhookData.data.caseId}</strong>
                        </Typography>
                    )}
                </Box>

                {webhookData.data.diagnoses && webhookData.data.diagnoses.length > 0 && (
                    <Box>
                        <Typography variant="subtitle1" fontWeight="medium">Diagnoses</Typography>
                        {webhookData.data.diagnoses.map((diagnosis, index) => (
                            <Typography key={index} variant="body2">
                                {diagnosis.code}: {diagnosis.qualifier.value}
                            </Typography>
                        ))}
                    </Box>
                )}

                <Box>
                    <Typography variant="subtitle1" fontWeight="medium">Verification Details</Typography>
                    <Typography variant="body2">
                        Decision Date: <strong>{webhookData.data.decisionDate ? new Date(webhookData.data.decisionDate).toLocaleDateString() : 'Pending'}</strong>
                    </Typography>
                    <Typography variant="body2">
                        Decision Days: <strong>{webhookData.data.decisionDays || 'N/A'}</strong>
                    </Typography>
                </Box>
            </Stack>
        </MainCardContainer>
    )
}
