import { useMemo } from 'react';
import { useBulkRefData, extractRefDataIds, resolveRefDataValue } from './use-bulk-ref-data';
import { EthosWorkflowsApiPatientDto } from '@client/workflows';

// Extended patient type with resolved reference data
export interface PatientWithRefData extends EthosWorkflowsApiPatientDto {
  resolved?: {
    demographics?: {
      gender?: string;
      birthSex?: string;
      maritalStatus?: string;
    };
    contactInformation?: {
      addresses?: Array<{
        state?: string;
        country?: string;
      }>;
    };
  };
}

export function usePatientsWithRefData(patients: EthosWorkflowsApiPatientDto[] | undefined) {
  // Extract all reference data IDs from all patients
  const refDataIds = useMemo(() => {
    if (!patients) return [];
    return extractRefDataIds(patients);
  }, [patients]);

  // Fetch all reference data in bulk
  const { refDataMap, isFetching: isRefDataFetching, error: refDataError } = useBulkRefData({
    ids: refDataIds
  });

  // Process patients and resolve reference data
  const patientsWithRefData = useMemo((): PatientWithRefData[] => {
    if (!patients || refDataMap.size === 0) return patients || [];

    return patients.map(patient => {
      const resolved: PatientWithRefData['resolved'] = {};

      // Resolve demographics
      if (patient.demographics) {
        resolved.demographics = {
          gender: resolveRefDataValue(patient.demographics.gender, refDataMap),
          birthSex: resolveRefDataValue(patient.demographics.birthSex, refDataMap),
          maritalStatus: resolveRefDataValue(patient.demographics.maritalStatus, refDataMap),
        };
      }

      // Resolve contact information
      if (patient.contactInformation) {
        resolved.contactInformation = {};

        // Resolve addresses
        if (patient.contactInformation.addresses) {
          resolved.contactInformation.addresses = patient.contactInformation.addresses.map(address => ({
            state: resolveRefDataValue(address.address?.state, refDataMap),
            country: resolveRefDataValue(address.address?.country, refDataMap),
          }));
        }
      }

      return {
        ...patient,
        resolved,
      } as PatientWithRefData;
    });
  }, [patients, refDataMap]);

  return {
    patients: patientsWithRefData,
    isRefDataFetching,
    refDataError,
  };
}
