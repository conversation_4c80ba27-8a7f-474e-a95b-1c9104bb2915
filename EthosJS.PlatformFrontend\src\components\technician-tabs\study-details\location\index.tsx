import Card from "@components/card";
import CardHeader from "@components/card-header";

import { Box, CardContent, Stack, Typography } from "@mui/material";
import styles from "@components/technician-tabs/styles";
import Summary from "@components/technician-tabs/summary";

type PSG = {
   inLab: string
   CPTCode: string
   ETCO2: string
   extendedEEG: string
   videoRecording: string
   transcutaneousCO2: string
   parasomniaAssessment: string
}

interface StudyDetailsTypeProp {
   title?: string
   values: {
      PSG: Partial<PSG>,
   }
}


const StudyDetailsLocation = (props: StudyDetailsTypeProp) => {

   const { title, values: { PSG } } = props;

   return (
      <Card>
         <CardHeader
            {...{
               title: <Typography>{title}</Typography>,
               emphasis: 'dark'
            }}
         />
         <Box sx={styles.cardBody}>
            <Stack gap={2}>
               <Stack>
                  <Card >
                     <CardHeader
                        {...{
                           title: <Typography>{'Northwest Sleep Center'}</Typography>,
                        }}
                     />
                     <CardContent>
                        <Summary
                           data={[
                              {
                                 label: 'Phone',
                                 value: PSG?.inLab as string,
                              },
                              {
                                 label: 'Fax',
                                 value: '(*************' as string
                              },
                              {
                                 label: 'Location Type',
                                 value: 'Clinic' as string
                              },
                              {
                                 label: 'Address',
                                 value: '12500 Tukwila International Blvd, Seattle, WA 98168' as string
                              },
                           ]}
                        />
                     </CardContent>
                  </Card>
               </Stack>
            </Stack>
         </Box>
      </Card>
   )
}

export default StudyDetailsLocation;