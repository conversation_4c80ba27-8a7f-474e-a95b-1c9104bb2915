import {
	MicrosoftAspNetCoreMvcProblemDetails,
	SystemTextJsonNodesJsonNode,
} from '@client/workflows';
import { postApiPatientAppointmentDraftMutation } from '@client/workflows/@tanstack/react-query.gen';
import { EthosWorkflowsApiCreatePatientAppointmentDto } from '@client/workflows';
import { useMutation } from '@tanstack/react-query';
import { useCallback } from 'react';
import { extractProblemDetails } from '@utils/errors';

export default function useAppointmentCreate({
	studyId,
	roomId,
	careLocationShiftId,
	date,
}: {
	studyId: string;
	roomId: string;
	careLocationShiftId: string;
	date: string;
}) {
	const {
		mutate: createPatientAppointment,
		isPending: isCreating,
		error,
	} = useMutation({
		...postApiPatientAppointmentDraftMutation({
			responseType: 'json',
			body: {
				studyId,
				roomId,
				careLocationShiftId,
				date,
			} as unknown as { [key: string]: SystemTextJsonNodesJsonNode },
		}),
		onError(error) {
			console.error(error);
		},
	});

	const createAppointment = useCallback(
		async (
			state?: Partial<EthosWorkflowsApiCreatePatientAppointmentDto>,
			successCallback?: (appointmentId: string) => void,
			errorCallback?: (error: MicrosoftAspNetCoreMvcProblemDetails) => void
		) => {
			createPatientAppointment(
				{
					body: {
						...state,
					} as unknown as { [key: string]: SystemTextJsonNodesJsonNode },
				},
				{
					onSuccess(mutationData) {
						const { entityId } = mutationData;
						if (successCallback && entityId) {
							successCallback(entityId);
						}
					},
					onError(error) {
						if (errorCallback) {
							const problemDetails = extractProblemDetails(error);
							errorCallback(problemDetails);
						}
					},
				}
			);
		},
		[createPatientAppointment]
	);

	return {
		createAppointment,
		isCreating,
		error,
	};
}
