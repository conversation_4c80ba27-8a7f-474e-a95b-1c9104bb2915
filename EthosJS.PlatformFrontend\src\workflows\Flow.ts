import { State } from './State';
import { Step } from './Step';
import { IFlow, ITypeDef } from './TypeDef';
import { Store } from '@tanstack/store';
import { WorkflowStateResponse } from '@models/Workflow.model';
import { Status } from '@components/left-menu';
import { pascalToSpacedWords } from '@utils/generator-helper';
import dayjs from 'dayjs';

export type StartStepKey = 'Start';

interface IFlowState {
  progress: number;
  status: Status;
  currentStep: Step | null;
  selectedStep: Step | null;
  currentState: State | null;
  workflowState: WorkflowStateResponse | null;
  lastUpdate: string;
}

export class Flow {
  name: string;
  displayName: string;
  description: string | null;
  types: Map<string, ITypeDef> = new Map();
  steps: Map<string, Step> = new Map();
  states: Map<string, State> = new Map();
  finalState: State | null = null;

  flowStore = new Store<IFlowState>({
    progress: 0,
    status: Status.None,
    currentStep: null,
    selectedStep: null,
    currentState: null,
    workflowState: null,
    lastUpdate: ''
  });

  constructor(
    { name, description, types, steps, states }: IFlow,
    extraTypes: Record<string, ITypeDef>
  ) {
    this.name = name;
    this.displayName = pascalToSpacedWords(name);
    this.description = description;
    this.types = new Map(Object.entries(types).map(([name, type]) => [name, type]));
    this.states = new Map(
      Object.entries(states).map(([name, state]) => {
        const newState = new State(state);
        if (newState.isFinal) {
          this.finalState = newState;
        }
        return [name, newState];
      })
    );
    Object.entries(extraTypes).forEach(([name, type]) => {
      this.types.set(name, type);
    });
    const stepsArray = Object.entries(steps).map(([_, step]) => {
      const newStep = new Step(step, this);
      if (newStep.to === this.finalState?.name) {
        newStep.isFinal = true;
      }
      return newStep;
    });
    this.steps = new Map(stepsArray.map((step) => [step.from ?? 'Start', step]));
    this.flowStore.setState((state) => ({
      ...state,
      currentStep: this.steps.get('Start') ?? null
    }));
  }

  getFinalState() {
    return this.finalState;
  }

  getStepDataStructure(name: string) {
    return this.types.get(name);
  }

  getStep(name: string) {
    return this.steps.get(name);
  }

  getStore() {
    return this.flowStore;
  }

  calculateFlowStatus(data: WorkflowStateResponse) {
    this.steps.forEach((step) => {
      step.calculateStepStatus(data);
    });
    let progress = 0;
    this.steps.forEach((step) => {
      if (step.getStatus() === Status.InProgress) {
        this.setCurrentStep(step);
      }
      if (step.getStatus() === Status.Completed) {
        progress++;
      }
    });
    const progressPercentage = (progress / this.steps.size) * 100;
    let lastUpdate = '';
    if (data.pastTransitions.length > 0) {
      lastUpdate = dayjs(data.pastTransitions[data.pastTransitions.length - 1].timestamp).format(
        'MMM D, YYYY h:mm A'
      );
    } else if (data.draftTransitions.length > 0) {
      lastUpdate = dayjs(data.draftTransitions[data.draftTransitions.length - 1].timestamp).format(
        'MMM D, YYYY h:mm A'
      );
    }
    this.flowStore.setState((state) => ({
      ...state,
      status: progressPercentage === 100 ? Status.Completed : Status.InProgress,
      progress: progressPercentage,
      workflowState: data,
      lastUpdate
    }));
  }

  setCurrentStep(step: Step) {
    this.flowStore.setState((state) => ({
      ...state,
      currentStep: step
    }));
  }

  setSelectedStep(step: Step) {
    this.flowStore.setState((state) => ({
      ...state,
      selectedStep: step === state.currentStep ? null : step
    }));
  }
}
