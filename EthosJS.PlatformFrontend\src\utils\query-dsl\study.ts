import { IPrimitiveQuery } from './core';

// Study primitive query types
export interface StudyQ extends IPrimitiveQuery {
	$type:
		| 'WithId'
		| 'WithOrderId'
		| 'WithAttributes'
		| 'WithStudyType'
		| 'WithEncounterType'
		| 'WithPatientId';
}

// Helper functions for StudyQ
export const StudyQuery = {
	withId: (id: string): StudyQ => ({
		$type: 'WithId',
		Id: id,
	}),

	withOrderId: (orderId: string): StudyQ => ({
		$type: 'WithOrderId',
		WithOrderId: orderId,
	}),

	withAttributes: (attributes: string[]): StudyQ => ({
		$type: 'WithAttributes',
		Attributes: attributes,
	}),

	withStudyType: (studyType: string): StudyQ => ({
		$type: 'WithStudyType',
		StudyType: studyType,
	}),

	withEncounterType: (encounterType: string): StudyQ => ({
		$type: 'WithEncounterType',
		EncounterType: encounterType,
	}),

	withPatientId: (patientId: string): StudyQ => ({
		$type: 'WithPatientId',
		PatientId: patientId,
	}),
};
