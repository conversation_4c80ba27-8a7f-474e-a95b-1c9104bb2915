import { AuthenticatedTemplate, UnauthenticatedTemplate } from "@azure/msal-react";
import { Box, Button, Typography } from "@mui/material";
import { useRouter } from "@tanstack/react-router";
import { PropsWithChildren } from "react";


export default function AuthGuard({ children }: PropsWithChildren) {

    const { navigate } = useRouter();

    return (
        <>
            <AuthenticatedTemplate>
                {children}
            </AuthenticatedTemplate>
            <UnauthenticatedTemplate>
                <Box sx={{ display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center", height: "100%", width: "100%" }}>
                    <Typography variant="h4" component="h1" align="center">
                        Please log in to continue
                    </Typography>
                    <Button onClick={() => navigate({ to: "/" })}>Login</Button>
                </Box>
            </UnauthenticatedTemplate>
        </>
    )
}