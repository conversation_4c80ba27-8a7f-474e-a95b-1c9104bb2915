import { Snackbar, Alert, SnackbarProps, AlertProps } from '@mui/material';
import { useEffect, useState } from 'react';

export interface NotificationState {
	message: string;
	severity: AlertProps['severity'];
}

// Base props that are always required
interface NotificationSnackbarProps {
	notification: NotificationState;
	snackbarProps?: SnackbarProps;
	alertProps?: AlertProps;
	open?: boolean;
	autoClose?: boolean;
	autoCloseDuration?: number;
	onCloseToast?: () => void;
}

export default function NotificationSnackbar({
	notification,
	open: openProp,
	onCloseToast,
	alertProps,
	snackbarProps,
	autoClose = true,
	autoCloseDuration = 5000,
}: NotificationSnackbarProps) {
	const [open, setOpen] = useState(!!notification.message);

	useEffect(() => {
		setOpen(!!notification.message);

		if (notification.message && autoClose) {
			setTimeout(() => {
				setOpen(false);
			}, autoCloseDuration);
		}
	}, [notification.message, autoClose, autoCloseDuration]);

	const handleClose = (_event?: React.SyntheticEvent | Event, reason?: string) => {
		if (reason === 'clickaway') {
			return;
		}
		setOpen(false);
		if (onCloseToast) {
			onCloseToast();
		}
	};

	// Use the prop value if provided, otherwise use the internal state
	const isOpen = openProp !== undefined ? openProp : open;

	return (
		<Snackbar
			open={isOpen && !!notification.message}
			autoHideDuration={autoCloseDuration}
			anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
			onClose={handleClose}
			{...snackbarProps}>
			<Alert
				onClose={handleClose}
				severity={notification.severity}
				variant="filled"
				sx={{ width: '100%' }}
				{...alertProps}>
				{notification.message}
			</Alert>
		</Snackbar>
	);
}
