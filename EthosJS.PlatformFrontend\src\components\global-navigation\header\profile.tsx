import {
  Box,
  BoxProps,
  lighten,
  List,
  Popover,
  PopoverProps,
  Typography,
  SxProps,
  useTheme,
  Avatar,
  Stack,
} from '@mui/material';
import { useId, useLayoutEffect, useRef, useState } from 'react';
import MenuItem, { IMenuItem } from '../sidebar/menu-item';

interface IProfile {
  title?: string;
  description?: string;
  popoverProps?: Partial<PopoverProps> & {
    items?: Array<IMenuItem>;
    onMenuItemSelect?: (item: IMenuItem, e: React.MouseEvent<HTMLLIElement, MouseEvent>) => void;
    title?: string
    description?: string
  };
}

const getTriggerStyle = (selected: boolean): SxProps => {
  const { palette }: any = useTheme();
  const borderColor = lighten(palette.primary.light, 0.9);
  const hoverColor = lighten(palette.primary.light, 0.8);

  return {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  };
};

const defaultPopoverPlacement: PopoverProps['anchorOrigin'] = {
  vertical: 'bottom',
  horizontal: 'left'
};

const Profile: React.FC<IProfile> = (props) => {
  const { title, description } = props;

  const { items = [], onMenuItemSelect, ...popoverProps } = props.popoverProps ?? {};

  const triggerRef = useRef<HTMLElement>(null);

  const rcPrefix = useId(),
    id = `${rcPrefix}-profile`;

  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [selected, setSelected] = useState(false);
  const [containerWidth, setContainerWidth] = useState<number>(0);

  const open = !!anchorEl;

  const menuItems = items ?? [];

  const onOpenPopover: BoxProps['onClick'] = (e) => {
    setSelected(true)
    setAnchorEl(e.currentTarget);
  };

  const onClosePopover = () => {
    setSelected(false)
    setAnchorEl(null);
  };

  const onSelectMenuItem: (
    item: IMenuItem,
    e: React.MouseEvent<HTMLLIElement, MouseEvent>
  ) => void = (item, e) => {
    onMenuItemSelect?.(item, e);
    onClosePopover();
  };

  useLayoutEffect(() => {
    if (triggerRef.current) {
      const width = triggerRef.current.clientWidth;
      setContainerWidth(width)
    }
  }, []);

  return (
    <>
      <Box ref={triggerRef} aria-describedby={id} sx={getTriggerStyle(selected)} onClick={onOpenPopover}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            cursor: 'pointer',
            p: 0.7,
            px: 1,
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
              borderRadius: 1,
            },
            borderRadius: 8,
          }}>
          <Stack>
            <Typography
              className="menu-label"
              fontSize={16}
              sx={{ userSelect: 'none', cursor: 'pointer' }}>
              {title}
            </Typography>
            <Typography
              className="menu-label"
              fontSize={12}
              sx={{ userSelect: 'none', cursor: 'pointer' }}>
              {description}
            </Typography>
          </Stack>
          <Avatar
            alt={`${name} profile image`}
            sx={{ width: 35, height: 35, ml: 1 }}
          />
        </Box>
      </Box>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={onClosePopover}
        anchorOrigin={defaultPopoverPlacement}
        {...popoverProps}
        PaperProps={{
          sx: (theme) => {
            return {
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'flex-start',
              p: 1.5,
              marginTop: 1,
              width: containerWidth,
              gap: '0.5rem',
              borderRadius: '1rem',
              border: '1px solid',
              borderColor: lighten(theme.palette.primary.light, 0.7),
              boxShadow:
                '0px 4px 8px -2px rgba(16, 24, 40, 0.10), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)'
            };
          }
        }}
        component={List}>
        {
          popoverProps?.title && (
            <Typography variant="asapCondensed" sx={{ fontSize: 16, fontWeight: 500, mb: 0, lineHeight: 0 }}>
              {popoverProps.title}
            </Typography>
          )
        }
        {
          popoverProps?.description && (
            <Typography variant="asapCondensed" sx={{ fontSize: 12, fontWeight: 500, mb: 1 }}>
              {popoverProps.description}
            </Typography>
          )
        }
        {menuItems.map((item, index) => {
          return (
            <MenuItem {...item} key={index.toString()} onClick={(e) => onSelectMenuItem(item, e)} />
          );
        })}
      </Popover>
    </>
  );
};

export type { IProfile };
export default Profile;
