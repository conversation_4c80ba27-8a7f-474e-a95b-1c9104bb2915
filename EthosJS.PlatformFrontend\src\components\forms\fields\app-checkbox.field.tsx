import { useFieldContext } from "@hooks/form-context";
import { FormControlLabel, Checkbox, FormControl, FormHelperText, FormControlLabelProps, FormControlProps } from "@mui/material";
import { FieldPropType } from "./FieldPropType";

export default function AppCheckboxField({ label, defaultChecked, formControlProps, formControlLabelProps }: FieldPropType & { defaultChecked?: boolean; formControlProps?: Partial<FormControlProps>; formControlLabelProps?: Partial<FormControlLabelProps> }) {

    const field = useFieldContext<boolean>();

    return (
        <FormControl
            {...formControlProps}
        >
            <FormControlLabel
                {...formControlLabelProps}
                control={<Checkbox defaultChecked={defaultChecked} checked={field.state.value} onChange={(_, checked) => field.handleChange(checked)} />}
                label={label}
            />
            {
                field.state.meta.errors.length > 0 && (
                    <FormHelperText error>{field.state.meta.errors.join(', ')}</FormHelperText>
                )
            }
        </FormControl>
    );
}