/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string;
  readonly VITE_WORKFLOWS_API_URL: string;
  readonly VITE_REFDATA_API_URL: string;
  readonly VITE_MSAL_CLIENT_ID: string;
  readonly VITE_MSAL_TENANT_ID: string;
  readonly VITE_MSAL_TENANT_NAME: string;
  readonly VITE_MSAL_FLOW: string;
  readonly VITE_MSAL_DEFAULT_REDIRECT_URI: string;
  readonly VITE_MSAL_POST_LOGOUT_REDIRECT_URI: string;
  readonly VITE_MUI_LICENSE_KEY: string;
  readonly VITE_APP_ENV: string;
  readonly VITE_APP_VERSION: string;
  readonly VITE_TFVC_CHANGESET: string;
  // add more custom env variables here...
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
