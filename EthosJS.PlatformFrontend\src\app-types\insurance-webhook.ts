export const webhookData: RequestWebhook[] = [
  {
    topic: 'request.updated',
    data: {
      activityLogs: [],
      apptId: 'A-124898',
      assignedOwner: 'persante',
      authChecked: false,
      authId: '',
      authRequired: 'noData',
      cancelled: false,
      caseId: '',
      coverages: [],
      coveragesErrors: [],
      coverageStatus: 'active_Coverage',
      createdDate: '2025-05-31T12:37:17.4186616+00:00',
      decisionDate: '2025-06-04T00:00:00+00:00',
      diagnoses: [
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.33',
          description: null,
          date: null
        },
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.30',
          description: null,
          date: null
        }
      ],
      effectiveDate: '2025-06-02T00:00:00+00:00',
      encounterType: 'outpatient',
      entryMethod: 'eviCore (CareCore)',
      expirationDate: '2025-06-11T00:00:00+00:00',
      id: 5695580,
      externalId: '02c90bfc-1b3e-f011-a5f1-7c1e5201dc62',
      initiatedDate: '2025-05-31T00:00:00+00:00',
      key: null,
      orderNotes: '',
      orderingProvider: {
        payerAssignedProviderIds: [],
        id: 5554634,
        firstName: 'LAVERNE',
        middleName: null,
        lastName: 'GRAVES-WASHINGTON',
        fullName: null,
        facilityName: null,
        address: null,
        addressLine1: '268 NORTH NEW RD.',
        addressLine2: null,
        city: 'PLEASANTVILLE',
        state: 'NJ',
        contactName: null,
        email: null,
        extension: null,
        fax: '************',
        npi: '**********',
        phone: '************',
        pin: null,
        placeOfServiceCode: null,
        placeOfService: null,
        role: 'Ordering Physician',
        ssn: null,
        specialtyCode: '363LF0000X',
        specialty: { code: '363LF0000X', value: 'Family' },
        submitterId: null,
        suffix: null,
        taxId: null,
        type: null,
        url: null,
        zip: '08232',
        billingType: 'institutional',
        npiType: 'Individual',
        obtainsAuth: false,
        clientId: null,
        notes: null,
        source: null
      },
      patient: {
        id: 111111,
        memberId: '**********',
        relationToSubscriber: 'Self',
        birth: '1967-08-22T00:00:00+00:00',
        firstName: 'Patrick',
        lastName: 'McDaniel',
        middleName: null,
        name: null,
        plan: null,
        gender: 'male',
        key: null,
        phone: null,
        medicalRecord: 'P-881249',
        apptId: 'A-124898',
        groupNumber: '893469843659',
        state: 'New Jersey',
        suffix: null,
        ssn: null,
        addressLine1: '119 E GREENFIELD AVE',
        addressLine2: '',
        city: 'PLEASANTVILLE',
        zipCode: '08232',
        payer: 'AETNA',
        payerId: 'AETNA'
      },
      payerNotes: 'Approved\n\nFacility Name: INSPIRA MEDICAL CENTER VINELAND - HOSP',
      placeOfService: { code: '22', value: 'On Campus-Outpatient Hospital' },
      additionalServiceTypes: null,
      primaryServiceType: null,
      procedures: [
        {
          id: 30134070,
          quantity: 1,
          procedureServiceQuantityType: { code: 'VS', value: 'Visits' },
          qualifier: { code: 'HC', value: 'CPT/HCPCS' },
          code: '95811',
          description: null,
          from: '2025-06-01T00:00:00+00:00',
          to: '2025-08-03T00:00:00+00:00',
          modifier: null,
          payerNote: 'For Utilization Management contact Evicore at ********** ',
          payerNoteUrl:
            'https://www.aetna.com/health-care-professionals/precertification/precertification-lists-results.html?ctpcode1=95811',
          authRequired: 'approved',
          authChecked: true,
          entryMethod: 'eviCore (CareCore)',
          entryMethodChecked: true
        }
      ],
      servicingProviders: [
        {
          payerAssignedProviderIds: [{ providerId: '3674509', payer: 'MEDICAID' }],
          id: 10008328,
          firstName: 'JAMES',
          middleName: null,
          lastName: "O'CONNELL",
          fullName: '',
          facilityName: 'INSPIRA MEDICAL CENTERS, INC.',
          address: null,
          addressLine1: '1505 W SHERMAN AVE',
          addressLine2: null,
          city: 'VINELAND',
          state: 'NJ',
          contactName: null,
          email: null,
          extension: null,
          fax: '************',
          npi: '**********',
          phone: '************',
          pin: null,
          placeOfServiceCode: null,
          placeOfService: null,
          role: 'Service Provider',
          ssn: null,
          specialtyCode: '282N00000X',
          specialty: { code: '282N00000X', value: 'General Acute Care Hospital' },
          submitterId: null,
          suffix: null,
          taxId: '*********',
          type: null,
          url: null,
          zip: '*********',
          billingType: 'institutional',
          npiType: 'Organizational',
          obtainsAuth: false,
          clientId: null,
          notes: null,
          source: null
        }
      ],
      status: 'Unknown',
      submissionTime: 155000,
      referralAuthId: null,
      referralAuthUpdateDate: null,
      medianDecisionDays: null,
      priority: null,
      appointmentType: null,
      orderType: null,
      encounterId: null,
      isDeleted: false,
      statusCheck: false,
      latestCoverageExternalCheckDate: null,
      latestCoverageUpdatedDate: '2025-05-31T12:37:18+00:00',
      daysOutClosed: 0,
      daysOutInitiated: 0,
      decisionDays: 2,
      favorite: false,
      orderTypeId: '',
      orderTypeName: 'Sleep Management',
      requestType: 'normal',
      rescheduleCount: 0,
      rescheduled: false,
      isProcessing: false
    },
    webhookId: '4764918a-f5a2-ef11-88cf-000d3a14bf3d',
    enrollmentId: 'cd9d812c-a56d-4bdd-a817-51f951de937f',
    relatedEntityId: '2132895892359823598',
    relatedEntityType: 'Request'
  },
  {
    topic: 'request.updated',
    data: {
      activityLogs: [],
      apptId: 'A-124898',
      assignedOwner: 'persante',
      authChecked: false,
      authId: '',
      authRequired: 'noData',
      cancelled: false,
      caseId: '',
      coverages: [],
      coveragesErrors: [],
      coverageStatus: 'inactive',
      createdDate: '2025-05-31T12:37:17.4186616+00:00',
      decisionDate: '2025-06-04T00:00:00+00:00',
      diagnoses: [
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.33',
          description: null,
          date: null
        },
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.30',
          description: null,
          date: null
        }
      ],
      effectiveDate: '2025-06-02T00:00:00+00:00',
      encounterType: 'outpatient',
      entryMethod: 'eviCore (CareCore)',
      expirationDate: '2025-06-11T00:00:00+00:00',
      id: 5695580,
      externalId: '02c90bfc-1b3e-f011-a5f1-7c1e5201dc62',
      initiatedDate: '2025-05-31T00:00:00+00:00',
      key: null,
      orderNotes: '',
      orderingProvider: {
        payerAssignedProviderIds: [],
        id: 5554634,
        firstName: 'LAVERNE',
        middleName: null,
        lastName: 'GRAVES-WASHINGTON',
        fullName: null,
        facilityName: null,
        address: null,
        addressLine1: '268 NORTH NEW RD.',
        addressLine2: null,
        city: 'PLEASANTVILLE',
        state: 'NJ',
        contactName: null,
        email: null,
        extension: null,
        fax: '************',
        npi: '**********',
        phone: '************',
        pin: null,
        placeOfServiceCode: null,
        placeOfService: null,
        role: 'Ordering Physician',
        ssn: null,
        specialtyCode: '363LF0000X',
        specialty: { code: '363LF0000X', value: 'Family' },
        submitterId: null,
        suffix: null,
        taxId: null,
        type: null,
        url: null,
        zip: '08232',
        billingType: 'institutional',
        npiType: 'Individual',
        obtainsAuth: false,
        clientId: null,
        notes: null,
        source: null
      },
      patient: {
        id: 111111,
        memberId: '**********',
        relationToSubscriber: 'Self',
        birth: '1967-08-22T00:00:00+00:00',
        firstName: 'Patrick',
        lastName: 'McDaniel',
        middleName: null,
        name: null,
        plan: null,
        gender: 'male',
        key: null,
        phone: null,
        medicalRecord: 'P-881249',
        apptId: 'A-124898',
        groupNumber: '893469843659',
        state: 'New Jersey',
        suffix: null,
        ssn: null,
        addressLine1: '119 E GREENFIELD AVE',
        addressLine2: '',
        city: 'PLEASANTVILLE',
        zipCode: '08232',
        payer: 'AETNA',
        payerId: 'AETNA'
      },
      payerNotes: 'Approved\n\nFacility Name: INSPIRA MEDICAL CENTER VINELAND - HOSP',
      placeOfService: { code: '22', value: 'On Campus-Outpatient Hospital' },
      additionalServiceTypes: null,
      primaryServiceType: null,
      procedures: [
        {
          id: 30134070,
          quantity: 1,
          procedureServiceQuantityType: { code: 'VS', value: 'Visits' },
          qualifier: { code: 'HC', value: 'CPT/HCPCS' },
          code: '95811',
          description: null,
          from: '2025-06-01T00:00:00+00:00',
          to: '2025-08-03T00:00:00+00:00',
          modifier: null,
          payerNote: 'For Utilization Management contact Evicore at ********** ',
          payerNoteUrl:
            'https://www.aetna.com/health-care-professionals/precertification/precertification-lists-results.html?ctpcode1=95811',
          authRequired: 'approved',
          authChecked: true,
          entryMethod: 'eviCore (CareCore)',
          entryMethodChecked: true
        }
      ],
      servicingProviders: [
        {
          payerAssignedProviderIds: [{ providerId: '3674509', payer: 'MEDICAID' }],
          id: 10008328,
          firstName: 'JAMES',
          middleName: null,
          lastName: "O'CONNELL",
          fullName: '',
          facilityName: 'INSPIRA MEDICAL CENTERS, INC.',
          address: null,
          addressLine1: '1505 W SHERMAN AVE',
          addressLine2: null,
          city: 'VINELAND',
          state: 'NJ',
          contactName: null,
          email: null,
          extension: null,
          fax: '************',
          npi: '**********',
          phone: '************',
          pin: null,
          placeOfServiceCode: null,
          placeOfService: null,
          role: 'Service Provider',
          ssn: null,
          specialtyCode: '282N00000X',
          specialty: { code: '282N00000X', value: 'General Acute Care Hospital' },
          submitterId: null,
          suffix: null,
          taxId: '*********',
          type: null,
          url: null,
          zip: '*********',
          billingType: 'institutional',
          npiType: 'Organizational',
          obtainsAuth: false,
          clientId: null,
          notes: null,
          source: null
        }
      ],
      status: 'Inactive',
      submissionTime: 155000,
      referralAuthId: null,
      referralAuthUpdateDate: null,
      medianDecisionDays: null,
      priority: null,
      appointmentType: null,
      orderType: null,
      encounterId: null,
      isDeleted: false,
      statusCheck: false,
      latestCoverageExternalCheckDate: null,
      latestCoverageUpdatedDate: '2025-05-31T12:37:18+00:00',
      daysOutClosed: 0,
      daysOutInitiated: 0,
      decisionDays: 2,
      favorite: false,
      orderTypeId: '',
      orderTypeName: 'Sleep Management',
      requestType: 'normal',
      rescheduleCount: 0,
      rescheduled: false,
      isProcessing: false
    },
    webhookId: '4764918a-f5a2-ef11-88cf-000d3a14bf3d',
    enrollmentId: 'cd9d812c-a56d-4bdd-a817-51f951de937f',
    relatedEntityId: '2132895892359823598',
    relatedEntityType: 'Request'
  },
  {
    topic: 'request.updated',
    data: {
      activityLogs: [],
      apptId: 'A-124898',
      assignedOwner: 'persante',
      authChecked: false,
      authId: '',
      authRequired: 'required',
      cancelled: false,
      caseId: '',
      coverages: [],
      coveragesErrors: [],
      coverageStatus: 'active_Coverage',
      createdDate: '2025-05-31T12:37:17.4186616+00:00',
      decisionDate: '2025-06-04T00:00:00+00:00',
      diagnoses: [
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.33',
          description: null,
          date: null
        },
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.30',
          description: null,
          date: null
        }
      ],
      effectiveDate: '2025-06-02T00:00:00+00:00',
      encounterType: 'outpatient',
      entryMethod: 'eviCore (CareCore)',
      expirationDate: '2025-06-11T00:00:00+00:00',
      id: 5695580,
      externalId: '02c90bfc-1b3e-f011-a5f1-7c1e5201dc62',
      initiatedDate: '2025-05-31T00:00:00+00:00',
      key: null,
      orderNotes: '',
      orderingProvider: {
        payerAssignedProviderIds: [],
        id: 5554634,
        firstName: 'LAVERNE',
        middleName: null,
        lastName: 'GRAVES-WASHINGTON',
        fullName: null,
        facilityName: null,
        address: null,
        addressLine1: '268 NORTH NEW RD.',
        addressLine2: null,
        city: 'PLEASANTVILLE',
        state: 'NJ',
        contactName: null,
        email: null,
        extension: null,
        fax: '************',
        npi: '**********',
        phone: '************',
        pin: null,
        placeOfServiceCode: null,
        placeOfService: null,
        role: 'Ordering Physician',
        ssn: null,
        specialtyCode: '363LF0000X',
        specialty: { code: '363LF0000X', value: 'Family' },
        submitterId: null,
        suffix: null,
        taxId: null,
        type: null,
        url: null,
        zip: '08232',
        billingType: 'institutional',
        npiType: 'Individual',
        obtainsAuth: false,
        clientId: null,
        notes: null,
        source: null
      },
      patient: {
        id: 111111,
        memberId: '**********',
        relationToSubscriber: 'Self',
        birth: '1967-08-22T00:00:00+00:00',
        firstName: 'Patrick',
        lastName: 'McDaniel',
        middleName: null,
        name: null,
        plan: null,
        gender: 'male',
        key: null,
        phone: null,
        medicalRecord: 'P-881249',
        apptId: 'A-124898',
        groupNumber: '893469843659',
        state: 'New Jersey',
        suffix: null,
        ssn: null,
        addressLine1: '119 E GREENFIELD AVE',
        addressLine2: '',
        city: 'PLEASANTVILLE',
        zipCode: '08232',
        payer: 'AETNA',
        payerId: 'AETNA'
      },
      payerNotes: 'Approved\n\nFacility Name: INSPIRA MEDICAL CENTER VINELAND - HOSP',
      placeOfService: { code: '22', value: 'On Campus-Outpatient Hospital' },
      additionalServiceTypes: null,
      primaryServiceType: null,
      procedures: [
        {
          id: 30134070,
          quantity: 1,
          procedureServiceQuantityType: { code: 'VS', value: 'Visits' },
          qualifier: { code: 'HC', value: 'CPT/HCPCS' },
          code: '95811',
          description: null,
          from: '2025-06-01T00:00:00+00:00',
          to: '2025-08-03T00:00:00+00:00',
          modifier: null,
          payerNote: 'For Utilization Management contact Evicore at ********** ',
          payerNoteUrl:
            'https://www.aetna.com/health-care-professionals/precertification/precertification-lists-results.html?ctpcode1=95811',
          authRequired: 'approved',
          authChecked: true,
          entryMethod: 'eviCore (CareCore)',
          entryMethodChecked: true
        }
      ],
      servicingProviders: [
        {
          payerAssignedProviderIds: [{ providerId: '3674509', payer: 'MEDICAID' }],
          id: 10008328,
          firstName: 'JAMES',
          middleName: null,
          lastName: "O'CONNELL",
          fullName: '',
          facilityName: 'INSPIRA MEDICAL CENTERS, INC.',
          address: null,
          addressLine1: '1505 W SHERMAN AVE',
          addressLine2: null,
          city: 'VINELAND',
          state: 'NJ',
          contactName: null,
          email: null,
          extension: null,
          fax: '************',
          npi: '**********',
          phone: '************',
          pin: null,
          placeOfServiceCode: null,
          placeOfService: null,
          role: 'Service Provider',
          ssn: null,
          specialtyCode: '282N00000X',
          specialty: { code: '282N00000X', value: 'General Acute Care Hospital' },
          submitterId: null,
          suffix: null,
          taxId: '*********',
          type: null,
          url: null,
          zip: '*********',
          billingType: 'institutional',
          npiType: 'Organizational',
          obtainsAuth: false,
          clientId: null,
          notes: null,
          source: null
        }
      ],
      status: 'Unknown',
      submissionTime: 155000,
      referralAuthId: null,
      referralAuthUpdateDate: null,
      medianDecisionDays: null,
      priority: null,
      appointmentType: null,
      orderType: null,
      encounterId: null,
      isDeleted: false,
      statusCheck: false,
      latestCoverageExternalCheckDate: null,
      latestCoverageUpdatedDate: '2025-05-31T12:37:18+00:00',
      daysOutClosed: 0,
      daysOutInitiated: 0,
      decisionDays: 2,
      favorite: false,
      orderTypeId: '',
      orderTypeName: 'Sleep Management',
      requestType: 'normal',
      rescheduleCount: 0,
      rescheduled: false,
      isProcessing: false
    },
    webhookId: '4764918a-f5a2-ef11-88cf-000d3a14bf3d',
    enrollmentId: 'cd9d812c-a56d-4bdd-a817-51f951de937f',
    relatedEntityId: '2132895892359823598',
    relatedEntityType: 'Request'
  },
  {
    topic: 'request.updated',
    data: {
      activityLogs: [],
      apptId: 'A-124898',
      assignedOwner: 'persante',
      authChecked: false,
      authId: '',
      authRequired: 'notRequired',
      cancelled: false,
      caseId: '',
      coverages: [],
      coveragesErrors: [],
      coverageStatus: 'active_Coverage',
      createdDate: '2025-05-31T12:37:17.4186616+00:00',
      decisionDate: '2025-06-04T00:00:00+00:00',
      diagnoses: [
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.33',
          description: null,
          date: null
        },
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.30',
          description: null,
          date: null
        }
      ],
      effectiveDate: '2025-06-02T00:00:00+00:00',
      encounterType: 'outpatient',
      entryMethod: 'eviCore (CareCore)',
      expirationDate: '2025-06-11T00:00:00+00:00',
      id: 5695580,
      externalId: '02c90bfc-1b3e-f011-a5f1-7c1e5201dc62',
      initiatedDate: '2025-05-31T00:00:00+00:00',
      key: null,
      orderNotes: '',
      orderingProvider: {
        payerAssignedProviderIds: [],
        id: 5554634,
        firstName: 'LAVERNE',
        middleName: null,
        lastName: 'GRAVES-WASHINGTON',
        fullName: null,
        facilityName: null,
        address: null,
        addressLine1: '268 NORTH NEW RD.',
        addressLine2: null,
        city: 'PLEASANTVILLE',
        state: 'NJ',
        contactName: null,
        email: null,
        extension: null,
        fax: '************',
        npi: '**********',
        phone: '************',
        pin: null,
        placeOfServiceCode: null,
        placeOfService: null,
        role: 'Ordering Physician',
        ssn: null,
        specialtyCode: '363LF0000X',
        specialty: { code: '363LF0000X', value: 'Family' },
        submitterId: null,
        suffix: null,
        taxId: null,
        type: null,
        url: null,
        zip: '08232',
        billingType: 'institutional',
        npiType: 'Individual',
        obtainsAuth: false,
        clientId: null,
        notes: null,
        source: null
      },
      patient: {
        id: 111111,
        memberId: '**********',
        relationToSubscriber: 'Self',
        birth: '1967-08-22T00:00:00+00:00',
        firstName: 'Patrick',
        lastName: 'McDaniel',
        middleName: null,
        name: null,
        plan: null,
        gender: 'male',
        key: null,
        phone: null,
        medicalRecord: 'P-881249',
        apptId: 'A-124898',
        groupNumber: '893469843659',
        state: 'New Jersey',
        suffix: null,
        ssn: null,
        addressLine1: '119 E GREENFIELD AVE',
        addressLine2: '',
        city: 'PLEASANTVILLE',
        zipCode: '08232',
        payer: 'AETNA',
        payerId: 'AETNA'
      },
      payerNotes: 'Approved\n\nFacility Name: INSPIRA MEDICAL CENTER VINELAND - HOSP',
      placeOfService: { code: '22', value: 'On Campus-Outpatient Hospital' },
      additionalServiceTypes: null,
      primaryServiceType: null,
      procedures: [
        {
          id: 30134070,
          quantity: 1,
          procedureServiceQuantityType: { code: 'VS', value: 'Visits' },
          qualifier: { code: 'HC', value: 'CPT/HCPCS' },
          code: '95811',
          description: null,
          from: '2025-06-01T00:00:00+00:00',
          to: '2025-08-03T00:00:00+00:00',
          modifier: null,
          payerNote: 'For Utilization Management contact Evicore at ********** ',
          payerNoteUrl:
            'https://www.aetna.com/health-care-professionals/precertification/precertification-lists-results.html?ctpcode1=95811',
          authRequired: 'approved',
          authChecked: true,
          entryMethod: 'eviCore (CareCore)',
          entryMethodChecked: true
        }
      ],
      servicingProviders: [
        {
          payerAssignedProviderIds: [{ providerId: '3674509', payer: 'MEDICAID' }],
          id: 10008328,
          firstName: 'JAMES',
          middleName: null,
          lastName: "O'CONNELL",
          fullName: '',
          facilityName: 'INSPIRA MEDICAL CENTERS, INC.',
          address: null,
          addressLine1: '1505 W SHERMAN AVE',
          addressLine2: null,
          city: 'VINELAND',
          state: 'NJ',
          contactName: null,
          email: null,
          extension: null,
          fax: '************',
          npi: '**********',
          phone: '************',
          pin: null,
          placeOfServiceCode: null,
          placeOfService: null,
          role: 'Service Provider',
          ssn: null,
          specialtyCode: '282N00000X',
          specialty: { code: '282N00000X', value: 'General Acute Care Hospital' },
          submitterId: null,
          suffix: null,
          taxId: '*********',
          type: null,
          url: null,
          zip: '*********',
          billingType: 'institutional',
          npiType: 'Organizational',
          obtainsAuth: false,
          clientId: null,
          notes: null,
          source: null
        }
      ],
      status: 'Unknown',
      submissionTime: 155000,
      referralAuthId: null,
      referralAuthUpdateDate: null,
      medianDecisionDays: null,
      priority: null,
      appointmentType: null,
      orderType: null,
      encounterId: null,
      isDeleted: false,
      statusCheck: false,
      latestCoverageExternalCheckDate: null,
      latestCoverageUpdatedDate: '2025-05-31T12:37:18+00:00',
      daysOutClosed: 0,
      daysOutInitiated: 0,
      decisionDays: 2,
      favorite: false,
      orderTypeId: '',
      orderTypeName: 'Sleep Management',
      requestType: 'normal',
      rescheduleCount: 0,
      rescheduled: false,
      isProcessing: false
    },
    webhookId: '4764918a-f5a2-ef11-88cf-000d3a14bf3d',
    enrollmentId: 'cd9d812c-a56d-4bdd-a817-51f951de937f',
    relatedEntityId: '2132895892359823598',
    relatedEntityType: 'Request'
  },
  {
    topic: 'request.updated',
    data: {
      activityLogs: [],
      apptId: 'A-124898',
      assignedOwner: 'persante',
      authChecked: false,
      authId: '',
      authRequired: 'denied',
      cancelled: false,
      caseId: '',
      coverages: [],
      coveragesErrors: [],
      coverageStatus: 'active_Coverage',
      createdDate: '2025-05-31T12:37:17.4186616+00:00',
      decisionDate: '2025-06-04T00:00:00+00:00',
      diagnoses: [
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.33',
          description: null,
          date: null
        },
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.30',
          description: null,
          date: null
        }
      ],
      effectiveDate: '2025-06-02T00:00:00+00:00',
      encounterType: 'outpatient',
      entryMethod: 'eviCore (CareCore)',
      expirationDate: '2025-06-11T00:00:00+00:00',
      id: 5695580,
      externalId: '02c90bfc-1b3e-f011-a5f1-7c1e5201dc62',
      initiatedDate: '2025-05-31T00:00:00+00:00',
      key: null,
      orderNotes: '',
      orderingProvider: {
        payerAssignedProviderIds: [],
        id: 5554634,
        firstName: 'LAVERNE',
        middleName: null,
        lastName: 'GRAVES-WASHINGTON',
        fullName: null,
        facilityName: null,
        address: null,
        addressLine1: '268 NORTH NEW RD.',
        addressLine2: null,
        city: 'PLEASANTVILLE',
        state: 'NJ',
        contactName: null,
        email: null,
        extension: null,
        fax: '************',
        npi: '**********',
        phone: '************',
        pin: null,
        placeOfServiceCode: null,
        placeOfService: null,
        role: 'Ordering Physician',
        ssn: null,
        specialtyCode: '363LF0000X',
        specialty: { code: '363LF0000X', value: 'Family' },
        submitterId: null,
        suffix: null,
        taxId: null,
        type: null,
        url: null,
        zip: '08232',
        billingType: 'institutional',
        npiType: 'Individual',
        obtainsAuth: false,
        clientId: null,
        notes: null,
        source: null
      },
      patient: {
        id: 111111,
        memberId: '**********',
        relationToSubscriber: 'Self',
        birth: '1967-08-22T00:00:00+00:00',
        firstName: 'Patrick',
        lastName: 'McDaniel',
        middleName: null,
        name: null,
        plan: null,
        gender: 'male',
        key: null,
        phone: null,
        medicalRecord: 'P-881249',
        apptId: 'A-124898',
        groupNumber: '893469843659',
        state: 'New Jersey',
        suffix: null,
        ssn: null,
        addressLine1: '119 E GREENFIELD AVE',
        addressLine2: '',
        city: 'PLEASANTVILLE',
        zipCode: '08232',
        payer: 'AETNA',
        payerId: 'AETNA'
      },
      payerNotes: 'Approved\n\nFacility Name: INSPIRA MEDICAL CENTER VINELAND - HOSP',
      placeOfService: { code: '22', value: 'On Campus-Outpatient Hospital' },
      additionalServiceTypes: null,
      primaryServiceType: null,
      procedures: [
        {
          id: 30134070,
          quantity: 1,
          procedureServiceQuantityType: { code: 'VS', value: 'Visits' },
          qualifier: { code: 'HC', value: 'CPT/HCPCS' },
          code: '95811',
          description: null,
          from: '2025-06-01T00:00:00+00:00',
          to: '2025-08-03T00:00:00+00:00',
          modifier: null,
          payerNote: 'For Utilization Management contact Evicore at ********** ',
          payerNoteUrl:
            'https://www.aetna.com/health-care-professionals/precertification/precertification-lists-results.html?ctpcode1=95811',
          authRequired: 'approved',
          authChecked: true,
          entryMethod: 'eviCore (CareCore)',
          entryMethodChecked: true
        }
      ],
      servicingProviders: [
        {
          payerAssignedProviderIds: [{ providerId: '3674509', payer: 'MEDICAID' }],
          id: 10008328,
          firstName: 'JAMES',
          middleName: null,
          lastName: "O'CONNELL",
          fullName: '',
          facilityName: 'INSPIRA MEDICAL CENTERS, INC.',
          address: null,
          addressLine1: '1505 W SHERMAN AVE',
          addressLine2: null,
          city: 'VINELAND',
          state: 'NJ',
          contactName: null,
          email: null,
          extension: null,
          fax: '************',
          npi: '**********',
          phone: '************',
          pin: null,
          placeOfServiceCode: null,
          placeOfService: null,
          role: 'Service Provider',
          ssn: null,
          specialtyCode: '282N00000X',
          specialty: { code: '282N00000X', value: 'General Acute Care Hospital' },
          submitterId: null,
          suffix: null,
          taxId: '*********',
          type: null,
          url: null,
          zip: '*********',
          billingType: 'institutional',
          npiType: 'Organizational',
          obtainsAuth: false,
          clientId: null,
          notes: null,
          source: null
        }
      ],
      status: 'Denied',
      submissionTime: 155000,
      referralAuthId: null,
      referralAuthUpdateDate: null,
      medianDecisionDays: null,
      priority: null,
      appointmentType: null,
      orderType: null,
      encounterId: null,
      isDeleted: false,
      statusCheck: false,
      latestCoverageExternalCheckDate: null,
      latestCoverageUpdatedDate: '2025-05-31T12:37:18+00:00',
      daysOutClosed: 0,
      daysOutInitiated: 0,
      decisionDays: 2,
      favorite: false,
      orderTypeId: '',
      orderTypeName: 'Sleep Management',
      requestType: 'normal',
      rescheduleCount: 0,
      rescheduled: false,
      isProcessing: false
    },
    webhookId: '4764918a-f5a2-ef11-88cf-000d3a14bf3d',
    enrollmentId: 'cd9d812c-a56d-4bdd-a817-51f951de937f',
    relatedEntityId: '2132895892359823598',
    relatedEntityType: 'Request'
  },
  {
    topic: 'request.updated',
    data: {
      activityLogs: [],
      apptId: 'A-124898',
      assignedOwner: 'persante',
      authChecked: true,
      authId: 'A4fbd0da23f091896e7bd',
      authRequired: 'approved',
      cancelled: false,
      caseId: '577bee206271d0c5f20498',
      coverages: [],
      coveragesErrors: [],
      coverageStatus: 'active_Coverage',
      createdDate: '2025-05-31T12:37:17.4186616+00:00',
      decisionDate: '2025-06-04T00:00:00+00:00',
      diagnoses: [
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.33',
          description: null,
          date: null
        },
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.30',
          description: null,
          date: null
        }
      ],
      effectiveDate: '2025-06-02T00:00:00+00:00',
      encounterType: 'outpatient',
      entryMethod: 'eviCore (CareCore)',
      expirationDate: '2025-06-11T00:00:00+00:00',
      id: 5695580,
      externalId: '02c90bfc-1b3e-f011-a5f1-7c1e5201dc62',
      initiatedDate: '2025-05-31T00:00:00+00:00',
      key: null,
      orderNotes: '',
      orderingProvider: {
        payerAssignedProviderIds: [],
        id: 5554634,
        firstName: 'LAVERNE',
        middleName: null,
        lastName: 'GRAVES-WASHINGTON',
        fullName: null,
        facilityName: null,
        address: null,
        addressLine1: '268 NORTH NEW RD.',
        addressLine2: null,
        city: 'PLEASANTVILLE',
        state: 'NJ',
        contactName: null,
        email: null,
        extension: null,
        fax: '************',
        npi: '**********',
        phone: '************',
        pin: null,
        placeOfServiceCode: null,
        placeOfService: null,
        role: 'Ordering Physician',
        ssn: null,
        specialtyCode: '363LF0000X',
        specialty: { code: '363LF0000X', value: 'Family' },
        submitterId: null,
        suffix: null,
        taxId: null,
        type: null,
        url: null,
        zip: '08232',
        billingType: 'institutional',
        npiType: 'Individual',
        obtainsAuth: false,
        clientId: null,
        notes: null,
        source: null
      },
      patient: {
        id: 111111,
        memberId: '**********',
        relationToSubscriber: 'Self',
        birth: '1967-08-22T00:00:00+00:00',
        firstName: 'Patrick',
        lastName: 'McDaniel',
        middleName: null,
        name: null,
        plan: null,
        gender: 'male',
        key: null,
        phone: null,
        medicalRecord: 'P-881249',
        apptId: 'A-124898',
        groupNumber: '893469843659',
        state: 'New Jersey',
        suffix: null,
        ssn: null,
        addressLine1: '119 E GREENFIELD AVE',
        addressLine2: '',
        city: 'PLEASANTVILLE',
        zipCode: '08232',
        payer: 'AETNA',
        payerId: 'AETNA'
      },
      payerNotes: 'Approved\n\nFacility Name: INSPIRA MEDICAL CENTER VINELAND - HOSP',
      placeOfService: { code: '22', value: 'On Campus-Outpatient Hospital' },
      additionalServiceTypes: null,
      primaryServiceType: null,
      procedures: [
        {
          id: 30134070,
          quantity: 1,
          procedureServiceQuantityType: { code: 'VS', value: 'Visits' },
          qualifier: { code: 'HC', value: 'CPT/HCPCS' },
          code: '95811',
          description: null,
          from: '2025-06-01T00:00:00+00:00',
          to: '2025-08-03T00:00:00+00:00',
          modifier: null,
          payerNote: 'For Utilization Management contact Evicore at ********** ',
          payerNoteUrl:
            'https://www.aetna.com/health-care-professionals/precertification/precertification-lists-results.html?ctpcode1=95811',
          authRequired: 'approved',
          authChecked: true,
          entryMethod: 'eviCore (CareCore)',
          entryMethodChecked: true
        }
      ],
      servicingProviders: [
        {
          payerAssignedProviderIds: [{ providerId: '3674509', payer: 'MEDICAID' }],
          id: 10008328,
          firstName: 'JAMES',
          middleName: null,
          lastName: "O'CONNELL",
          fullName: '',
          facilityName: 'INSPIRA MEDICAL CENTERS, INC.',
          address: null,
          addressLine1: '1505 W SHERMAN AVE',
          addressLine2: null,
          city: 'VINELAND',
          state: 'NJ',
          contactName: null,
          email: null,
          extension: null,
          fax: '************',
          npi: '**********',
          phone: '************',
          pin: null,
          placeOfServiceCode: null,
          placeOfService: null,
          role: 'Service Provider',
          ssn: null,
          specialtyCode: '282N00000X',
          specialty: { code: '282N00000X', value: 'General Acute Care Hospital' },
          submitterId: null,
          suffix: null,
          taxId: '*********',
          type: null,
          url: null,
          zip: '*********',
          billingType: 'institutional',
          npiType: 'Organizational',
          obtainsAuth: false,
          clientId: null,
          notes: null,
          source: null
        }
      ],
      status: 'Approved',
      submissionTime: 155000,
      referralAuthId: null,
      referralAuthUpdateDate: null,
      medianDecisionDays: null,
      priority: null,
      appointmentType: null,
      orderType: null,
      encounterId: null,
      isDeleted: false,
      statusCheck: false,
      latestCoverageExternalCheckDate: null,
      latestCoverageUpdatedDate: '2025-05-31T12:37:18+00:00',
      daysOutClosed: 0,
      daysOutInitiated: 0,
      decisionDays: 2,
      favorite: false,
      orderTypeId: '',
      orderTypeName: 'Sleep Management',
      requestType: 'normal',
      rescheduleCount: 0,
      rescheduled: false,
      isProcessing: false
    },
    webhookId: '4764918a-f5a2-ef11-88cf-000d3a14bf3d',
    enrollmentId: 'cd9d812c-a56d-4bdd-a817-51f951de937f',
    relatedEntityId: '2132895892359823598',
    relatedEntityType: 'Request'
  },
  {
    topic: 'request.updated',
    data: {
      activityLogs: [],
      apptId: 'A-124898',
      assignedOwner: 'persante',
      authChecked: false,
      authId: '',
      authRequired: 'withdrawn',
      cancelled: false,
      caseId: '',
      coverages: [],
      coveragesErrors: [],
      coverageStatus: 'active_Coverage',
      createdDate: '2025-05-31T12:37:17.4186616+00:00',
      decisionDate: '2025-06-04T00:00:00+00:00',
      diagnoses: [
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.33',
          description: null,
          date: null
        },
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.30',
          description: null,
          date: null
        }
      ],
      effectiveDate: '2025-06-02T00:00:00+00:00',
      encounterType: 'outpatient',
      entryMethod: 'eviCore (CareCore)',
      expirationDate: '2025-06-11T00:00:00+00:00',
      id: 5695580,
      externalId: '02c90bfc-1b3e-f011-a5f1-7c1e5201dc62',
      initiatedDate: '2025-05-31T00:00:00+00:00',
      key: null,
      orderNotes: '',
      orderingProvider: {
        payerAssignedProviderIds: [],
        id: 5554634,
        firstName: 'LAVERNE',
        middleName: null,
        lastName: 'GRAVES-WASHINGTON',
        fullName: null,
        facilityName: null,
        address: null,
        addressLine1: '268 NORTH NEW RD.',
        addressLine2: null,
        city: 'PLEASANTVILLE',
        state: 'NJ',
        contactName: null,
        email: null,
        extension: null,
        fax: '************',
        npi: '**********',
        phone: '************',
        pin: null,
        placeOfServiceCode: null,
        placeOfService: null,
        role: 'Ordering Physician',
        ssn: null,
        specialtyCode: '363LF0000X',
        specialty: { code: '363LF0000X', value: 'Family' },
        submitterId: null,
        suffix: null,
        taxId: null,
        type: null,
        url: null,
        zip: '08232',
        billingType: 'institutional',
        npiType: 'Individual',
        obtainsAuth: false,
        clientId: null,
        notes: null,
        source: null
      },
      patient: {
        id: 111111,
        memberId: '**********',
        relationToSubscriber: 'Self',
        birth: '1967-08-22T00:00:00+00:00',
        firstName: 'Patrick',
        lastName: 'McDaniel',
        middleName: null,
        name: null,
        plan: null,
        gender: 'male',
        key: null,
        phone: null,
        medicalRecord: 'P-881249',
        apptId: 'A-124898',
        groupNumber: '893469843659',
        state: 'New Jersey',
        suffix: null,
        ssn: null,
        addressLine1: '119 E GREENFIELD AVE',
        addressLine2: '',
        city: 'PLEASANTVILLE',
        zipCode: '08232',
        payer: 'AETNA',
        payerId: 'AETNA'
      },
      payerNotes: 'Approved\n\nFacility Name: INSPIRA MEDICAL CENTER VINELAND - HOSP',
      placeOfService: { code: '22', value: 'On Campus-Outpatient Hospital' },
      additionalServiceTypes: null,
      primaryServiceType: null,
      procedures: [
        {
          id: 30134070,
          quantity: 1,
          procedureServiceQuantityType: { code: 'VS', value: 'Visits' },
          qualifier: { code: 'HC', value: 'CPT/HCPCS' },
          code: '95811',
          description: null,
          from: '2025-06-01T00:00:00+00:00',
          to: '2025-08-03T00:00:00+00:00',
          modifier: null,
          payerNote: 'For Utilization Management contact Evicore at ********** ',
          payerNoteUrl:
            'https://www.aetna.com/health-care-professionals/precertification/precertification-lists-results.html?ctpcode1=95811',
          authRequired: 'approved',
          authChecked: true,
          entryMethod: 'eviCore (CareCore)',
          entryMethodChecked: true
        }
      ],
      servicingProviders: [
        {
          payerAssignedProviderIds: [{ providerId: '3674509', payer: 'MEDICAID' }],
          id: 10008328,
          firstName: 'JAMES',
          middleName: null,
          lastName: "O'CONNELL",
          fullName: '',
          facilityName: 'INSPIRA MEDICAL CENTERS, INC.',
          address: null,
          addressLine1: '1505 W SHERMAN AVE',
          addressLine2: null,
          city: 'VINELAND',
          state: 'NJ',
          contactName: null,
          email: null,
          extension: null,
          fax: '************',
          npi: '**********',
          phone: '************',
          pin: null,
          placeOfServiceCode: null,
          placeOfService: null,
          role: 'Service Provider',
          ssn: null,
          specialtyCode: '282N00000X',
          specialty: { code: '282N00000X', value: 'General Acute Care Hospital' },
          submitterId: null,
          suffix: null,
          taxId: '*********',
          type: null,
          url: null,
          zip: '*********',
          billingType: 'institutional',
          npiType: 'Organizational',
          obtainsAuth: false,
          clientId: null,
          notes: null,
          source: null
        }
      ],
      status: 'Withdrawn',
      submissionTime: 155000,
      referralAuthId: null,
      referralAuthUpdateDate: null,
      medianDecisionDays: null,
      priority: null,
      appointmentType: null,
      orderType: null,
      encounterId: null,
      isDeleted: false,
      statusCheck: false,
      latestCoverageExternalCheckDate: null,
      latestCoverageUpdatedDate: '2025-05-31T12:37:18+00:00',
      daysOutClosed: 0,
      daysOutInitiated: 0,
      decisionDays: 2,
      favorite: false,
      orderTypeId: '',
      orderTypeName: 'Sleep Management',
      requestType: 'normal',
      rescheduleCount: 0,
      rescheduled: false,
      isProcessing: false
    },
    webhookId: '4764918a-f5a2-ef11-88cf-000d3a14bf3d',
    enrollmentId: 'cd9d812c-a56d-4bdd-a817-51f951de937f',
    relatedEntityId: '2132895892359823598',
    relatedEntityType: 'Request'
  },
  {
    topic: 'request.updated',
    data: {
      activityLogs: [],
      apptId: 'A-124898',
      assignedOwner: 'persante',
      authChecked: false,
      authId: '',
      authRequired: 'noData',
      cancelled: false,
      caseId: '',
      coverages: [],
      coveragesErrors: [],
      coverageStatus: 'active_Coverage',
      createdDate: '2025-05-31T12:37:17.4186616+00:00',
      decisionDate: '2025-06-04T00:00:00+00:00',
      diagnoses: [
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.33',
          description: null,
          date: null
        },
        {
          qualifier: { code: 'ABF', value: 'ICD-10-CM Diagnosis' },
          code: 'G47.30',
          description: null,
          date: null
        }
      ],
      effectiveDate: '2025-06-02T00:00:00+00:00',
      encounterType: 'outpatient',
      entryMethod: 'eviCore (CareCore)',
      expirationDate: '2025-06-11T00:00:00+00:00',
      id: 5695580,
      externalId: '02c90bfc-1b3e-f011-a5f1-7c1e5201dc62',
      initiatedDate: '2025-05-31T00:00:00+00:00',
      key: null,
      orderNotes: '',
      orderingProvider: {
        payerAssignedProviderIds: [],
        id: 5554634,
        firstName: 'LAVERNE',
        middleName: null,
        lastName: 'GRAVES-WASHINGTON',
        fullName: null,
        facilityName: null,
        address: null,
        addressLine1: '268 NORTH NEW RD.',
        addressLine2: null,
        city: 'PLEASANTVILLE',
        state: 'NJ',
        contactName: null,
        email: null,
        extension: null,
        fax: '************',
        npi: '**********',
        phone: '************',
        pin: null,
        placeOfServiceCode: null,
        placeOfService: null,
        role: 'Ordering Physician',
        ssn: null,
        specialtyCode: '363LF0000X',
        specialty: { code: '363LF0000X', value: 'Family' },
        submitterId: null,
        suffix: null,
        taxId: null,
        type: null,
        url: null,
        zip: '08232',
        billingType: 'institutional',
        npiType: 'Individual',
        obtainsAuth: false,
        clientId: null,
        notes: null,
        source: null
      },
      patient: {
        id: 111111,
        memberId: '**********',
        relationToSubscriber: 'Self',
        birth: '1967-08-22T00:00:00+00:00',
        firstName: 'Patrick',
        lastName: 'McDaniel',
        middleName: null,
        name: null,
        plan: null,
        gender: 'male',
        key: null,
        phone: null,
        medicalRecord: 'P-881249',
        apptId: 'A-124898',
        groupNumber: '893469843659',
        state: 'New Jersey',
        suffix: null,
        ssn: null,
        addressLine1: '119 E GREENFIELD AVE',
        addressLine2: '',
        city: 'PLEASANTVILLE',
        zipCode: '08232',
        payer: 'AETNA',
        payerId: 'AETNA'
      },
      payerNotes: 'Approved\n\nFacility Name: INSPIRA MEDICAL CENTER VINELAND - HOSP',
      placeOfService: { code: '22', value: 'On Campus-Outpatient Hospital' },
      additionalServiceTypes: null,
      primaryServiceType: null,
      procedures: [
        {
          id: 30134070,
          quantity: 1,
          procedureServiceQuantityType: { code: 'VS', value: 'Visits' },
          qualifier: { code: 'HC', value: 'CPT/HCPCS' },
          code: '95811',
          description: null,
          from: '2025-06-01T00:00:00+00:00',
          to: '2025-08-03T00:00:00+00:00',
          modifier: null,
          payerNote: 'For Utilization Management contact Evicore at ********** ',
          payerNoteUrl:
            'https://www.aetna.com/health-care-professionals/precertification/precertification-lists-results.html?ctpcode1=95811',
          authRequired: 'approved',
          authChecked: true,
          entryMethod: 'eviCore (CareCore)',
          entryMethodChecked: true
        }
      ],
      servicingProviders: [
        {
          payerAssignedProviderIds: [{ providerId: '3674509', payer: 'MEDICAID' }],
          id: 10008328,
          firstName: 'JAMES',
          middleName: null,
          lastName: "O'CONNELL",
          fullName: '',
          facilityName: 'INSPIRA MEDICAL CENTERS, INC.',
          address: null,
          addressLine1: '1505 W SHERMAN AVE',
          addressLine2: null,
          city: 'VINELAND',
          state: 'NJ',
          contactName: null,
          email: null,
          extension: null,
          fax: '************',
          npi: '**********',
          phone: '************',
          pin: null,
          placeOfServiceCode: null,
          placeOfService: null,
          role: 'Service Provider',
          ssn: null,
          specialtyCode: '282N00000X',
          specialty: { code: '282N00000X', value: 'General Acute Care Hospital' },
          submitterId: null,
          suffix: null,
          taxId: '*********',
          type: null,
          url: null,
          zip: '*********',
          billingType: 'institutional',
          npiType: 'Organizational',
          obtainsAuth: false,
          clientId: null,
          notes: null,
          source: null
        }
      ],
      status: 'In Review',
      submissionTime: 155000,
      referralAuthId: null,
      referralAuthUpdateDate: null,
      medianDecisionDays: null,
      priority: null,
      appointmentType: null,
      orderType: null,
      encounterId: null,
      isDeleted: false,
      statusCheck: false,
      latestCoverageExternalCheckDate: null,
      latestCoverageUpdatedDate: '2025-05-31T12:37:18+00:00',
      daysOutClosed: 0,
      daysOutInitiated: 0,
      decisionDays: 2,
      favorite: false,
      orderTypeId: '',
      orderTypeName: 'Sleep Management',
      requestType: 'normal',
      rescheduleCount: 0,
      rescheduled: false,
      isProcessing: false
    },
    webhookId: '4764918a-f5a2-ef11-88cf-000d3a14bf3d',
    enrollmentId: 'cd9d812c-a56d-4bdd-a817-51f951de937f',
    relatedEntityId: '2132895892359823598',
    relatedEntityType: 'Request'
  }
];

/**
 * Represents a code-value pair used throughout the system
 * for standardized coding systems
 */
export interface CodeValue {
  code: string;
  value: string;
}

/**
 * Represents a medical diagnosis with coding information
 */
export interface Diagnosis {
  qualifier: CodeValue;
  code: string;
  description: string | null;
  date: string | null;
}

/**
 * Represents a healthcare provider (individual or organization)
 * involved in the patient's care
 */
export interface Provider {
  payerAssignedProviderIds?: Array<{ providerId: string; payer: string }>;
  id: number;
  firstName: string;
  middleName: string | null;
  lastName: string;
  fullName?: string;
  facilityName?: string;
  address: string | null;
  addressLine1: string;
  addressLine2: string | null;
  city: string;
  state: string;
  contactName: string | null;
  email: string | null;
  extension: string | null;
  fax: string;
  npi: string;
  phone: string;
  pin: string | null;
  placeOfServiceCode: string | null;
  placeOfService: string | null;
  role: string;
  ssn: string | null;
  specialtyCode: string;
  specialty: CodeValue;
  submitterId: string | null;
  suffix: string | null;
  taxId: string | null;
  type: string | null;
  url: string | null;
  zip: string;
  billingType: 'institutional' | 'professional';
  npiType: 'Individual' | 'Organizational';
  obtainsAuth: boolean;
  clientId: string | null;
  notes: string | null;
  source: string | null;
}

/**
 * Represents a patient receiving healthcare services
 */
export interface Patient {
  id: number;
  memberId: string;
  relationToSubscriber: string;
  birth: string;
  firstName: string;
  lastName: string;
  middleName: string | null;
  name: string | null;
  plan: string | null;
  gender: 'male' | 'female' | string;
  key: string | null;
  phone: string | null;
  medicalRecord: string;
  apptId: string;
  groupNumber: string;
  state: string;
  suffix: string | null;
  ssn: string | null;
  addressLine1: string;
  addressLine2: string;
  city: string;
  zipCode: string;
  payer: string;
  payerId: string;
}

/**
 * Represents a medical procedure with authorization details
 */
export interface Procedure {
  id: number;
  quantity: number;
  procedureServiceQuantityType: CodeValue;
  qualifier: CodeValue;
  code: string;
  description: string | null;
  from: string;
  to: string;
  modifier: string | null;
  payerNote: string | null;
  payerNoteUrl: string | null;
  authRequired: string;
  authChecked: boolean;
  entryMethod: string;
  entryMethodChecked: boolean;
}

/**
 * Represents the possible webhook event topics that can be received
 * from the insurance system
 */
export type WebhookTopic = 'request.updated';

export interface RequestWebhook {
  topic: WebhookTopic;
  data: RequestData;
  webhookId: string;
  enrollmentId: string;
  relatedEntityId: string;
  relatedEntityType: string;
}

export type AuthRequiredStatus =
  | 'noData'
  | 'required'
  | 'notRequired'
  | 'denied'
  | 'approved'
  | 'withdrawn';
export type RequestStatus =
  | 'Unknown'
  | 'Inactive'
  | 'Denied'
  | 'Approved'
  | 'Withdrawn'
  | 'In Review';
export type CoverageStatus = 'active_Coverage' | 'inactive';

export interface RequestData {
  activityLogs: any[];
  apptId: string;
  assignedOwner: string | null;
  authChecked: boolean;
  authId: string | null;
  authRequired: AuthRequiredStatus;
  cancelled: boolean;
  caseId: string | null;
  coverages: any[];
  coveragesErrors: any[];
  coverageStatus: CoverageStatus;
  createdDate: string;
  decisionDate: string | null;
  diagnoses: Diagnosis[];
  effectiveDate: string | null;
  encounterType: string;
  entryMethod: string;
  expirationDate: string | null;
  id: number;
  externalId: string;
  initiatedDate: string | null;
  key: string | null;
  orderNotes: string | null;
  orderingProvider: Provider;
  patient: Patient;
  payerNotes: string | null;
  placeOfService: CodeValue;
  additionalServiceTypes: any | null;
  primaryServiceType: any | null;
  procedures: Procedure[];
  servicingProviders: Provider[];
  status: RequestStatus;
  submissionTime: number;
  referralAuthId: string | null;
  referralAuthUpdateDate: string | null;
  medianDecisionDays: number | null;
  priority: string | null;
  appointmentType: string | null;
  orderType: string | null;
  encounterId: string | null;
  isDeleted: boolean;
  statusCheck: boolean;
  latestCoverageExternalCheckDate: string | null;
  latestCoverageUpdatedDate: string | null;
  daysOutClosed: number | null;
  daysOutInitiated: number | null;
  decisionDays: number | null;
  favorite: boolean;
  orderTypeId: string | null;
  orderTypeName: string | null;
  requestType: string;
  rescheduleCount: number;
  rescheduled: boolean;
  isProcessing: boolean;
}
