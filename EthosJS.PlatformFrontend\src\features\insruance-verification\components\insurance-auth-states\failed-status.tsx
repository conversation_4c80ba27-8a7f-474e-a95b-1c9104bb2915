import { GetApiInsuranceVerificationStatusByJobIdResponse } from '@client/workflows';
import MainCardContainer from '@components/main-container/main-card-container';
import { Chip, Stack, Button } from '@mui/material';
import { AlertTriangle } from 'lucide-react';

export default function FailedStatus({
	verificationStatus: _verificationStatus,
	onEditInsurance,
	onRetryVerification,
}: {
	verificationStatus: GetApiInsuranceVerificationStatusByJobIdResponse;
	onEditInsurance: () => void;
	onRetryVerification: () => void;
}) {
	return (
		<MainCardContainer
			title="Insurance Verification"
			headerSize="medium"
			icon={<AlertTriangle />}
			color="warning"
			emphasis="low"
			descriptionSubheader="Member ID not found in carrier system. Please check the insurance information and try again."
			customAction={
				<Chip
					label="Failed"
					sx={{ borderRadius: 2, color: 'black' }}
				/>
			}>
			<Stack
				direction="row"
				spacing={2}
				sx={{ p: 2, justifyContent: 'flex-end' }}>
				<Button
					variant="outlined"
					onClick={onEditInsurance}>
					Edit Insurance
				</Button>
				<Button
					variant="contained"
					onClick={onRetryVerification}>
					Retry Verification
				</Button>
			</Stack>
		</MainCardContainer>
	);
}
