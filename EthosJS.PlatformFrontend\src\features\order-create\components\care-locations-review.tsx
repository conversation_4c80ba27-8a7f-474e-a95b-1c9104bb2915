import { PatientCreate, PatientRead } from '@auth/scopes';
import { EthosModelQueryDto1EthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null } from '@client/workflows';
import { postApiCareLocationSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import ChipSummary from '@components/chip-summary';
import MainCardContainer from '@components/main-container/main-card-container';
import { Stack } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { Query, CareLocationQuery } from '@utils/query-dsl';
import { map } from 'lodash';
import { Hospital, MapPinPlus } from 'lucide-react';
import { useRefDataValues } from '../../../hooks/use-ref-data-values';

interface CareLocationsReviewProps {
	careLocationId: string;
}

export default function CareLocationsReview({ careLocationId }: CareLocationsReviewProps) {
	const {
		data: careLocations,
		isFetching: isCareLocationFetching,
		error: fetchCareLocationError,
	} = useQuery({
		...postApiCareLocationSearchOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			body: Query.literal(
				CareLocationQuery.withId(careLocationId)
			) as unknown as EthosModelQueryDto1EthosModelCareLocationQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!careLocationId,
	});

	const careLocation = careLocations?.items?.[0];

	const { values: encounterTypes } = useRefDataValues({
		ids: careLocation?.supportedEncounterTypes ?? [],
	});

	const { values: studyTypes } = useRefDataValues({
		ids: careLocation?.supportedStudyTypes ?? [],
	});

	if (!careLocation) {
		return <MainCardContainer title="No Care Location Selected" />;
	}

	if (isCareLocationFetching) {
		return <MainCardContainer title="Loading Care Location..." />;
	}

	if (fetchCareLocationError) {
		return <MainCardContainer title="Error Loading Care Location..." />;
	}

	const { name } = careLocation;

	return (
		<MainCardContainer
			icon={<Hospital />}
			title="Location"
			color="primary"
			emphasis="low">
			<MainCardContainer
				icon={<MapPinPlus />}
				headerSize="small"
				title={name}
				color="gray"
				emphasis="low">
				<Stack>
					<ChipSummary
						variant="outlined"
						hideSeperator
						items={[
							...map(careLocation?.contactDetail?.phoneNumbers, (phoneNumber, index) => ({
								label: `Phone (${index + 1})`,
								value: phoneNumber.phoneNumber,
							})),
							...map(careLocation?.contactDetail?.emails, (email, index) => ({
								label: `Email (${index + 1})`,
								value: email.email,
							})),
						]}
					/>
					<ChipSummary
						variant="outlined"
						hideSeperator
						items={map(encounterTypes, (encounterType, index) => ({
							label: `Encounter Type (${index + 1})`,
							value: encounterType.title,
						}))}
					/>
					<ChipSummary
						variant="outlined"
						hideSeperator
						items={map(studyTypes, (studyType, index) => ({
							label: `Study Type (${index + 1})`,
							value: studyType.title,
						}))}
					/>
				</Stack>
			</MainCardContainer>
		</MainCardContainer>
	);
}
