import AddContactsStep from '@features/patient-create/components/steps/contacts.step';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/patient-information/contact-information'
)({
	component: RouteComponent,
});

function RouteComponent() {
	const { patientId } = Route.useParams();

	const navigate = Route.useNavigate();

	return (
		<AddContactsStep
			patientId={patientId}
			successCallback={() => {
				console.log('success');
				navigate({
					to: '/patients/$patientId/patient-information/addresses',
					params: { patientId },
				});
			}}
		/>
	);
}
