// This file is auto-generated by @hey-api/openapi-ts

export type EthosRoleDto = {
    name?: string | null;
    id?: string | null;
    scopes?: Array<string> | null;
    filters?: Array<string> | null;
};

export type EthosScopeDto = {
    name?: string | null;
    description?: string | null;
    privileged?: boolean;
    assignable?: boolean;
};

export type GetApiRolesData = {
    body?: never;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/roles';
};

export type GetApiRolesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiRolesData = {
    body?: EthosRoleDto;
    path?: never;
    query?: never;
    url: '/api/roles';
};

export type PostApiRolesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiRolesBuiltinData = {
    body?: never;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/roles/builtin';
};

export type GetApiRolesBuiltinResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type DeleteApiRolesByRoleIdData = {
    body?: never;
    path: {
        roleId: string;
    };
    query?: never;
    url: '/api/roles/{roleId}';
};

export type DeleteApiRolesByRoleIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiRolesByRoleIdData = {
    body?: never;
    path: {
        roleId: string;
    };
    query?: never;
    url: '/api/roles/{roleId}';
};

export type GetApiRolesByRoleIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PutApiRolesByRoleIdData = {
    body?: EthosRoleDto;
    path: {
        roleId: string;
    };
    query?: never;
    url: '/api/roles/{roleId}';
};

export type PutApiRolesByRoleIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type DeleteApiRolesByRoleIdAssignByUserIdData = {
    body?: never;
    path: {
        roleId: string;
        userId: string;
    };
    query?: never;
    url: '/api/roles/{roleId}/assign/{userId}';
};

export type DeleteApiRolesByRoleIdAssignByUserIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiRolesByRoleIdAssignByUserIdData = {
    body?: never;
    path: {
        roleId: string;
        userId: string;
    };
    query?: never;
    url: '/api/roles/{roleId}/assign/{userId}';
};

export type PostApiRolesByRoleIdAssignByUserIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiRolesAssignmentsByUserIdData = {
    body?: never;
    path: {
        userId: string;
    };
    query?: never;
    url: '/api/roles/assignments/{userId}';
};

export type GetApiRolesAssignmentsByUserIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type DeleteApiRolesScopesData = {
    body?: Array<string>;
    path?: never;
    query?: never;
    url: '/api/roles/scopes';
};

export type DeleteApiRolesScopesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiRolesScopesData = {
    body?: never;
    path?: never;
    query?: {
        offset?: number;
        limit?: number;
    };
    url: '/api/roles/scopes';
};

export type GetApiRolesScopesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiRolesScopesData = {
    body?: Array<EthosScopeDto>;
    path?: never;
    query?: never;
    url: '/api/roles/scopes';
};

export type PostApiRolesScopesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PutApiRolesScopesByScopeData = {
    body?: EthosScopeDto;
    path: {
        scope: string;
    };
    query?: never;
    url: '/api/roles/scopes/{scope}';
};

export type PutApiRolesScopesByScopeResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type DeleteApiRolesByRoleIdScopesData = {
    body?: Array<string>;
    path: {
        roleId: string;
    };
    query?: {
        removeAll?: boolean;
    };
    url: '/api/roles/{roleId}/scopes';
};

export type DeleteApiRolesByRoleIdScopesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiRolesByRoleIdScopesData = {
    body?: Array<string>;
    path: {
        roleId: string;
    };
    query?: never;
    url: '/api/roles/{roleId}/scopes';
};

export type PostApiRolesByRoleIdScopesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type ClientOptions = {
    baseURL: 'http://home.goodspace.org:4005' | (string & {});
};