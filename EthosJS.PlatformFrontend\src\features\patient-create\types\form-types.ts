import {
	EthosWorkflowsApiCreatePatientInputDto,
	EthosWorkflowsApiPersonalAddressDto,
} from '@client/workflows';

type PatientBasicInformationData = Pick<
	EthosWorkflowsApiCreatePatientInputDto,
	'patientInformation' | 'demographics' | 'physicalMeasurements'
>;

type ContactsData = Pick<EthosWorkflowsApiCreatePatientInputDto, 'contactInformation'>;

type AddressesData = {
	addresses: EthosWorkflowsApiPersonalAddressDto[];
};

type InsurancesData = Pick<EthosWorkflowsApiCreatePatientInputDto, 'insurances'>;

type GuardiansData = Pick<EthosWorkflowsApiCreatePatientInputDto, 'guardians'>;

type ClinicalConsiderationsData = Pick<
	EthosWorkflowsApiCreatePatientInputDto,
	| 'clinicalConsiderations'
	| 'schedulingPreferences'
	| 'additionalPatientNotes'
	| 'caregiverInformation'
>;

interface SchedulingPreferencesData {
	specialAccomodations: number[];
	mobilityAssistance: number[];
	schedulingPreferences: {
		technicianPreference: number | null;
		preferredDayOfWeek: number[];
		additionalPatientNotes: string;
	};
}

export type {
	PatientBasicInformationData,
	ContactsData,
	AddressesData,
	InsurancesData,
	GuardiansData,
	ClinicalConsiderationsData,
	SchedulingPreferencesData,
};
