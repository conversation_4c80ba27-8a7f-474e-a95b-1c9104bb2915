import CardHeader from "@components/card-header";
import { Box, CardContent, Stack, Typography } from "@mui/material";

import styles from "@components/technician-tabs/styles";
import Summary from "@components/technician-tabs/summary";
import Card from "@components/card";

type Demographics = {
   dob: string
   sex: string
   genderIdentity: string
   maritalStatus: string
   race: string
   ethnicity: string
}

type PhysicalMeasurements = {
   hieght: string
   weight: string
   neck: string
   BMI: string
}

type SummaryDetails = {
   SSN: string
   patientId: string
}

interface PatientInfoProp {
   title?: string
   values: {
      summaryDetails: Partial<SummaryDetails>,
      demographics: Partial<Demographics>
      physicalMeasurements: Partial<PhysicalMeasurements>
   }
}

const PatientInfo = (props: PatientInfoProp) => {

   const { title, values: { demographics, physicalMeasurements, summaryDetails } } = props;

   return (
      <Card>
         <CardHeader
            {...{
               title: <Typography>{title}</Typography>,
               emphasis: 'dark'
            }}
         />
         <Box sx={styles.cardBody}>
            <Stack gap={2}>
               <Summary
                  data={[
                     {
                        label: 'Social Security Number',
                        value: summaryDetails?.SSN as string,
                     },
                     {
                        label: 'Patient ID',
                        value: summaryDetails?.patientId as string
                     },
                  ]}
               />

               <Stack>
                  <Card sx={{ borderBottomLeftRadius: 0, borderBottomRightRadius: 0 }}>
                     <CardHeader
                        {...{
                           title: <Typography>{'Demographics'}</Typography>,
                        }}
                     />
                     <CardContent>
                        <Summary
                           data={[
                              {
                                 label: 'Date of Birth',
                                 value: demographics?.dob as string,
                              },
                              {
                                 label: 'Birth Sex',
                                 value: demographics?.dob as string
                              },
                              {
                                 label: 'Gender Identity',
                                 value: demographics?.genderIdentity as string
                              },
                              {
                                 label: 'Marital Status',
                                 value: demographics?.maritalStatus as string
                              },
                              {
                                 label: 'Race',
                                 value: demographics?.race as string
                              },
                              {
                                 label: 'Ethnicity',
                                 value: demographics?.ethnicity as string
                              },
                           ]}
                        />
                     </CardContent>
                  </Card>
                  <Card sx={{ borderTopLeftRadius: 0, borderTopRightRadius: 0 }}>
                     <CardHeader
                        {...{
                           title: <Typography>{'Physical Measurements'}</Typography>,
                        }}
                     />
                     <CardContent>
                        <Summary
                           data={[
                              {
                                 label: 'Height (in)',
                                 value: physicalMeasurements?.hieght as string,
                              },
                              {
                                 label: 'Weight (lbs)',
                                 value: physicalMeasurements?.weight as string
                              },
                              {
                                 label: 'Neck (in)',
                                 value: physicalMeasurements?.neck as string,
                              },
                              {
                                 label: 'BMI',
                                 value: physicalMeasurements?.BMI as string
                              },
                           ]}
                        />
                     </CardContent>
                  </Card>
               </Stack>
            </Stack>
         </Box>
      </Card>
   )
};

export default PatientInfo;