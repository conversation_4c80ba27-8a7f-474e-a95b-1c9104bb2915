import { MainCardContainerProps } from '@components/main-container/main-card-container';

export interface UploadedFile {
  id: string;
  title: string;
  fileName: string;
  uploadedBy: string;
  uploadedTime: string;
  // Extended properties for file upload container
  category?: string;
  documentType?: string;
  issueDate?: string;
  expiryDate?: string;
  fileSize?: string;
  fileId?: string;
  status?: 'uploading' | 'completed' | 'failed';
}

export interface FileFormData {
  category: string;
  documentType: string;
  issueDate: string;
  expiryDate: string;
  fileId?: string;
  fileName?: string;
}

export interface FileUploadContainerProps {
  title: string;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  onAddFile?: (fileData: FileFormData) => void;
  onEditFile?: (fileId: string, fileData: FileFormData) => void;
  onDeleteFile?: (fileId: string) => void;
  workflowId: string;
  footerProps?: MainCardContainerProps['footerProps'];
  customAction?: React.ReactNode;
  emphasis?: MainCardContainerProps['emphasis'];
  color?: MainCardContainerProps['color'];
  descriptionSubheader?: string;
  descriptionText?: string;
}

export interface FileItemProps {
  file: UploadedFile;
  allowEdit?: boolean;
  onEdit?: (fileData: FileFormData) => void;
  onDelete?: () => void;
  size?: 'medium' | 'small';
  footerProps?: MainCardContainerProps['footerProps'];
  icon?: React.ReactNode;
  isEditing?: boolean;
  onStartEdit?: () => void;
  onCancelEdit?: () => void;
  onSave?: (fileData: FileFormData) => void;
  workflowId?: string;
}

export interface FileUploadFormProps {
  title?: string;
  icon?: React.ReactNode;
  onSave: (fileData: FileFormData) => void;
  onCancel: () => void;
  onDelete?: () => void;
  workflowId: string;
  initialData?: Partial<FileFormData>;
  showUploadedFile?: boolean;
}

export interface FileListProps {
  files: UploadedFile[];
  onEdit?: (fileId: string) => void;
  onDelete?: (fileId: string) => void;
  allowEdit?: boolean;
  allowDelete?: boolean;
}
