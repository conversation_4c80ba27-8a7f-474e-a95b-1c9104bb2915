import InsurancesStep from '@features/patient-create/components/steps/insurances.step';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/patient-information/insurances'
)({
	component: RouteComponent,
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const navigate = Route.useNavigate();

	const successCallback = () => {
		navigate({
			to: '/patients/$patientId/patient-information/guardians',
			params: { patientId },
		});
	};

	return (
		<InsurancesStep
			patientId={patientId}
			successCallback={successCallback}
		/>
	);
}
