import { DriveFolderUploadOutlined } from "@mui/icons-material";
import { Typography } from "@mui/material";
import { useStore } from "@tanstack/react-store";
import { Flow } from "@workflows/Flow";

export default function Timestamp({ flow }: { flow: Flow }) {

    const { lastUpdate } = useStore(flow.getStore(), ({ lastUpdate }) => ({
        lastUpdate
    }));

    return (<Typography
        variant="body1"
        component="div"
        sx={{
            display: 'flex',
            borderTop: '1px solid',
            borderColor: 'divider',
            py: 1,
            px: 2,
            alignItems: 'center',
            justifyContent: 'center',
            lineHeight: 1,
        }}
    >
        <DriveFolderUploadOutlined fontSize="small" sx={{ mr: 1 }} /> Saved on{' '}
        {lastUpdate}
    </Typography>)
}