import AddInfoCard from '@components/add-info-card';
import { withForm } from '@hooks/app-form';
import { Mail } from '@mui/icons-material';

import EmailForm, { emailFormDefaultValues } from '../app-email-form/app-email.form';
import { formatEmailSummary } from '../create-patient/utils';
import ArrayFieldContainer from '@components/array-field-container';

interface FormValues {
	emails: any[];
}

const EmailAdress = withForm<FormValues>({
	defaultValues: {
		emails: [],
	},
	render: function Render({ form }) {
		return (
			<>
				<form.AppField
					name="emails"
					mode="array">
					{({ pushValue, removeValue, state, replaceValue }) => (
						<AddInfoCard
							title="Emails"
							icon={<Mail />}
							onClick={() => pushValue(emailFormDefaultValues)}
							showHeader={state?.value.length === 0}>
							{state.value.map((_, i) => (
								<ArrayFieldContainer
									initialEditState={true}
									items={formatEmailSummary(state.value[i])}
									title="Email">
									{({ setEdit }) => (
										<EmailForm
											key={i}
											formValues={state.value[i]}
											onAdd={(data) => {
												replaceValue(i, data);
												setEdit(false);
											}}
											onCancel={() => {
												setEdit(false);
											}}
											onDelete={() => {
												removeValue(i);
												setEdit(false);
											}}
										/>
									)}
								</ArrayFieldContainer>
							))}
						</AddInfoCard>
					)}
				</form.AppField>
			</>
		);
	},
});

export default EmailAdress;
