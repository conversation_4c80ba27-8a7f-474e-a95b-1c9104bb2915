// import useOrderCreate from '@features/order-create/hooks/use-order-create';
// import useStudyCreate from '@features/order-create/hooks/use-study-create';
import ClinicalConsiderationsStep from '@features/patient-create/components/steps/clinical-considerations.step';
import { createFileRoute } from '@tanstack/react-router';
// import dayjs from 'dayjs';

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/patient-information/clinical-considerations'
)({
	component: RouteComponent,
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const navigate = Route.useNavigate();

	// const { createOrder } = useOrderCreate();
	// const { createStudy } = useStudyCreate();

	// const createStudyAndNavigate = (orderId: string) => {
	// 	createStudy(
	// 		{
	// 			orderId,
	// 		},
	// 		(studyId) => {
	// 			navigate({
	// 				to: '/patients/$patientId/order/study',
	// 				params: { patientId },
	// 				search: { orderId, studyId },
	// 			});
	// 		}
	// 	);
	// };

	const createOrderAndStudy = () => {
		navigate({
			to: '/patients/$patientId/patient-information/schedule-preferences',
			params: { patientId },
		});
		// createOrder(
		// 	{
		// 		patientId,
		// 	},
		// 	{
		// 		flowState: {
		// 			progress: 0,
		// 			status: 'InProgress',
		// 			lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
		// 		},
		// 		stepState: {
		// 			AddStudy: 'InProgress',
		// 			AddCareLocation: 'NotStarted',
		// 			AddPhysicians: 'NotStarted',
		// 			ReviewAndSubmitOrder: 'NotStarted',
		// 		},
		// 	},
		// 	createStudyAndNavigate
		// );
	};

	return (
		<ClinicalConsiderationsStep
			patientId={patientId}
			successCallback={createOrderAndStudy}
		/>
	);
}
