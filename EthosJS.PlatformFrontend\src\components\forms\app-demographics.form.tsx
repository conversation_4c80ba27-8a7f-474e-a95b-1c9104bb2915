import MainCardContainer from '@components/main-container/main-card-container';
import { withForm } from '@hooks/app-form';
import { Grid2 as Grid } from '@mui/material';
import { formHasErrors } from '@utils/forms';
import { Users } from 'lucide-react';

const DemographicsForm = withForm({
	render: ({ form }) => {
		const hasErrors = formHasErrors(form, 'demographics');
		return (
			<MainCardContainer
				title="Demographics"
				icon={<Users />}
				color={hasErrors ? 'error' : 'primary'}
				emphasis={hasErrors ? 'high' : 'low'}>
				<Grid
					container
					spacing={2}>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="demographics.dateOfBirth"
							children={(field) => (
								<field.AppDateField
									label="Date of Birth *"
									dataTestId="demographics.dateOfBirth"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="demographics.birthSex"
							children={(field) => (
								<field.AppAutocompleteField
									label="Birth Sex"
									required
									referenceDataSetName="sex"
									filterOptions={(x) => x}
									dataTestId="demographics.birthSex"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="demographics.gender"
							children={(field) => (
								<field.AppAutocompleteField
									label="Gender Identity"
									required
									referenceDataSetName="gender"
									dataTestId="demographics.gender"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="demographics.race"
							children={(field) => (
								<field.AppAutocompleteField
									label="Race"
									required
									referenceDataSetName="race"
									dataTestId="demographics.race"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="demographics.maritalStatus"
							children={(field) => (
								<field.AppAutocompleteField
									label="Marital Status"
									required
									referenceDataSetName="maritalStatus"
									dataTestId="demographics.maritalStatus"
								/>
							)}
						/>
					</Grid>
					<Grid size={{ xs: 12, sm: 4 }}>
						<form.AppField
							name="demographics.ethnicity"
							children={(field) => (
								<field.AppAutocompleteField
									label="Ethnicity"
									required
									referenceDataSetName="ethnicity"
									dataTestId="demographics.ethnicity"
								/>
							)}
						/>
					</Grid>
				</Grid>
			</MainCardContainer>
		);
	},
});

export default DemographicsForm;
