import { styled, Box, IconButton, Collapse, Typography } from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { useEffect, useState, useRef } from 'react';

const Container = styled(Box, { shouldForwardProp: (prop) => prop !== 'severity' })<{
	severity: 'error' | 'warning' | 'info' | 'success';
}>(({ theme }) => ({
	padding: theme.spacing(2),
	display: 'flex',
	gap: theme.spacing(1),
	position: 'relative',
	borderRadius: theme.spacing(1),
	variants: [
		{
			props: { severity: 'error' },
			style: {
				backgroundColor: theme.palette.error.light,
				color: theme.palette.error.contrastText,
				border: `1px solid ${theme.palette.error.main}`,
				...theme.applyStyles('dark', {
					color: theme.palette.error.contrastText,
					backgroundColor: theme.palette.error.dark,
				}),
			},
		},
		{
			props: { severity: 'warning' },
			style: {
				backgroundColor: theme.palette.warning.light,
				color: theme.palette.warning.dark,
				border: `1px solid ${theme.palette.warning.main}`,
				...theme.applyStyles('dark', {
					color: theme.palette.warning.contrastText,
					backgroundColor: theme.palette.warning.dark,
				}),
			},
		},
		{
			props: { severity: 'info' },
			style: {
				backgroundColor: theme.palette.info.light,
				color: theme.palette.info.dark,
				border: `1px solid ${theme.palette.info.main}`,
				...theme.applyStyles('dark', {
					color: theme.palette.info.contrastText,
					backgroundColor: theme.palette.info.dark,
				}),
			},
		},
		{
			props: { severity: 'success' },
			style: {
				backgroundColor: theme.palette.success.light,
				color: theme.palette.success.dark,
				border: `1px solid ${theme.palette.success.main}`,
				...theme.applyStyles('dark', {
					color: theme.palette.success.contrastText,
					backgroundColor: theme.palette.success.dark,
				}),
			},
		},
	],
}));

const ContentWrapper = styled(Box)({
	flex: 1,
	display: 'flex',
	alignItems: 'center',
});

interface NotificationBannerProps {
	message: string | undefined;
	severity: 'error' | 'warning' | 'info' | 'success';
	autoClose?: boolean;
	autoCloseDuration?: number;
	onClose?: () => void;
	scrollIntoView?: boolean;
}

export default function NotificationBanner({
	message,
	severity = 'info',
	autoClose = true,
	autoCloseDuration = 5000,
	onClose,
	scrollIntoView = false,
}: NotificationBannerProps) {
	const [open, setOpen] = useState(!!message);
	const bannerRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		setOpen(!!message);

		if (message) {
			if (scrollIntoView && bannerRef.current) {
				bannerRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
			}

			if (autoClose) {
				setTimeout(() => {
					setOpen(false);
				}, autoCloseDuration);
			}
		}
	}, [message, autoClose, autoCloseDuration, scrollIntoView]);

	const handleClose = () => {
		setOpen(false);
		if (onClose) {
			onClose();
		}
	};

	return (
		<Collapse in={open && !!message}>
			<Container
				ref={bannerRef}
				severity={severity}>
				<ContentWrapper>
					<Typography color="inherit">{message}</Typography>
				</ContentWrapper>
				{onClose && (
					<IconButton
						size="small"
						onClick={handleClose}
						aria-label="close"
						sx={{ alignSelf: 'flex-start' }}>
						<CloseIcon fontSize="small" />
					</IconButton>
				)}
			</Container>
		</Collapse>
	);
}
