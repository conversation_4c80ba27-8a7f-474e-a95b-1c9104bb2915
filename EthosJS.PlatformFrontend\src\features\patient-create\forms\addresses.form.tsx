import StepCardControl from '@components/step-card-control';
import { Box, CardContent, Button, Stack } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { formOptions, useStore } from '@tanstack/react-form';
import { MapPin, Truck, Receipt } from 'lucide-react';
import { filter, orderBy } from 'lodash';
import { FormProps } from '@components/forms/predefined-form-props';
import { AddressesData } from '../types';
import { useMemo } from 'react';
import AddressForm, { addressFormOptions } from '@components/forms/app-address-form';
import { AddressUse } from '@components/forms/app-address-form/app-address.form';
import { addressesWithUseTypeTransformer } from './transformers';
import LoadingComponent from '@components/loading-component';
import { getErrorsForIndexField } from '@utils/forms';
import ArrayFormContainer from '@components/forms/app-array-form-container';
import { formatAddressSummary } from '@components/forms/app-address-form';
import { useAddressUses } from '@hooks/use-address-uses';

const defaultValues: AddressesData = {
	addresses: [],
};

function addressesOptions(savedData?: AddressesData) {
	return formOptions({
		defaultValues: {
			...defaultValues,
			...savedData,
		},
	});
}

export default function AddressesForm({
	onSubmit,
	onSaveDraft,
	savedData,
	onValidate,
	isUpdate,
}: FormProps<AddressesData>) {
	const options = useMemo(() => addressesOptions(savedData), [savedData]);
	const form = useAppForm({
		...options,
		onSubmit: async ({ value }) => {
			const vals = addressesWithUseTypeTransformer(value.addresses);
			onSubmit({
				addresses: vals,
			} as AddressesData);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	const { addressUses, isFetching } = useAddressUses();

	if (isFetching) {
		return (
			<Box sx={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
				<LoadingComponent />
			</Box>
		);
	}

	return (
		<Box
			component="form"
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
				<form.AppField
					name="addresses"
					mode="array">
					{({ pushValue, removeValue, state, replaceValue }) => {
						const addressUsesOrdered = orderBy(
							addressUses,
							[
								(item) => {
									if (item.key?.value === 'Physical') return 1;
									if (item.key?.value === 'Billing') return 2;
									if (item.key?.value === 'Shipping') return 3;
									return 4; // Any other types come last
								},
							],
							['asc']
						);

						return (
							<Stack gap={2}>
								{addressUsesOrdered.map((item) => {
									return (
										<ArrayFormContainer
											key={item.id}
											pushValue={pushValue}
											dataTestId={`addresses.${item.key?.value}`}
											removeValue={() =>
												removeValue(
													state.value?.findIndex((address) => address.use === Number(item.id))
												)
											}
											replaceValue={(_index, value) => {
												replaceValue(
													state.value?.findIndex((address) => address.use === Number(item.id)),
													value
												);
											}}
											state={{
												value:
													state.value?.filter((address) => address.use === Number(item.id)) ?? [],
											}}
											onValidate={async (data, index) => {
												const addresses =
													state.value?.map((_, j) => {
														if (j === index) {
															return data;
														}
														return state.value?.[j];
													}) ?? [];
												const vals = {
													addresses,
												} as AddressesData;
												const res = await onValidate(vals);
												return getErrorsForIndexField(
													`contactInformation.addresses[${index}]`,
													res
												);
											}}
											isUpdate={isUpdate}
											renderForm={function (props) {
												return (
													<AddressForm
														addressUse={item.key?.value as AddressUse}
														addressUseRefValue={Number(item.id)}
														physicalAddresses={filter(state.value, {
															use: addressUsesOrdered[0].id,
														})}
														{...props}
													/>
												);
											}}
											defaultValues={addressFormOptions(undefined, item.id).defaultValues}
											formatSummary={formatAddressSummary}
											mainCardContainerProps={{
												title: `${item.key?.value} Addresses`,
												icon:
													item.key?.value === 'Physical' ? (
														<MapPin />
													) : item.key?.value === 'Delivery' ? (
														<Truck />
													) : (
														<Receipt />
													),
												color: 'primary',
												emphasis: state.value?.filter((address) => address.use === Number(item.id))
													?.length
													? 'high'
													: 'low',
												primaryActionType: 'Add',
											}}
										/>
									);
								})}
							</Stack>
						);
					}}
				</form.AppField>
			</CardContent>
			<StepCardControl>
				{!isUpdate && (
					<Button
						variant="outlined"
						color="primary"
						onClick={() => onSaveDraft(values)}>
						Save Draft
					</Button>
				)}
				<form.Subscribe
					selector={({ isDirty, canSubmit, isSubmitting }) => ({
						isDirty,
						canSubmit,
						isSubmitting,
					})}>
					{({ isDirty, canSubmit, isSubmitting }) => (
						<Button
							variant="contained"
							color="primary"
							type="submit"
							loading={isSubmitting}
							disabled={!isDirty || !canSubmit}>
							{isUpdate ? 'Update' : 'Next'}
						</Button>
					)}
				</form.Subscribe>
			</StepCardControl>
		</Box>
	);
}
