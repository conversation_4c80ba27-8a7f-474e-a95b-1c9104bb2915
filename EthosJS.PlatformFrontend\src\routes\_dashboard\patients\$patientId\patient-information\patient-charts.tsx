import List from '@components/list';
import MainCardContainer from '@components/main-container/main-card-container';
import StepCardControl from '@components/step-card-control';
import useOrderCreate from '@features/order-create/hooks/use-order-create';
import useStudyCreate from '@features/order-create/hooks/use-study-create';
import { DocumentScanner } from '@mui/icons-material';
import { Button, Grid, IconButton, MenuItem, Stack, TextField } from '@mui/material';
import { createFileRoute, useRouter } from '@tanstack/react-router';
import dayjs from 'dayjs';
import { Download, Edit, Eye, HeartPulseIcon } from 'lucide-react';

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/patient-information/patient-charts'
)({
	component: RouteComponent,
});

function RouteComponent() {
	return <PetientCharts />;
}

const Filters = () => {
	const categories = [
		{
			label: 'All Categories',
			value: '1',
		},
	];

	const documentTypes = [
		{
			label: 'All Type',
			value: '1',
		},
	];

	const profiles = [
		{
			label: 'Ashlet W',
			value: '1',
		},
	];

	const uploaded = [
		{
			label: 'Last 30 Days',
			value: '1',
		},
		{
			label: 'Last 60 Days',
			value: '2',
		},
	];

	return (
		<Grid
			container
			spacing={2}>
			<Grid
				item
				xs={12}
				md={3}>
				<TextField
					fullWidth
					select
					label="Categories">
					{categories?.map((category) => {
						return (
							<MenuItem
								value={category?.value}
								key={category?.value}>
								{category?.label}
							</MenuItem>
						);
					})}
				</TextField>
			</Grid>
			<Grid
				item
				xs={12}
				md={9}>
				<TextField
					fullWidth
					label="Search"
				/>
			</Grid>
			<Grid
				item
				xs={12}
				md={3}>
				<TextField
					fullWidth
					select
					label="Document Type">
					{documentTypes?.map((documentType) => {
						return (
							<MenuItem
								value={documentType?.value}
								key={documentType?.value}>
								{documentType?.label}
							</MenuItem>
						);
					})}
				</TextField>
			</Grid>
			<Grid
				item
				xs={12}
				md={4.5}>
				<TextField
					fullWidth
					select
					label="Uploaded By">
					{profiles?.map((profile) => {
						return (
							<MenuItem
								value={profile?.value}
								key={profile?.value}>
								{profile?.label}
							</MenuItem>
						);
					})}
				</TextField>
			</Grid>
			<Grid
				item
				xs={12}
				md={4.5}>
				<TextField
					fullWidth
					select
					label="Uploaded">
					{uploaded?.map((item) => {
						return (
							<MenuItem
								value={item?.value}
								key={item?.value}>
								{item?.label}
							</MenuItem>
						);
					})}
				</TextField>
			</Grid>
		</Grid>
	);
};

const items = [
	{
		title: 'HIPAA Privacy Notice/Acknowledgment',
		subTitle: 'HIPAA_Privacy_Notice_Walter_Bishop_01-15-2025.pdf',
		value: '1',
		icon: <DocumentScanner />,
	},
	{
		title: 'HIPAA Privacy Notice/Acknowledgment',
		subTitle: 'HIPAA_Privacy_Notice_Walter_Bishop_01-15-2025.pdf',
		value: '2',
		icon: <DocumentScanner />,
	},
	{
		title: 'HIPAA Privacy Notice/Acknowledgment',
		subTitle: 'HIPAA_Privacy_Notice_Walter_Bishop_01-15-2025.pdf',
		value: '3',
		icon: <DocumentScanner />,
	},
	{
		title: 'HIPAA Privacy Notice/Acknowledgment',
		subTitle: 'HIPAA_Privacy_Notice_Walter_Bishop_01-15-2025.pdf',
		value: '4',
		icon: <DocumentScanner />,
	},
	{
		title: 'HIPAA Privacy Notice/Acknowledgment',
		subTitle: 'HIPAA_Privacy_Notice_Walter_Bishop_01-15-2025.pdf',
		value: '5',
		icon: <DocumentScanner />,
	},
	{
		title: 'HIPAA Privacy Notice/Acknowledgment',
		subTitle: 'HIPAA_Privacy_Notice_Walter_Bishop_01-15-2025.pdf',
		value: '6',
		icon: <DocumentScanner />,
	},
	{
		title: 'HIPAA Privacy Notice/Acknowledgment',
		subTitle: 'HIPAA_Privacy_Notice_Walter_Bishop_01-15-2025.pdf',
		value: '7',
		icon: <DocumentScanner />,
	},
	{
		title: 'HIPAA Privacy Notice/Acknowledgment',
		subTitle: 'HIPAA_Privacy_Notice_Walter_Bishop_01-15-2025.pdf',
		value: '8',
		icon: <DocumentScanner />,
	},
];

const PetientCharts = () => {
	const { patientId } = Route.useParams();
	const navigate = Route.useNavigate();
	const router = useRouter();

	const { createOrder } = useOrderCreate();
	const { createStudy } = useStudyCreate();

	const onGoBack = () => {
		router.history.back();
	};

	const createStudyAndNavigate = (orderId: string) => {
		createStudy(
			{
				orderId,
			},
			(studyId) => {
				navigate({
					to: '/patients/$patientId/order/study',
					params: { patientId },
					search: { orderId, studyId },
				});
			}
		);
	};

	const onCreatePatient = () => {
		createOrder(
			{
				patientId,
			},
			{
				flowState: {
					progress: 0,
					status: 'InProgress',
					lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
				},
				stepState: {
					AddStudy: 'InProgress',
					AddCareLocation: 'NotStarted',
					AddPhysicians: 'NotStarted',
					ReviewAndSubmitOrder: 'NotStarted',
				},
			},
			createStudyAndNavigate
		);
	};
	const onCreatePatientSaveDraft = () => {};
	return (
		<MainCardContainer
			icon={<HeartPulseIcon />}
			emphasis="medium"
			title="Patient Chart Documents"
			primaryActionType="Add">
			<Stack gap={2}>
				<Filters />
				<List
					items={items?.map((i) => {
						return {
							...i,
							extra: (
								<Stack
									direction={'row'}
									gap={2}>
									<IconButton>
										<Download />
									</IconButton>
									<IconButton>
										<Eye />
									</IconButton>
									<IconButton>
										<Edit />
									</IconButton>
								</Stack>
							),
						};
					})}
					selectable={false}
				/>
			</Stack>
			<StepCardControl>
				<Button
					color="primary"
					sx={{ mr: 'auto' }}
					onClick={() => onGoBack()}>
					Back
				</Button>
				{/* <Button
					variant="outlined"
					color="primary"
					onClick={onCreatePatientSaveDraft}>
					Create Patient & Save Draft
				</Button> */}
				<Button
					variant="contained"
					color="primary"
					onClick={onCreatePatient}>
					Create Patient & Exit
				</Button>
			</StepCardControl>
		</MainCardContainer>
	);
};
