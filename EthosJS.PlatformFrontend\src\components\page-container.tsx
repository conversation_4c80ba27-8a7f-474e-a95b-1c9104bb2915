import { SvgIconComponent } from "@mui/icons-material";
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  styled,
} from "@mui/material";
import FeaturedIcon from "./featured-icon";
import { PropsWithChildren, ReactNode } from "react";

const AppBarContainer = styled(AppBar)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  borderBottom: `1.5px solid ${theme.palette.grey[200]}`,
  padding: theme.spacing(1, 2),
}));

interface PageContainerProps extends PropsWithChildren {
  title: string;
  icon: SvgIconComponent;
  actions?: ReactNode;
}

export default function PageContainer(props: PageContainerProps) {
  const { title, icon, actions, children } = props;
  return (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        padding: "24px",
        bgcolor: "#F2F4F7",
      }}
    >
      <AppBarContainer position="static" elevation={0} sx={{ borderRadius: "16px 16px 0 0 ", padding:"16px" }}>
        <Toolbar disableGutters>
          <FeaturedIcon icon={icon} />
          <Typography
            variant="h6"
            component="div"
            color="primary"
            fontWeight={"medium"}
            sx={{ flexGrow: 1, ml: 0.75, fontSize: "1.5rem" }}
          >
            {title}
          </Typography>
          {actions}
        </Toolbar>
      </AppBarContainer>
      <Box sx={{ flexGrow: 1, overflow: "auto", minHeight: 0 }}>{children}</Box>
    </Box>
  );
}
