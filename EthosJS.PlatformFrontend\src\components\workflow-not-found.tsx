import { Box, Button, Paper, Typography, useTheme } from '@mui/material';
import { ErrorOutline, ArrowBackIos } from '@mui/icons-material';
import { Link } from '@tanstack/react-router';

interface WorkflowNotFoundProps {
    message?: string;
    navigateBackPath?: string;
    navigateBackText?: string;
}

export default function WorkflowNotFound({
    message = "The requested workflow could not be found.",
    navigateBackPath = "/patients",
    navigateBackText = "Back to Patients"
}: WorkflowNotFoundProps) {
    const theme = useTheme();

    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                width: '100%',
                p: 3
            }}
        >
            <Paper
                elevation={3}
                sx={{
                    p: 5,
                    borderRadius: 2,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    maxWidth: 500,
                    width: '100%',
                    border: `1px solid ${theme.palette.divider}`
                }}
            >
                <ErrorOutline
                    color="error"
                    sx={{ fontSize: 72, mb: 2, opacity: 0.8 }}
                />

                <Typography variant="h4" component="h1" sx={{ mb: 1, fontWeight: 500 }}>
                    Workflow Not Found
                </Typography>

                <Typography
                    variant="body1"
                    color="text.secondary"
                    align="center"
                    sx={{ mb: 4 }}
                >
                    {message}
                </Typography>

                <Button
                    component={Link}
                    to={navigateBackPath}
                    variant="contained"
                    color="primary"
                    startIcon={<ArrowBackIos fontSize='small' />}
                >
                    {navigateBackText}
                </Button>
            </Paper>
        </Box>
    );
}
