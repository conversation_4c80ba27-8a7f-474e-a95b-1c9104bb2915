import dayjs from 'dayjs';
import { SchedulingState, SchedulingStepNames, StateTypes } from '../types/state-types';

const STEP_ORDER: SchedulingStepNames[] = [
    'AppointmentSelection',
    'InstructionsAndDocuments', 
    'ReviewAndConfirm',
    'FollowUpCall',
    'AppointmentConfirmation'
];

function getNextStep(currentStep: SchedulingStepNames): SchedulingStepNames | undefined {
    const currentIndex = STEP_ORDER.indexOf(currentStep);
    return STEP_ORDER[currentIndex + 1];
}

function getPreviousStep(currentStep: SchedulingStepNames): SchedulingStepNames | undefined {
    const currentIndex = STEP_ORDER.indexOf(currentStep);
    return STEP_ORDER[currentIndex - 1];
}

function calculateProgress(stepState: Record<SchedulingStepNames, StateTypes>): number {
    const completedSteps = Object.values(stepState).filter(state => state === 'Complete').length;
    return Math.round((completedSteps / STEP_ORDER.length) * 100);
}

function getNewSchedulingState(
    currentState: SchedulingState,
    currentStep: SchedulingStepNames,
    stepStatus: StateTypes = 'Complete'
): SchedulingState {
    const nextStep = getNextStep(currentStep);
    const newStepState = {
        ...currentState.stepState,
        [currentStep]: stepStatus
    };

    if (nextStep && stepStatus === 'Complete') {
        newStepState[nextStep] = 'InProgress';
    }

    const progress = calculateProgress(newStepState);
    const isComplete = progress === 100;

    return {
        ...currentState,
        flowState: {
            ...currentState.flowState,
            status: isComplete ? 'Complete' : stepStatus === 'Error' ? 'Error' : 'InProgress',
            progress,
            lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
            currentStep: nextStep || currentStep
        },
        stepState: newStepState
    };
}

export { 
    getNewSchedulingState, 
    getNextStep, 
    getPreviousStep, 
    calculateProgress,
    STEP_ORDER 
};