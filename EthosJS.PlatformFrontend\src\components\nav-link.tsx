import { forwardRef, ReactNode } from 'react';
import {
  ListItemButton,
  ListItemIcon,
  ListItemText,
  ListItemButtonProps,
  Tooltip,
  styled,
  lighten,
  Theme,
  darken
} from '@mui/material';
import { createLink, LinkComponent } from '@tanstack/react-router';
const textId = `nav-item-title`;
const iconSlotId = `nav-item--leading-icon`;

const StyledListItemButton = styled(ListItemButton, { skipSx: true, slot: 'a' })(({ theme }) => [
  {
    position: 'relative',
    height: 44,
    minWidth: 44,
    borderRadius: theme.spacing(0.75),
    padding: theme.spacing(0.5),
    backgroundColor: theme.palette.background.paper,
    color: theme.palette.text.primary,
    fontWeight: 600,
    marginBottom: theme.spacing(1),
    overflow: 'hidden',

    background: theme.palette.grey[50],
    border: '1px solid',
    borderColor: theme.palette.grey[100],
    '&:hover': {
      backgroundColor: lighten(theme.palette.primary.light, 0.8),
      borderColor: lighten(theme.palette.primary.main, 0.7),
      color: theme.palette.primary.main,
      [`& .${iconSlotId}`]: {
        backgroundColor: lighten(theme.palette.primary.main, 0.1),
        borderColor: lighten(theme.palette.primary.main, 0.7),
        color: theme.palette.common.white
      }
    },
    '&:active': {
      backgroundColor: lighten(theme.palette.primary.main, 0.1),
      borderColor: lighten(theme.palette.primary.main, 0.1),
      [`& .${iconSlotId}`]: {
        borderColor: theme.palette.common.white,
        backgroundColor: theme.palette.common.white,
        color: lighten(theme.palette.primary.main, 0.1)
      },
      [`& .${textId}`]: {
        color: theme.palette.common.white
      }
    },
    '&.active': {
      color: theme.palette.primary.contrastText,
      '&:hover': {
        [`& .${iconSlotId}`]: {
          color: theme.palette.common.white
        }
      },
      [`& .${textId}`]: {
        color: theme.palette.primary.main
      },
      [`& .${iconSlotId}`]: {
        color: theme.palette.primary.main
      }
    }
  },
  theme.applyStyles('dark', {
    '&:hover': {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
      border: `1px solid ${darken(theme.palette.primary.dark, 0.5)}`
    },
    '&.active': {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
      border: `1px solid ${darken(theme.palette.primary.dark, 0.5)}`,
      '&:hover': {
        backgroundColor: theme.palette.primary.main,
        color: theme.palette.primary.contrastText,
        border: `1px solid ${darken(theme.palette.primary.dark, 0.5)}`
      }
    }
  })
]);

const StyledListItemIcon = styled(ListItemIcon)(({ theme }) => ({
  color: 'inherit',
  width: theme.spacing(4.25),
  minWidth: theme.spacing(4),
  padding: theme.spacing(0.5),
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  marginLeft: theme.spacing(0.2),
  '& svg': {
    fontSize: 24
  },
  borderRadius: 2
}));

const StyledListItemText = styled(ListItemText)(({ theme }) => ({
  marginLeft: theme.spacing(1)
}));

export interface MUILinkProps extends Omit<ListItemButtonProps, 'href'> {
  text: string;
  open: boolean;
  icon: ReactNode;
  badge?: ReactNode;
}

const MUILinkComponent = forwardRef<HTMLAnchorElement, MUILinkProps>(
  ({ text, icon, badge, open, ...props }, ref) => {
    const seleceted = props.className === 'active';

    return (
      <Tooltip title={text} placement="right">
        {/* @ts-ignore */}
        <StyledListItemButton component="a" ref={ref} {...props} selected={seleceted}>
          <StyledListItemIcon className={iconSlotId}>{icon}</StyledListItemIcon>
          <StyledListItemText primary={text} className={textId} />
          {badge && <>{badge}</>}
        </StyledListItemButton>
      </Tooltip>
    );
  }
);

const NavLinkComponent = createLink(MUILinkComponent);

export const NavLink: LinkComponent<typeof MUILinkComponent> = (props) => {
  return <NavLinkComponent preload={'intent'} {...props} />;
};
