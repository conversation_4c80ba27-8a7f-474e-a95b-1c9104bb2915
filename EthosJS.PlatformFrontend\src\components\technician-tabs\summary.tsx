import { Chip, Stack, SxProps, Theme, Typography, useTheme } from "@mui/material";



interface SummaryProps {
   data: Array<{ label?: string, value?: string, skipSeperator?: boolean, highlighted?: boolean }>
   highlighted?: Array<string>
}

const Summary = (props: SummaryProps) => {
   const { data = [], highlighted = [] } = props;

   return (
      <Stack sx={{ flexDirection: 'row', gap: 1 }}>
         {data.map(({ label, value, skipSeperator = false, highlighted: highlightedArg = false }, index) => {

            const isHighlighted = highlightedArg ?? highlighted?.includes(value as string);

            return (
               <Chip
                  label={(
                     <Stack sx={chipStyle.labelRoot}>
                        {label ? (
                           <Typography
                              sx={({ palette }) => {
                                 return {
                                    ...{ fontWeight: 700, color: palette?.common?.black },
                                    ...(isHighlighted ? { color: palette.common.white } as any : {})
                                 }
                              }}
                           >
                              {`${label}`}{!skipSeperator ? ':' : ""}
                           </Typography>
                        ) : null}
                        {value ? (
                           <Typography>
                              {`${value}`}
                           </Typography>
                        ) : null}

                     </Stack>
                  )}
                  key={index}
                  sx={({ palette }) => {
                     return (
                        {
                           ...chipStyle.root,
                           ...(isHighlighted ? { background: palette.primary.dark } as any : {})
                        }
                     )
                  }}
               />
            )
         })}



      </Stack>
   )
}

const chipStyle: Record<'labelRoot' | 'root', SxProps<Theme>> = {
   root: {
      borderRadius: 5,
   },
   labelRoot: { flexDirection: 'row', gap: 1 },
}

export default Summary