import * as React from 'react';
import TabsPrimitive from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';

interface TabPanelProps {
   children?: React.ReactNode;
   index: number;
   label?: string
   value?: number
}

function CustomTabPanel(props: TabPanelProps) {
   const { children, value, index, ...other } = props;

   return (
      <div
         role="tabpanel"
         hidden={value !== index}
         id={`simple-tabpanel-${index}`}
         aria-labelledby={`simple-tab-${index}`}
         {...other}
      >
         {value === index && <Box sx={{ mt: 1 }} >{children}</Box>}
      </div>
   );
}

function a11yProps(index: number) {
   return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
   };
}

export interface TabsProps {
   items: Array<TabPanelProps>
   onTabChange?: (selectedIndex: number) => void
   value?: number
}

export default function Tabs({ items = [], value: valueProp, onTabChange }: TabsProps) {
   const [valueLocal, setValueLocal] = React.useState(0);

   const value = valueProp ?? valueLocal;

   const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
      setValueLocal(newValue);
      typeof onTabChange === 'function' && onTabChange(newValue);
   };

   return (
      <Box sx={{ width: '100%' }}>
         <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <TabsPrimitive
               value={value}
               onChange={handleChange}
               aria-label="basic tabs example"
            >
               {items.map((item, index) => {
                  return <Tab label={item?.label} key={index} {...a11yProps(index)} />
               })}
            </TabsPrimitive>
         </Box>
         {items?.map((item, index) => {
            return (
               <CustomTabPanel
                  value={value}
                  index={index}
                  key={index}
               >
                  {item?.children && (
                     React.cloneElement(item?.children as React.ReactElement)
                  )}
               </CustomTabPanel>
            )
         })}
      </Box>
   );
}
