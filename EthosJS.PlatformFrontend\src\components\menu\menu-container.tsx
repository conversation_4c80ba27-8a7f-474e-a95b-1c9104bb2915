import { styled, Stack, alpha } from '@mui/material';

const MenuContainer = styled(Stack, {
	shouldForwardProp: (prop) => prop !== 'color',
})<{ color?: 'primary' }>(({ theme, color }) => {
	const { palette } = theme;

	return {
		padding: theme.spacing(1.5),
		paddingBottom: 0,
		borderRadius: theme.spacing(2.75),
		borderWidth: 1,
		borderStyle: 'solid',
		minWidth: '300px',
		maxWidth: '350px',
		height: '100%',
		...(color === 'primary' && {
			background: palette.common.white,
			borderColor: alpha(palette.primary.light, 0.4),
			...theme.applyStyles('dark', {
				background: palette.background.paper,
				borderColor: alpha(palette.primary.main, 0.6),
			}),
		}),
		borderBottom: 'none',
		borderBottomLeftRadius: 0,
		borderBottomRightRadius: 0,
	};
});

export default MenuContainer;
