import { Card, Box, Typography, styled } from "@mui/material"
import { SvgIconComponent } from "@mui/icons-material"


type StatsCardType = "success" | "info" | "warning" | "error" | "primary"
type IconType = StatsCardType

const CardWithBorder = styled(Card)(({ theme }) => ({
    border: `1px solid ${theme.palette.grey[200]}`,
    borderRadius: "16px",
    padding: theme.spacing(2),
    maxWidth: 350,
    minWidth: 220,
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottomLeftRadius: 0,
}))

interface StatsCardProps {
    title: string
    mainContent: string
    subContent: string
    icon: SvgIconComponent,
    type: StatsCardType,
    iconType?: IconType
}

export default function StatsCard(props: StatsCardProps) {
    const { title, mainContent, subContent, icon: Icon, type = "success", iconType = 'primary' } = props

    return (
        <CardWithBorder
            elevation={1}
        >
            <Box>
                <Typography
                    variant="h6"
                    sx={{
                        color: "text.secondary",
                        fontSize: "0.75rem",
                        fontWeight: 600,
                        mb: 1,
                    }}
                >
                    {title}
                </Typography>
                <Typography
                    variant="h5"
                    sx={{
                        fontSize: "1.25rem",
                        fontWeight: 700,
                        mb: 1,
                        color: "text.primary",
                    }}
                >
                    {mainContent}
                </Typography>
                <Typography
                    sx={{
                        color: `${type}.main`,
                        fontWeight: 700,
                        fontSize: "0.875rem",
                    }}
                >
                    {subContent}
                </Typography>
            </Box>
            <Box
                sx={{
                    position: "relative",
                    borderRadius: "50%",
                    width: 62,
                    height: 62,
                    p: "14px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    overflow: "hidden",
                }}
            >
                <Box
                    sx={{
                        backgroundColor: `${iconType}.light`,
                        opacity: 0.1,
                        position: "absolute",
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                    }}
                >

                </Box>
                <Icon color={`${iconType}`} />
            </Box>
        </CardWithBorder>
    )
}
