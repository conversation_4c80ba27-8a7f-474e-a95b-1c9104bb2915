import { IPrimitiveQuery } from './core';

// CareLocation primitive query types
export interface CareLocationQ extends IPrimitiveQuery {
	$type: 'WithId' | 'WithName' | 'WithParentId' | 'WithFacilityId' | 'WithApproximateName';
}

// Helper functions for CareLocationQ
export const CareLocationQuery = {
	withId: (id: string): CareLocationQ => ({
		$type: 'WithId',
		id: id,
	}),

	withName: (name: string): CareLocationQ => ({
		$type: 'WithName',
		Name: name,
	}),

	withParentId: (parentId: string): CareLocationQ => ({
		$type: 'WithParentId',
		ParentId: parentId,
	}),

	withFacilityId: (facilityId: string): CareLocationQ => ({
		$type: 'WithFacilityId',
		FacilityId: facilityId,
	}),

	withApproximateName: (approxName: string): CareLocationQ => ({
		$type: 'WithApproximateName',
		ApproxName: approxName,
	}),
};
