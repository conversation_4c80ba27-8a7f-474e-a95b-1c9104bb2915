import { acquireToken } from '@auth/auth-utils';
import { msalInstance } from '@auth/msal';
import { client } from '@client/refdata/client.gen';

const baseURL = import.meta.env.DEV
  ? 'http://localhost:4004'
  : import.meta.env.VITE_REFDATA_API_URL;

export default function applyClientConfig() {
  client.instance.interceptors.request.use(
    async (config) => {
      // Extract scopes from config or use empty array as default
      const scopes = config.scopes;
      // Pass scopes to acquireToken
      const accessToken = await acquireToken(msalInstance, scopes);
      if (accessToken) {
        config.headers.Authorization = `Bearer ${accessToken}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
  client.setConfig({
    baseURL: baseURL
  });
}
