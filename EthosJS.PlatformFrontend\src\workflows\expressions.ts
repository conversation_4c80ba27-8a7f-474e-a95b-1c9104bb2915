import { ITypeAnnotation } from './TypeDef';

export interface IExpr<T> {
  type: any;
  extra: T;
  toJSON(): any;
  toString(): string;
  mapExtra<U>(func: (value: T) => U): IExpr<U>;
}

export enum ExprType {
  BinOp = 'BinOp',
  IntLit = 'IntLit',
  FloatingLit = 'FloatingLit',
  StringLit = 'StringLit',
  Var = 'Var',
  Call = 'Call',
  Property = 'Property',
  Comparison = 'Comparison',
  Logical = 'Logical',
  Function = 'Function'
}

export interface IBinOpExpr<T> extends IExpr<T> {
  type: ExprType.BinOp;
  left: IExpr<T>;
  op: string; // ">", ">=", "==", "!=", "&&", "||", "+", "-", "*", "/"
  right: IExpr<T>;
  extra: T;
}

export interface IIntLitExpr<T> extends IExpr<T> {
  type: ExprType.IntLit;
  value: number;
  extra: T;
}

export interface IFloatingLitExpr<T> extends IExpr<T> {
  type: ExprType.FloatingLit;
  value: number;
  extra: T;
}

export interface IStringLitExpr<T> extends IExpr<T> {
  type: ExprType.StringLit;
  value: string;
  extra: T;
}

export interface IVarExpr<T> extends IExpr<T> {
  type: ExprType.Var;
  name: string;
  extra: T;
}

export interface ICallExpr<T> extends IExpr<T> {
  type: ExprType.Call;
  func: string; // function name, e.g. "len", "year", or special "__index__"
  args: IExpr<T>[];
  extra: T;
}

export interface IPropertyExpr<T> extends IExpr<T> {
  type: ExprType.Property;
  path: string[];
  extra: T;
}

export interface IComparisonExpr<T> extends IExpr<T> {
  type: ExprType.Comparison;
  left: IExpr<T>;
  right: IExpr<T>;
  operator: string; // '==', '!=', '>', '>=', '<', '<=', 'in', 'not in'
  extra: T;
}

export interface ILogicalExpr<T> extends IExpr<T> {
  type: ExprType.Logical;
  operator: 'and' | 'or' | 'not';
  operands: IExpr<T>[];
  extra: T;
}

export interface IFunctionExpr<T> extends IExpr<T> {
  type: ExprType.Function;
  name: string;
  args: IExpr<T>[];
  extra: T;
}

// Expression implementations
export class BinOp<T> implements IBinOpExpr<T> {
  readonly type = ExprType.BinOp;

  constructor(
    public left: IExpr<T>,
    public op: string,
    public right: IExpr<T>,
    public extra: T
  ) {}

  toString(): string {
    return `(${this.left.toString()} ${this.op} ${this.right.toString()})`;
  }

  toJSON(): any {
    return {
      type: 'BinOp',
      left: this.left.toJSON(),
      op: this.op,
      right: this.right.toJSON(),
      extra: (this.extra && (this.extra as any).toJSON?.()) || null
    };
  }

  mapExtra<U>(func: (value: T) => U): BinOp<U> {
    return new BinOp(
      this.left.mapExtra(func),
      this.op,
      this.right.mapExtra(func),
      func(this.extra)
    );
  }
}

export class IntLit<T> implements IIntLitExpr<T> {
  readonly type = ExprType.IntLit;

  constructor(
    public value: number,
    public extra: T
  ) {}

  toString(): string {
    return this.value.toString();
  }

  toJSON(): any {
    return {
      type: 'IntLit',
      value: this.value,
      extra: (this.extra && (this.extra as any).toJSON?.()) || null
    };
  }

  mapExtra<U>(func: (value: T) => U): IntLit<U> {
    return new IntLit(this.value, func(this.extra));
  }
}

export class FloatingLit<T> implements IFloatingLitExpr<T> {
  readonly type = ExprType.FloatingLit;

  constructor(
    public value: number,
    public extra: T
  ) {}

  toString(): string {
    return this.value.toString();
  }

  toJSON(): any {
    return {
      type: 'FloatingLit',
      value: this.value,
      extra: (this.extra && (this.extra as any).toJSON?.()) || null
    };
  }

  mapExtra<U>(func: (value: T) => U): FloatingLit<U> {
    return new FloatingLit(this.value, func(this.extra));
  }
}

export class StringLit<T> implements IStringLitExpr<T> {
  readonly type = ExprType.StringLit;

  constructor(
    public value: string,
    public extra: T
  ) {}

  toString(): string {
    return JSON.stringify(this.value);
  }

  toJSON(): any {
    return {
      type: 'StringLit',
      value: this.value,
      extra: (this.extra && (this.extra as any).toJSON?.()) || null
    };
  }

  mapExtra<U>(func: (value: T) => U): StringLit<U> {
    return new StringLit(this.value, func(this.extra));
  }
}

export class Var<T> implements IVarExpr<T> {
  readonly type = ExprType.Var;

  constructor(
    public name: string,
    public extra: T
  ) {}

  toString(): string {
    return this.name;
  }

  toJSON(): any {
    return {
      type: 'Var',
      name: this.name,
      extra: (this.extra && (this.extra as any).toJSON?.()) || null
    };
  }

  mapExtra<U>(func: (value: T) => U): Var<U> {
    return new Var(this.name, func(this.extra));
  }
}

export class Call<T> implements ICallExpr<T> {
  readonly type = ExprType.Call;

  constructor(
    public func: string,
    public args: IExpr<T>[],
    public extra: T
  ) {}

  toString(): string {
    return `${this.func}(${this.args.map((arg) => arg.toString()).join(', ')})`;
  }

  toJSON(): any {
    return {
      type: 'Call',
      func: this.func,
      args: this.args.map((arg) => arg.toJSON()),
      extra: (this.extra && (this.extra as any).toJSON?.()) || null
    };
  }

  mapExtra<U>(func: (value: T) => U): Call<U> {
    return new Call(
      this.func,
      this.args.map((arg) => arg.mapExtra(func)),
      func(this.extra)
    );
  }
}

// Expression builders
export const expr = {
  binOp: <T>(left: IExpr<T>, op: string, right: IExpr<T>, extra: T): BinOp<T> =>
    new BinOp(left, op, right, extra),

  int: <T>(value: number, extra: T): IntLit<T> => new IntLit(value, extra),

  float: <T>(value: number, extra: T): FloatingLit<T> => new FloatingLit(value, extra),

  string: <T>(value: string, extra: T): StringLit<T> => new StringLit(value, extra),

  var: <T>(name: string, extra: T): Var<T> => new Var(name, extra),

  call: <T>(func: string, args: IExpr<T>[], extra: T): Call<T> => new Call(func, args, extra)
};

// Expression evaluator
export function evaluateExpr<T>(expression: IExpr<T>, context: any): any {
  switch (expression.type) {
    case ExprType.IntLit:
      return (expression as IIntLitExpr<T>).value;
    case ExprType.FloatingLit:
      return (expression as IFloatingLitExpr<T>).value;
    case ExprType.StringLit:
      return (expression as IStringLitExpr<T>).value;
    case ExprType.Property:
      return (expression as IPropertyExpr<T>).path.reduce(
        (obj: any, key: string) => obj?.[key],
        context
      );

    case ExprType.Comparison: {
      const comp = expression as IComparisonExpr<T>;
      const left = evaluateExpr(comp.left, context);
      const right = evaluateExpr(comp.right, context);

      switch (comp.operator) {
        case '==':
          return left === right;
        case '!=':
          return left !== right;
        case '>':
          return left > right;
        case '>=':
          return left >= right;
        case '<':
          return left < right;
        case '<=':
          return left <= right;
        case 'in':
          return Array.isArray(right) && right.includes(left);
        case 'not in':
          return Array.isArray(right) && !right.includes(left);
        default:
          throw new Error(`Unknown operator: ${comp.operator}`);
      }
    }

    case ExprType.Logical: {
      const logic = expression as ILogicalExpr<T>;
      switch (logic.operator) {
        case 'and':
          return logic.operands.every((op) => evaluateExpr(op, context));
        case 'or':
          return logic.operands.some((op) => evaluateExpr(op, context));
        case 'not':
          return !evaluateExpr(logic.operands[0], context);
        default:
          throw new Error(`Unknown logical operator: ${logic.operator}`);
      }
    }

    case ExprType.Function: {
      const func = expression as IFunctionExpr<T>;
      const args = func.args.map((arg) => evaluateExpr(arg, context));
      return evaluateFunction(func.name, args, context);
    }

    case ExprType.BinOp: {
      const binOp = expression as IBinOpExpr<T>;
      const left = evaluateExpr(binOp.left, context);
      const right = evaluateExpr(binOp.right, context);

      switch (binOp.op) {
        // Arithmetic operators
        case '+':
          return left + right;
        case '-':
          return left - right;
        case '*':
          return left * right;
        case '/':
          return left / right;
        // Comparison operators
        case '==':
          return left === right;
        case '!=':
          return left !== right;
        case '>':
          return left > right;
        case '>=':
          return left >= right;
        case '<':
          return left < right;
        case '<=':
          return left <= right;
        // Logical operators
        case '&&':
          return left && right;
        case '||':
          return left || right;
        default:
          throw new Error(`Unknown binary operator: ${binOp.op}`);
      }
    }

    case ExprType.Var: {
      const varExpr = expression as IVarExpr<T>;
      return context[varExpr.name];
    }

    case ExprType.Call: {
      const callExpr = expression as ICallExpr<T>;
      const args = callExpr.args.map((arg) => evaluateExpr(arg, context));
      return evaluateFunction(callExpr.func, args, context);
    }

    default:
      throw new Error(`Unknown expression type: ${(expression as any).type}`);
  }
}

// Function evaluator
function evaluateFunction(name: string, args: any[], context: any): any {
  const functions: Record<string, (...args: any[]) => any> = {
    // String operations
    length: (value: any) => value?.length ?? 0,
    len: (value: any) => value?.length ?? 0, // Add alias for 'length'
    isEmpty: (value: any) => !value || value.length === 0,
    matches: (value: string, pattern: string | RegExp) => {
      if (typeof value !== 'string') return false;
      const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
      return regex.test(value);
    },

    // Type checking
    isNull: (value: any) => value === null || value === undefined,
    hasType: (value: ITypeAnnotation, type: string) => value.type.type === type,
    isString: (value: any) => typeof value === 'string',
    isNumber: (value: any) => typeof value === 'number' && !isNaN(value),
    isBoolean: (value: any) => typeof value === 'boolean',

    // Array operations
    includes: (array: any[], value: any) => Array.isArray(array) && array.includes(value),
    count: (array: any[]) => (Array.isArray(array) ? array.length : 0),

    // Numeric operations
    min: (value: number, minValue: number) =>
      typeof value === 'number' ? value >= minValue : false,
    max: (value: number, maxValue: number) =>
      typeof value === 'number' ? value <= maxValue : false,
    between: (value: number, min: number, max: number) =>
      typeof value === 'number' ? value >= min && value <= max : false,

    // String specific
    startsWith: (value: string, prefix: string) =>
      typeof value === 'string' ? value.startsWith(prefix) : false,
    endsWith: (value: string, suffix: string) =>
      typeof value === 'string' ? value.endsWith(suffix) : false,
    contains: (value: string, substring: string) =>
      typeof value === 'string' ? value.includes(substring) : false
  };

  const fn = functions[name];
  if (!fn) throw new Error(`Unknown function: ${name}`);
  return fn(...args);
}
