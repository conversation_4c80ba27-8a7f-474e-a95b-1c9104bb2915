
import Tabs, { TabsProps } from "@components/tabs";
import { Box, CardHeaderProps, Typography } from "@mui/material";
import { createFileRoute } from "@tanstack/react-router";
import InsurenceDetails from "../../../components/technician-tabs/insurance-details";
import StudyDetails from "../../../components/technician-tabs/study-details";
import Profile from "../../../components/technician-tabs/profile";
import { CircleOutlined } from "@mui/icons-material";
import { useState } from "react";
import CardHeader from "@components/card-header";
import styles from "../../../components/technician-tabs/styles";

export const Route = createFileRoute('/_dashboard/technician/')({
   component: Technician,
});


interface TechniciansProps extends Pick<TabsProps, 'onTabChange'> {
   title: string
   icon?: CardHeaderProps['avatar']
   tabItems?: TabsProps['items']
   selectedTab?: number
};

const DEFAULT_TAB_ITEM_S = [
   {
      label: 'Patient Profile',
      index: 0,
      children: <Profile />
   },
   {
      label: 'Study Details',
      index: 1,
      children: <StudyDetails />
   },
   {
      label: 'Insurance Details',
      index: 2,
      children: <InsurenceDetails />
   },
   {
      label: 'Clinical Considerations',
      index: 3,
      children: <p>wqdwq</p>
   }
]

function Technician(props: TechniciansProps) {

   const {
      title = null, tabItems = DEFAULT_TAB_ITEM_S, icon = <CircleOutlined />, selectedTab: selectedTabProp, onTabChange: onTabChangeProp
   } = props;

   const [selectedTabLocal, setSelectedTabLocal] = useState<number>(0);

   const selectedTab = selectedTabProp ?? selectedTabLocal;

   const onTabChange = (newSelectedTab: number) => {
      typeof onTabChangeProp === 'function' && onTabChangeProp(newSelectedTab);
      setSelectedTabLocal(newSelectedTab);
   };

   return (
      <Box
         sx={{ p: 2 }}
      >
         <CardHeader
            {...{
               emphasis: 'dark',
               sx: styles.cardHeader,
               avatar: icon,
               title: (
                  <Typography>
                     {title}
                  </Typography>
               )
            }} />
         <Box sx={styles.cardBody}>
            <Tabs
               {...{
                  items: tabItems,
                  value: selectedTab,
                  onTabChange
               }} />
         </Box>
      </Box>
   );
}

export default Technician;