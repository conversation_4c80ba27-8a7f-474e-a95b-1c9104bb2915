import { useState } from 'react';
import { NotificationState } from '@components/notification-snackbar';

export default function useNotificaion() {
	const [notificationState, setNotification] = useState({
		showToast: false,
		notification: { message: '', severity: 'info' } as NotificationState,
	});

	const setNotificationState = (notification: NotificationState) => {
		setNotification({
			showToast: true,
			notification,
		});
	};

	const closeNotification = () => {
		setNotification({
			showToast: false,
			notification: { message: '', severity: 'info' } as NotificationState,
		});
	};

	return {
		notificationState,
		setNotificationState,
		closeNotification,
	};
}
