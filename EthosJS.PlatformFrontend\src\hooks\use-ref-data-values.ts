import { ReferenceDataSet, ReferenceDataSetKeyValueDto } from '@client/refdata';
import { getApiReferenceSetsKeysOptions } from '@client/refdata/@tanstack/react-query.gen';
import { useQuery } from '@tanstack/react-query';
import { map } from 'lodash';

type RefDataResponse = {
	set: ReferenceDataSet;
	value: ReferenceDataSetKeyValueDto;
};

export function useRefDataValues({ ids }: { ids: Array<number> }) {
	const { data, isFetching, error } = useQuery({
		...getApiReferenceSetsKeysOptions({
			responseType: 'json',
			query: {
				ids,
			},
		}),
		enabled: !!ids.length,
		select: (data) => {
			if (!data) return [];
			const { items } = data as { items: RefDataResponse[] };
			return map(items, (item) => {
				const { value } = item;
				const { values } = value;
				const { code, name } = (values as { code: string; name: string }) ?? {};
				return {
					title: name ?? '',
					description: code ?? '',
				};
			});
		},
	});

	return {
		values: data,
		isFetching,
		error,
	};
}
