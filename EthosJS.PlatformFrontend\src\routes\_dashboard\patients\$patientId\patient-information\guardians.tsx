import GuardiansStep from '@features/patient-create/components/steps/guardians.step';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/patient-information/guardians'
)({
	component: RouteComponent,
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const navigate = Route.useNavigate();

	return (
		<GuardiansStep
			patientId={patientId}
			successCallback={() => {
				navigate({
					to: '/patients/$patientId/patient-information/clinical-considerations',
					params: { patientId },
				});
			}}
		/>
	);
}
