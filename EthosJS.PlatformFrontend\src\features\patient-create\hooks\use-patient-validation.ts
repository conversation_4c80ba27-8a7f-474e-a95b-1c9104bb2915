import { ValidationErrors } from '@app-types/validation';
import { PatientCreate, PatientRead } from '@auth/scopes';
import {
	EthosUtilitiesIssue,
	EthosWorkflowsApiCreatePatientInputDto,
	SystemTextJsonNodesJsonNode,
} from '@client/workflows';
import { postApiPatientDraftValidateMutation } from '@client/workflows/@tanstack/react-query.gen';
import { useMutation } from '@tanstack/react-query';
import { useCallback } from 'react';

function getErrorData(errors: EthosUtilitiesIssue[] | null | undefined): ValidationErrors {
	if (!errors) return undefined;
	const fieldErrors = errors.reduce(
		(acc, error) => {
			const { paths } = error;
			const path = paths?.join('.');
			if (!path) return acc;

			const { message } = error;
			if (!message) return acc;

			const messages = acc[path] ?? [];
			messages.push({ message });

			return { ...acc, [path]: messages };
		},
		{} as Record<string, { message: string }[]>
	);
	return {
		fields: fieldErrors,
	};
}

export default function usePatientValidation() {
	const {
		mutate: validatePatient,
		mutateAsync: validatePatientAsync,
		isPending: isValidationPending,
		error,
	} = useMutation({
		...postApiPatientDraftValidateMutation({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		}),
		onError(error) {
			if (error) {
				console.error(error);
			}
		},
	});

	const validate = useCallback(
		async (data: Partial<EthosWorkflowsApiCreatePatientInputDto>) => {
			validatePatient({
				body: data as unknown as { [key: string]: SystemTextJsonNodesJsonNode },
			});
		},
		[validatePatient]
	);

	const validateAsync = useCallback(
		async (data: Partial<EthosWorkflowsApiCreatePatientInputDto>) => {
			return await validatePatientAsync({
				body: data as unknown as { [key: string]: SystemTextJsonNodesJsonNode },
			});
		},
		[validatePatientAsync]
	);

	const validateFormatted = useCallback(
		async (data: Partial<EthosWorkflowsApiCreatePatientInputDto>) => {
			const res = await validateAsync(data);
			if (res?.errors) {
				return getErrorData(res.errors);
			}
			return undefined;
		},
		[validateAsync]
	);

	return {
		validateFormatted,
		validate,
		validateAsync,
		isValidationPending,
		error,
	};
}
