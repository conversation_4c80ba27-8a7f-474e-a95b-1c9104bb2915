import { GetApiInsuranceVerificationStatusByJobIdResponse } from '@client/workflows';
import MainCardContainer from '@components/main-container/main-card-container';
import { Chip, Stack, Button, Box, List, ListItem, ListItemText } from '@mui/material';
import { XCircle } from 'lucide-react';

export default function DeniedStatus({
	verificationStatus: _verificationStatus,
	onEditInsurance,
	onRetryVerification,
}: {
	verificationStatus: GetApiInsuranceVerificationStatusByJobIdResponse;
	onEditInsurance: () => void;
	onRetryVerification: () => void;
}) {
	return (
		<MainCardContainer
			title="Insurance Verification"
			headerSize="medium"
			icon={<XCircle />}
			color="error"
			emphasis="low"
			descriptionSubheader="Coverage denied for the requested services. The procedure is not covered under the current policy."
			customAction={
				<Chip
					label="Denied"
					sx={{ borderRadius: 2, color: 'black' }}
				/>
			}>
			<Box sx={{ p: 2 }}>
				<List dense>
					<ListItem>
						<ListItemText primary="• Verify procedure code is correct" />
					</ListItem>
					<ListItem>
						<ListItemText primary="• Check if a different plan covers this procedure" />
					</ListItem>
					<ListItem>
						<ListItemText primary="• Consider alternative procedures that may be covered" />
					</ListItem>
				</List>
				<Stack
					direction="row"
					spacing={2}
					sx={{ justifyContent: 'flex-end', mt: 2 }}>
					<Button
						variant="outlined"
						onClick={onEditInsurance}>
						Edit Insurance
					</Button>
					<Button
						variant="contained"
						onClick={onRetryVerification}>
						Retry Verification
					</Button>
				</Stack>
			</Box>
		</MainCardContainer>
	);
}
