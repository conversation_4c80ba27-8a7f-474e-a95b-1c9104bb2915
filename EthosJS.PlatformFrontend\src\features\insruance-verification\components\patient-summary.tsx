import { getApiPatientByIdOptions } from '@client/workflows/@tanstack/react-query.gen';
import ChipSummary from '@components/chip-summary';
import MainCardContainer from '@components/main-container/main-card-container';
import { Typography } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { maskPhoneNumber } from '@utils/maskers';
import { map } from 'lodash';

interface PatientSummaryProps {
	patientId: string;
}

export default function PatientSummary({ patientId }: PatientSummaryProps) {
	const { data: patientData, isFetching: isFetchingPatientData } = useQuery({
		...getApiPatientByIdOptions({
			path: { id: patientId! },
			responseType: 'json',
		}),
		enabled: !!patientId,
	});

	if (isFetchingPatientData) {
		return (
			<MainCardContainer
				title="Patient Information"
				color="primary"
				emphasis="low">
				<Typography
					variant="body1"
					color="primary">
					Loading patient data...
				</Typography>
			</MainCardContainer>
		);
	}

	if (!patientData) {
		return (
			<MainCardContainer
				title="Patient Information"
				color="error"
				emphasis="low">
				<Typography
					variant="body1"
					color="error">
					Error loading patient data
				</Typography>
			</MainCardContainer>
		);
	}

	const { id, names, demographics, contactInformation } = patientData ?? {};

	return (
		<MainCardContainer
			title="Patient Information"
			color="primary"
			emphasis="low">
			<ChipSummary
				items={[
					{
						label: 'Patient ID',
						value: id ? id?.toString() : '',
					},
					{
						label: 'Name',
						value:
							names?.[0]?.firstName + ' ' + names?.[0]?.lastName
								? names?.[0]?.firstName + ' ' + names?.[0]?.lastName
								: '',
					},
					{
						label: 'DOB',
						value: demographics?.dateOfBirth ? demographics?.dateOfBirth : '',
					},
					...map(contactInformation?.phoneNumbers, (phoneNumber, index) => ({
						label: `Phone (${index + 1})`,
						value: maskPhoneNumber(phoneNumber.value),
					})),
				]}
			/>
		</MainCardContainer>
	);
}
