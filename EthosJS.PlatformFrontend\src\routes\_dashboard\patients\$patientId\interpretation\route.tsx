import Card from '@components/card';
import InterpretContentArea from '@features/interpretation/components/content-area';
import InterPretationMenu from '@features/interpretation/components/interpretation-menu';
import { Box } from '@mui/material';
import { createFileRoute, useLocation } from '@tanstack/react-router'

export const Route = createFileRoute(
   '/_dashboard/patients/$patientId/interpretation',
)({
   component: RouteComponent,
})

function RouteComponent() {

   const { patientId } = Route.useParams();
   const { orderId, studyId } = Route.useSearch();
   const navigate = Route.useNavigate();
   const { pathname } = useLocation();

   return (
      <Card
         sx={{
            flex: 1,
            minHeight: 0,
            display: 'flex',
            gap: 2,
            p: 2,
            pb: 0,
            borderRadius: 2,
            borderBottomLeftRadius: 0,
            borderBottomRightRadius: 0,
         }}>
               <InterPretationMenu
                  onSelectMenu={(path) => {
                     navigate({
                        to: `/patients/$patientId/interpretation${path}`,
                        params: { patientId },
                        search: { orderId, studyId },
                     });
                  }}
                  activePath={pathname}
                  orderId={orderId as string}
                  patientId={patientId}
                  studyId={studyId as string}
                  onSelect={() => {
                     navigate({
                        to: '/patients/$patientId/schedule/appointment-creation/dashboard',
                        params: { patientId },
                        search: { orderId: orderId as string, studyId },
                     });
                  }}
               />            
         <Card
            color="primary"
            sx={{
               flex: 1,
               minHeight: 0,
               position: 'relative',
               height: '100%',
               borderBottomLeftRadius: 0,
               borderBottomRightRadius: 0,
            }}>
            <Box
               sx={{
                  flex: 1,
                  overflow: 'auto',
                  height: 'calc(100% - 67px)',
                  pb: 0,
               }}>
            <InterpretContentArea />
            </Box>
         </Card>

      </Card>
   )
}
