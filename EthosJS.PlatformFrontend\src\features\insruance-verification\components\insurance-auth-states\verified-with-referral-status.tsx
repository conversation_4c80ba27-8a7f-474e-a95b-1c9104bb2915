import { GetApiInsuranceVerificationStatusByJobIdResponse } from '@client/workflows';
import MainCardContainer from '@components/main-container/main-card-container';
import { Chip, Stack, TextField, Alert, Button } from '@mui/material';
import { CheckCircle, FileText } from 'lucide-react';
import { useState } from 'react';
import { ReferralInfo } from './types';

export default function VerifiedWithReferralStatus({
	verificationStatus: _verificationStatus,
	onSubmitReferral,
}: {
	verificationStatus: GetApiInsuranceVerificationStatusByJobIdResponse;
	onSubmitReferral: (referralInfo: ReferralInfo) => void;
}) {
	const [referralNumber, setReferralNumber] = useState('');
	const [referralDate, setReferralDate] = useState('');

	const handleSubmit = () => {
		if (referralNumber && referralDate) {
			onSubmitReferral({ referralNumber, referralDate });
		}
	};

	return (
		<MainCardContainer
			title="Insurance Verification"
			headerSize="medium"
			icon={<CheckCircle />}
			color="success"
			emphasis="low"
			descriptionSubheader="Verification complete. Prior authorization is required."
			customAction={
				<Chip
					label="Verified"
					sx={{ borderRadius: 2, color: 'black' }}
				/>
			}>
			<Stack
				spacing={2}
				sx={{ p: 2 }}>
				{/* Referral Alert */}
				<Alert
					severity="warning"
					icon={<FileText />}>
					Referral needed. Please enter referral information.
				</Alert>

				{/* Referral Form */}
				<Stack
					direction="row"
					spacing={2}
					alignItems="center">
					<TextField
						label="Referral Number *"
						value={referralNumber}
						onChange={(e) => setReferralNumber(e.target.value)}
						size="small"
						sx={{ flex: 1 }}
					/>
					<TextField
						label="Referral Date *"
						type="date"
						value={referralDate}
						onChange={(e) => setReferralDate(e.target.value)}
						size="small"
						slotProps={{
							inputLabel: { shrink: true },
						}}
						sx={{ flex: 1 }}
					/>
					<Button
						variant="contained"
						onClick={handleSubmit}
						disabled={!referralNumber || !referralDate}>
						Submit Referral Information
					</Button>
				</Stack>
			</Stack>
		</MainCardContainer>
	);
}
