import { useFieldContext } from '@hooks/form-context';
import { DatePicker } from '@mui/x-date-pickers-pro';
import dayjs, { Dayjs } from 'dayjs';
import { FieldPropType } from './FieldPropType';

const conversionFormat = 'YYYY-MM-DD';

export default function AppDateField({
	label,
	required,
	disabled,
	dataTestId,
}: FieldPropType & { dataTestId?: string }) {
	const {
		state: { value },
		handleChange,
		handleBlur,
		getMeta,
	} = useFieldContext<string>();

	// Convert string to Dayjs if it's a string and not empty
	const dayjsValue =
		typeof value === 'string'
			? value === ''
				? null
				: dayjs(value, conversionFormat)
			: (value as Dayjs | null);

	// Handle change and convert to string format
	const onChange = (date: Dayjs | null) => {
		handleChange(date?.format(conversionFormat) || '');
	};

	const shouldShowError = getMeta().isTouched || getMeta().isDirty;
	const errors = shouldShowError ? getMeta().errors.map((error) => error.message) : [];

	return (
		<DatePicker
			label={label}
			value={dayjsValue}
			onChange={onChange}
			slotProps={{
				textField: {
					required,
					disabled,
					error: shouldShowError && errors.length > 0,
					helperText: errors.join(', '),
					onBlur: () => handleBlur(),
					fullWidth: true,
					slotProps: {
						input: {
							inputProps: {
								'data-testid': dataTestId,
							},
						},
					},
				},
			}}
		/>
	);
}
