import MainCardContainer from '@components/main-container/main-card-container';
import { Chip } from '@mui/material';
import { ShieldCheck } from 'lucide-react';

export default function SelfPayStatus() {
	return (
		<MainCardContainer
			title="Insurance Verification"
			headerSize="medium"
			icon={<ShieldCheck />}
			color="primary"
			emphasis="low"
			descriptionSubheader="Patient will be paying out-of-pocket for services. No insurance verification is needed."
			customAction={
				<Chip
					label="Self-Pay"
					sx={{ borderRadius: 2, color: 'black' }}
				/>
			}
		/>
	);
}
