import type { ChipData } from '@components/chip-summary';
import ChipSummary from '@components/chip-summary';
import MainCardContainer, {
	type MainCardContainerProps,
} from '@components/main-container/main-card-container';
import { useMemo, useState } from 'react';
import { Box, Button, lighten, Stack } from '@mui/material';

interface TabItemDefaultMode {
	name: string;
	value: string;
	chipSummaryData: Array<ChipData>;
	mode?: 'default';
}

interface TabItemArrayMode {
	name: string;
	value: string;
	chipSummaryData: Array<Array<ChipData>>;
	mode: 'array';
}

type TabItemType = TabItemDefaultMode | TabItemArrayMode;

export interface IDetailsReviewContainer {
	mode: 'tab' | 'default';
	tabItems?: Array<TabItemType>;
	defaultValue?: TabItemType['name'];
	mainCardContainerProps?: Partial<MainCardContainerProps>;
}

const DetailsReviewContainer = ({
	mode = 'default',
	tabItems = [],
	defaultValue,
	mainCardContainerProps,
}: IDetailsReviewContainer) => {
	const renderer = useMemo(() => {
		if (mode === 'tab') {
			return <TabView {...{ tabItems, defaultValue }} />;
		}
		return;
	}, [mode]);

	return <MainCardContainer {...mainCardContainerProps}>{renderer}</MainCardContainer>;
};

interface ITabView extends Omit<IDetailsReviewContainer, 'mode'> {
	value?: TabItemType['value'];
	defaultValue?: ITabView['value'];
	onTabSelect?: (selectedTab: ITabView['value']) => void;
}

const TabView = ({
	defaultValue,
	value: valueProps,
	tabItems,
	onTabSelect: onTabSelectProp,
}: ITabView) => {
	const [localValue, setLocalValue] = useState<ITabView['value']>(defaultValue);

	const value = localValue ?? valueProps;

	const currentChipSummaryContext = useMemo(
		() => tabItems?.find((i) => i.value === value),
		[value]
	);

	const items = currentChipSummaryContext?.chipSummaryData || [];

	const onTabSelect: ITabView['onTabSelect'] = (requestedValue) => {
		onTabSelectProp?.(requestedValue);
		setLocalValue(requestedValue);
	};

	return (
		<Stack
			sx={{
				gap: 2,
			}}>
			<Stack
				direction={'row'}
				gap={2}>
				{tabItems?.map(({ chipSummaryData, ...item }) => {
					const isSelected = value === item.value;
					return (
						<Button
							onClick={() => onTabSelect(item.value)}
							key={item.value}
							variant={isSelected ? 'contained' : 'outlined'}
							color={isSelected ? 'primary' : 'primary'}>
							{item.name}
						</Button>
					);
				})}
			</Stack>

			<Box
				sx={({ palette }) => {
					return {
						p: 1.5,
						background: lighten(palette.primary.light, 0.8),
						borderWidth: 1,
						borderStyle: 'solid',
						borderColor: lighten(palette.primary.light, 0.7),
						borderRadius: 0.4,
					};
				}}>
				{currentChipSummaryContext?.mode === 'array' ? (
					<Stack gap={2}>
						{items?.map((item) => {
							return (
								<ChipSummary
									items={(item as ChipData[])?.map((i) => ({ ...i, color: 'primary' }))}
									variant="outlined"
								/>
							);
						})}
					</Stack>
				) : (
					<ChipSummary
						items={(items as ChipData[])?.map((i) => ({ ...i, color: 'primary' }))}
						variant="outlined"
					/>
				)}
			</Box>
		</Stack>
	);
};

export default DetailsReviewContainer;
