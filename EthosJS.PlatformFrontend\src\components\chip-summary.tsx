import { Edit } from '@mui/icons-material';
import { Box, Chip, IconButton, Stack } from '@mui/material';

interface ChipData extends Pick<ChipSummaryProps, 'variant'> {
	label?: string;
	value: string;
	action?: React.ReactNode;
	hideSeperator?: boolean;
	color?: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
}

interface ChipSummaryProps {
	items: ChipData[];
	onEdit?: () => void;
	hideSeperator?: boolean;
	variant?: 'filled' | 'outlined';
}

export default function ChipSummary({
	items,
	onEdit,
	hideSeperator = false,
	variant = 'filled',
}: ChipSummaryProps) {
	return (
		<Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
			<Stack
				direction="row"
				spacing={1}
				flex={1}
				flexWrap="wrap"
				useFlexGap>
				{items
					?.filter((i) => !!i?.value)
					?.map((item, index) => (
						<Chip
							key={index}
							variant={item?.variant ?? variant}
							sx={{
								borderRadius: 6,
							}}
							color={item.color ?? 'default'}
							label={
								<Box component="span">
									<Box
										component="span"
										sx={{ fontWeight: 'bold', mr: 0.5 }}>
										{item.label}
										{`${(item?.hideSeperator ?? hideSeperator) ? '' : ':'}`}
									</Box>
									{item.value}
								</Box>
							}
						/>
					))}
			</Stack>
			{onEdit && (
				<IconButton onClick={onEdit}>
					<Edit />
				</IconButton>
			)}
		</Box>
	);
}

export type { ChipSummaryProps, ChipData };
