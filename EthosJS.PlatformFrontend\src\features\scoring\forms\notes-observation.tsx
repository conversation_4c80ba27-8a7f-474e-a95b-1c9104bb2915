import ChipSummary from "@components/chip-summary";
import CollapsibleMainCardContainer from "@components/collapsible-main-card-container";
import MainCardContainer from "@components/main-container/main-card-container";
import { Note } from "@mui/icons-material";
import { Button, Stack, Typography } from "@mui/material";

import dayjs from 'dayjs';

const NotesObservation = () => {

   const notes = [
      {
         id: '1',
         createdBy: {
            firstName: '<PERSON>',
            lastName: '<PERSON>',
            fullName: '<PERSON>'
         },
         createdAt: new Date(),
         observationType: 'Respiratory Event',
         epochTime: ' Epoch 287',
         note: {
            content: '45-second obstructive apnea with significant desaturation to 78%. Event occurred during REM sleep. Pat<PERSON> showed strong arousal response. Clear airflow cessation with continued respiratory effort observed'
         }
      },
      {
         id: '2',
         createdBy: {
            firstName: 'John',
            lastName: '<PERSON>',
            fullName: '<PERSON>'
         },
         createdAt: new Date(),
         observationType: 'Respiratory Event',
         epochTime: ' Epoch 287',
         note: {
            content: '45-second obstructive apnea with significant desaturation to 78%. Event occurred during REM sleep. <PERSON><PERSON> showed strong arousal response. Clear airflow cessation with continued respiratory effort observed'
         }
      },
      {
         id: '2',
         createdBy: {
            firstName: 'John',
            lastName: 'Scott',
            fullName: 'John Scott'
         },
         createdAt: new Date(),
         observationType: 'Respiratory Event',
         epochTime: ' Epoch 287',
         note: {
            content: '45-second obstructive apnea with significant desaturation to 78%. Event occurred during REM sleep. Patient showed strong arousal response. Clear airflow cessation with continued respiratory effort observed'
         }
      }
   ]


   return (
      <>
         <CollapsibleMainCardContainer
            mainContainerProps={{
               title: 'Scoring Notes & Observations',
               primaryActionType: 'Add',
               icon: <Note />,
               emphasis: !!notes.length ? 'high' : 'low',
               customAction: (
                  <Button
                     variant="contained"
                     sx={({ palette }) => ({
                        bgcolor: palette.common.white,
                        color: palette.common.black,
                        borderRadius: 2
                     })}
                  >
                     {notes.length} Notes
                  </Button>
               ),
            }}
            collapse={!!notes.length}
         >
            <Stack gap={1}>
               {notes.map(({ createdBy, createdAt, observationType, epochTime, ...note }) => {
                  return (
                     <MainCardContainer
                        {...{
                           title: `Added by ${createdBy.fullName}, ${dayjs(createdAt).format('lll')}`,
                           primaryActionType: 'Edit'
                        }}
                     >
                        <Stack gap={2}>
                           <ChipSummary
                              items={[
                                 {
                                    label: 'Observation Type',
                                    value: observationType
                                 },
                                 {
                                    label: 'Epoch/Time',
                                    value: epochTime
                                 }
                              ]}
                           />

                           <Stack
                              p={2}
                              gap={1}
                              sx={({ palette }) => {
                                 return {
                                    bgcolor: palette.grey[200],
                                    borderRadius: 2
                                 }
                              }}
                           >
                              <Typography variant="body1" color="black">{note.note.content}</Typography>
                           </Stack>
                        </Stack>
                     </MainCardContainer>
                  )
               })}
            </Stack>
         </CollapsibleMainCardContainer>
      </>
   )
}

export default NotesObservation;