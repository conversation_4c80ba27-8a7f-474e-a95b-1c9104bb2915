import { Status } from '@components/left-menu';
import AddBasicInformationStep from './basic-information.step';
import AddContactsStep from './contacts.step';
import AddAddressesStep from './addresses.step';
import AddInsurancesStep from './insurances.step';
import AddGuardiansStep from './guardians.step';
import AddClinicalInformationStep from './clinical-considerations.step';
import { FileUser, Phone, Home, ShieldCheck, HeartHandshake, BriefcaseMedical } from 'lucide-react';

export const createPatientSteps = [
	{
		name: 'Demographics',
		key: 'AddBasicInformation',
		icon: FileUser,
		component: AddBasicInformationStep,
		status: Status.None,
		data: {},
		from: undefined,
		to: 'AddContacts',
	},
	{
		name: 'Contacts',
		key: 'AddContacts',
		icon: Phone,
		component: AddContactsStep,
		status: Status.None,
		data: {},
		from: 'AddBasicInformation',
		to: 'AddAddresses',
	},
	{
		name: 'Addresses',
		key: 'AddAddresses',
		icon: Home,
		component: AddAddressesStep,
		status: Status.None,
		data: {},
		from: 'AddContacts',
		to: 'AddInsurances',
	},
	{
		name: 'Insurances',
		key: 'AddInsurances',
		icon: ShieldCheck,
		component: AddInsurancesStep,
		status: Status.None,
		data: {},
		from: 'AddAddresses',
		to: 'AddGuardians',
	},
	{
		name: 'Guardians',
		key: 'AddGuardians',
		icon: HeartHandshake,
		component: AddGuardiansStep,
		status: Status.None,
		data: {},
		from: 'AddInsurances',
		to: 'AddClinicalInformation',
	},
	{
		name: 'Clinical Considerations',
		key: 'AddClinicalInformation',
		icon: BriefcaseMedical,
		component: AddClinicalInformationStep,
		status: Status.None,
		data: {},
		from: 'AddGuardians',
		to: 'Committed',
	},
];
