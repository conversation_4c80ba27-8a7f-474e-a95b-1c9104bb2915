import LeftMenu, { Status } from '@components/left-menu';
import StepCard from '@components/step-card';
import {
	VerifiedUser,
	TroubleshootOutlined as TroubleshootOutlinedIcon,
} from '@mui/icons-material';
import { Box, CardContent, List } from '@mui/material';
import { createFileRoute, Outlet } from '@tanstack/react-router';
import { useStore } from '@tanstack/react-store';
import { useEffect } from 'react';
import { z } from 'zod';
import useVisitStore from '@hooks/use-visit-store';
import { ClipboardPlus, ClipboardCheck } from 'lucide-react';
import SelectionControls from '@features/visits/selection-controls';

const validateSearch = z.object({
	orderId: z.string(),
	studyId: z.string().optional(),
});

export const Route = createFileRoute('/_dashboard/patients/$patientId/visit')({
	component: RouteComponent,
	validateSearch,
});

export const visitSteps = [
	{
		name: 'Pre-Study',
		key: 'PreStudy',
		icon: ClipboardPlus,
		stepNumber: 1,
		status: Status.None,
		from: undefined,
		to: 'PreStudy',
		data: {},
		navigationURL: `/patients/$patientId/visit/pre-study`,
	},
	{
		name: 'Study',
		key: 'Study',
		icon: TroubleshootOutlinedIcon,
		status: Status.None,
		from: undefined,
		stepNumber: 2,
		to: 'Study',
		data: {},
		navigationURL: `/patients/$patientId/visit/study`,
	},
	{
		name: 'Post Study',
		key: 'PostStudy',
		icon: ClipboardCheck,
		status: Status.None,
		from: undefined,
		stepNumber: 3,
		to: 'PostStudy',
		data: {},
		navigationURL: `/patients/$patientId/visit/post-study`,
	},
];

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const navigate = Route.useNavigate();

	const onNavigateStep = (selectedStep: (typeof visitSteps)[number]) => {
		const search = { orderId: orderId, studyId: studyId };
		navigate({
			to: selectedStep.navigationURL,
			params: { patientId },
			search,
		});
	};

	const onFieldChange = (fieldName: string, fieldValue: string) => {
		navigate({
			to: '/patients/$patientId/visit/pre-study',
			params: { patientId },
			search: (prev) => ({ ...prev, [fieldName]: fieldValue }),
		});
	};
	return (
		<Box sx={{ flex: 1, minHeight: 0 }}>
			<StepCard
				title="Verify Insurance"
				icon={VerifiedUser}
				showHeader={false}
				isLoading={false}>
				<CardContent sx={styles.cardContent}>
					<Box sx={styles.cardContentInner}>
						<SelectionControls {...{ patientId, onFieldChange: onFieldChange, studyId, orderId }} />
						<List
							sx={{ width: 220 }}
							disablePadding>
							{/* {steps.map((step, index) => {
								return (
									<LeftMenu
										name={step.name}
										icon={step.icon}
										status={step.status}
										stepNumber={step.stepNumber}
										disabled={step.status === Status.None}
										onClick={() => onNavigateStep(step)}
										key={index.toString()}
									/>
								);
							})} */}
						</List>
					</Box>
					<Box sx={styles.outletContent}>
						<Outlet />
					</Box>
				</CardContent>
			</StepCard>
		</Box>
	);
}

const styles = {
	cardContent: {
		overflow: 'auto',
		minHeight: 0,
		flex: 1,
		display: 'flex',
		gap: 2,
		height: '100%',
	},
	cardContentInner: { overflow: 'auto', height: '100%' },
	outletContent: { flex: 1, overflow: 'auto', height: '100%' },
};
