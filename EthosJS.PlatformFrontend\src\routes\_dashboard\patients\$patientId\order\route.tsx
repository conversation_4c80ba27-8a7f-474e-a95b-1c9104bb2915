import Card from '@components/card';
import OrderCreateMenu from '@features/order-create/components/menu';
import { Box, Button } from '@mui/material';
import { createFileRoute, Outlet, redirect, useLocation } from '@tanstack/react-router';
import z from 'zod';

const OrderCreateParams = z.object({
	orderId: z.string(),
});

export const Route = createFileRoute('/_dashboard/patients/$patientId/order')({
	component: RouteComponent,
	validateSearch: OrderCreateParams,
	loaderDeps: ({ search }) => ({ ...search }),
	beforeLoad: ({ params, search, location }) => {
		if (location.pathname === `/_dashboard/patients/${params.patientId}/_create-order`) {
			throw redirect({
				to: '/patients/$patientId/order/study',
				params: { patientId: params.patientId },
				search,
			});
		}
	},
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const { pathname } = useLocation();
	const navigate = Route.useNavigate();

	if (!orderId) {
		return (
			<Box>
				<Button>Create Order</Button>
			</Box>
		);
	}

	return (
		<Card
			sx={{
				flex: 1,
				minHeight: 0,
				display: 'flex',
				gap: 2,
				p: 2,
				pb: 0,
				borderRadius: 2,
				borderBottomLeftRadius: 0,
				borderBottomRightRadius: 0,
			}}>
			<OrderCreateMenu
				patientId={patientId}
				orderId={orderId}
				activePath={pathname}
				studyId={studyId}
				onSelect={({ orderId, studyId }) => {
					navigate({
						to: '/patients/$patientId/order/study',
						params: { patientId },
						search: { orderId, studyId },
					});
				}}
				onClick={(path) => {
					navigate({
						to: `/patients/$patientId/order${path}`,
						params: { patientId },
						search: { orderId, studyId },
					});
				}}
			/>
			<Card
				color="primary"
				sx={{
					flex: 1,
					minHeight: 0,
					position: 'relative',
					p: 1,
					pb: 0,
					height: '100%',
					borderBottomLeftRadius: 0,
					borderBottomRightRadius: 0,
				}}>
				{/* <Box
					sx={{
						flex: 1,
						overflow: 'auto',
						height: 'calc(100% - 67px)',
						pb: 0,
					}}> */}
				<Outlet />
				{/* </Box> */}
			</Card>
		</Card>
	);
}
