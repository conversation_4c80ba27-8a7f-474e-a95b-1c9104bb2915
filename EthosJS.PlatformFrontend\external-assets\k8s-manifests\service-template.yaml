apiVersion: v1
kind: Service
metadata:
  name: ethosjs-platformfrontend-nodeport
  namespace: ethos-ns-uat
spec:
  selector:
    app: ethosjs-platformfrontend
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
      nodePort: 30082
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: ethosjs-platformfrontend-loadbalancer
  namespace: ethos-ns-uat
spec:
  selector:
    app: ethosjs-platformfrontend
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: LoadBalancer 