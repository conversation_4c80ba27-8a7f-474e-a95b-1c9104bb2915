import { MainCardContainerProps } from '@components/main-container/main-card-container';

export interface Note {
  id: string;
  header: string;
  subHeader?: string;
  descriptionSubheader?: string;
  descriptionText?: string;
  content: string;
}

export interface NotesContainerProps {
  title: string;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  onAddNote?: (content: string) => void;
  onDeleteNote?: (noteId: string) => void;
  allowAdd?: boolean;
  allowDelete?: boolean;
  placeholder?: string;
  maxLength?: number;
  footerProps?: MainCardContainerProps['footerProps'];
  color?: MainCardContainerProps['color'];
  descriptionSubheader?: string;
  descriptionText?: string;
}

export interface NoteItemProps {
  note: Note;
  allowEdit?: boolean;
  onEdit?: (updatedNote: Omit<Note, 'id'>) => void;
  onDelete?: () => void;
  size?: 'medium' | 'small';
  footerProps?: MainCardContainerProps['footerProps'];
  icon?: React.ReactNode;
  onStartEdit?: (noteId: string) => void;
  onCancelEdit?: () => void;
  isEditing?: boolean;
}

export interface NoteFormProps {
  onSave: (content: string) => void;
  onCancel: () => void;
}

export interface SingleNoteItemProps {
  note: Note;
  allowEdit?: boolean;
  onEdit?: (updatedNote: Omit<Note, 'id'>) => void;
  onDelete?: () => void;
  size?: 'medium' | 'small';
  footerProps?: MainCardContainerProps['footerProps'];
  icon?: React.ReactNode;
}