import React, { useState, useEffect } from 'react';
import { Stack, Grid2 as Grid, Box, Typography, IconButton } from '@mui/material';
import { CheckCircle, Delete as DeleteIcon } from '@mui/icons-material';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-form';
import { useQuery } from '@tanstack/react-query';
import { getApiFileStatusByFileIdOptions } from '@client/workflows/@tanstack/react-query.gen';
import { FileText } from 'lucide-react';
import MainCardContainer from '@components/main-container/main-card-container';
import FileUpload from './file-upload';
import { FileUploadFormProps, FileFormData } from './types';
import { PatientCreate, PatientRead } from '@auth/scopes';

const categoryOptions = [
  { label: 'Identity Documents', value: 'Identity Documents' },
  { label: 'Insurance Documents', value: 'Insurance Documents' },
  { label: 'Medical Forms', value: 'Medical Forms' },
  { label: 'Consent Forms', value: 'Consent Forms' }
];

const documentTypeOptions = [
  { label: 'Photo ID', value: 'Photo ID' },
  { label: 'Insurance Card', value: 'Insurance Card' },
  { label: 'Pre-Study Questionnaire', value: 'Pre-Study Questionnaire' },
  { label: 'Sleep Study Consent Form', value: 'Sleep Study Consent Form' },
  { label: 'HIPAA Acknowledgment', value: 'HIPAA Acknowledgment' },
  { label: 'Video Recording Consent', value: 'Video Recording Consent' }
];

const dateValues = [{ label: '0000-00-00', value: 'issue-date' }];

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`;
}

export const FileUploadForm: React.FC<FileUploadFormProps> = ({
  title,
  icon,
  onSave,
  onCancel,
  onDelete,
  workflowId,
  initialData,
  showUploadedFile = false
}) => {
  const allowMultiple = true;

  const [uploadedFile, setUploadedFile] = useState(
    showUploadedFile && initialData?.fileName && initialData?.fileId
      ? { fileId: initialData.fileId, fileName: initialData.fileName, fileSize: 2400000 }
      : null
  );

  const [uploadedFiles, setUploadedFiles] = useState<
    Array<{
      fileId: string;
      fileName: string;
      fileSize: number;
    }>
  >([]);

  const form = useAppForm({
    defaultValues: {
      category: initialData?.category || '',
      documentType: initialData?.documentType || '',
      issueDate: initialData?.issueDate || '',
      expiryDate: initialData?.expiryDate || ''
    } as FileFormData
  });

  const { values } = useStore(form.store, (state) => ({
    values: state.values
  }));

  const { data: fileStatus } = useQuery({
    ...getApiFileStatusByFileIdOptions({
      path: { fileId: initialData?.fileId! },
      scopes: [PatientCreate.value, PatientRead.value],
      responseType: 'json'
    }),
    enabled: !!initialData?.fileId
  });

  // Initialize form with initial data when component mounts or initialData changes
  useEffect(() => {
    if (initialData) {
      form.setFieldValue('category', initialData.category || '');
      form.setFieldValue('documentType', initialData.documentType || '');
      form.setFieldValue('issueDate', initialData.issueDate || '');
      form.setFieldValue('expiryDate', initialData.expiryDate || '');
    } else {
      form.reset();
      setUploadedFile(null);
    }
  }, [initialData, form]);

  useEffect(() => {
    if (fileStatus && initialData) {
      const status: any = fileStatus;

      // Set form values from API response
      form.setFieldValue('category', initialData.category || '');
      form.setFieldValue('documentType', initialData.documentType || '');
      form.setFieldValue('issueDate', initialData.issueDate || '');
      form.setFieldValue('expiryDate', initialData.expiryDate || '');

      const fileName = status.fileName || status.originalFileName;
      const fileSize = status.fileSize || 2400000;

      if (status.fileId && fileName) {
        setUploadedFile({ fileId: status.fileId, fileName, fileSize });
      }
    } else if (initialData && !fileStatus) {
      // If we have initialData but no fileStatus yet, set uploaded file from initialData
      // This ensures the form validation passes while waiting for the API response
      if (initialData.fileId && initialData.fileName) {
        setUploadedFile({
          fileId: initialData.fileId,
          fileName: initialData.fileName,
          fileSize: 2400000 // Default size
        });
      }
    }
  }, [fileStatus, initialData, form]);

  const isFormValid = () => {
    const hasFields =
      values.category?.trim() &&
      values.documentType?.trim() &&
      values.issueDate?.trim() &&
      values.expiryDate?.trim();
    const hasFile = allowMultiple
      ? uploadedFiles.length > 0
      : uploadedFile?.fileId || initialData?.fileId;
    return !!hasFields && !!hasFile;
  };

  const handleSave = () => {
    if (allowMultiple) {
      if (uploadedFiles.length === 0) return;
      uploadedFiles.forEach((file) => {
        const formData: FileFormData = {
          ...values,
          fileId: file.fileId,
          fileName: file.fileName
        };
        onSave(formData);
      });
      setUploadedFiles([]);
      form.reset();
    } else {
      const fileId = uploadedFile?.fileId || initialData?.fileId;
      const fileName = uploadedFile?.fileName || initialData?.fileName;

      if (!fileId || !fileName) return;

      const formData: FileFormData = {
        ...values,
        fileId,
        fileName
      };

      onSave(formData);
      form.reset();
      setUploadedFile(null);
    }
  };

  const handleCancel = () => {
    form.reset();
    setUploadedFile(null);
    onCancel();
  };

  const handleFileUpload = (fileId: string, fileName: string, fileSize: number) => {
    if (allowMultiple) {
      setUploadedFiles((prev) => [...prev, { fileId, fileName, fileSize }]);
    } else {
      setUploadedFile({ fileId, fileName, fileSize });
    }
  };

  const handleFileDelete = () => {
    setUploadedFile(null);
    form.setFieldValue('fileId', '');
    form.setFieldValue('fileName', '');
  };

  return (
    <MainCardContainer
      title={title}
      icon={icon || <FileText />}
      emphasis="high"
      color="primary"
      primaryActionType="Delete"
      onPrimaryAction={handleCancel}
      footerProps={{
        primaryButton1: {
          label: initialData ? 'Save' : 'Add',
          onClick: handleSave,
          disabled: !isFormValid()
        },
        primaryButton2: {
          label: 'Cancel',
          onClick: handleCancel
        },
        ...(initialData &&
          onDelete && {
            secondaryButton1: {
              label: 'Delete',
              onClick: onDelete
            }
          })
      }}>
      <Stack gap="16px" sx={{ p: '16px' }}>
        <Grid container spacing={2}>
          <Grid size={12}>
            <form.AppField
              name="category"
              children={(field) => (
                <field.AppSelectField label="Select Category" options={categoryOptions} required />
              )}
            />
          </Grid>
          <Grid size={12}>
            <form.AppField
              name="documentType"
              children={(field) => (
                <field.AppSelectField
                  label="Select Document Type"
                  options={documentTypeOptions}
                  required
                />
              )}
            />
          </Grid>
        </Grid>

        <Grid container spacing={2}>
          <Grid size={6}>
            <form.AppField
              name="issueDate"
              children={(field) => (
                <field.AppSelectField label="Issue Date" options={dateValues} required />
              )}
            />
          </Grid>
          <Grid size={6}>
            <form.AppField
              name="expiryDate"
              children={(field) => (
                <field.AppSelectField label="Expiry Date" options={dateValues} required />
              )}
            />
          </Grid>
        </Grid>

        {values.category && values.documentType && values.issueDate && values.expiryDate && (
          <Stack gap="16px">
            {!uploadedFile ? (
              <FileUpload
                workflowId={workflowId}
                onFileUpload={handleFileUpload}
                allowMultiple={allowMultiple}
              />
            ) : (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  gap: '16px'
                }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                  <FileText color="#5E35B1" />
                  <Box>
                    <Typography
                      variant="subtitle1"
                      fontWeight="regular"
                      color="black"
                      fontSize="1rem">
                      {uploadedFile.fileName || 'Unknown file'}
                    </Typography>
                    <Typography variant="body2" fontWeight="regular" fontSize="0.875rem">
                      {formatFileSize(uploadedFile.fileSize)} • completed
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                  <IconButton onClick={handleFileDelete} size="small" color="default">
                    <DeleteIcon />
                  </IconButton>
                  <CheckCircle color="success" sx={{ height: '24px', width: '24px' }} />
                </Box>
              </Box>
            )}
          </Stack>
        )}
      </Stack>
    </MainCardContainer>
  );
};
