import {
	EthosWorkflowsApiPersonalEmailDto,
	EthosWorkflowsApiPersonalEmergencyContactDto,
	EthosWorkflowsApiPersonalPhoneNumberDto,
} from '@client/workflows';
import { ChipData } from '@components/chip-summary';
import { filter } from 'lodash';

function formatPhoneNumberSummary(
	values: EthosWorkflowsApiPersonalPhoneNumberDto
): Array<ChipData> {
	const summary: Array<ChipData> = [];

	summary.push({
		label: 'Type',
		value: values.type ? values.type.toString() : '',
	});

	summary.push({
		label: 'Phone',
		value: values.value,
	});

	summary.push({
		label: 'Preferred Time',
		value: values.preferredTime ? values.preferredTime.toString() : '',
	});

	return filter(
		summary,
		(item) => typeof item.label !== 'undefined' && typeof item.value !== 'undefined'
	);
}

function formatEmailSummary(values: EthosWorkflowsApiPersonalEmailDto): Array<ChipData> {
	const summary: Array<ChipData> = [];

	// Add email
	summary.push({
		label: 'Email',
		value: values.value,
	});

	summary.push({
		label: 'Type',
		value: values.use ? values.use.toString() : '',
	});

	return summary;
}

function formatEmergencyContactSummary(
	values: EthosWorkflowsApiPersonalEmergencyContactDto
): Array<ChipData> {
	const summary: Array<ChipData> = [];

	// Add name
	summary.push({
		label: 'Name',
		value: `${values.firstName} ${values.lastName}`,
	});

	// Add phone number
	summary.push({
		label: 'Phone',
		value: values.contactInformation,
	});

	summary.push({
		label: 'Relationship',
		value: values.relationship ? values.relationship.toString() : '',
	});

	return summary;
}

export { formatPhoneNumberSummary, formatEmailSummary, formatEmergencyContactSummary };
