import { EthosWorkflowsApiPersonalAddressDto } from '@client/workflows';
import { ChipData } from '@components/chip-summary';

// TODO: Get this from reference data
export const USA_CODE = 1541;

export const defaultValues = {
	use: null!,
	type: null!,
	address: {
		line1: '',
		line2: '',
		city: '',
		state: null!,
		postalCode: '',
		country: USA_CODE,
	},
};

export function addressFormOptions(savedData?: EthosWorkflowsApiPersonalAddressDto, use?: number) {
	return {
		defaultValues: {
			...defaultValues,
			use: use ?? defaultValues.use,
			...(savedData ? savedData : {}),
		},
	};
}

export function formatAddressSummary(
	values: EthosWorkflowsApiPersonalAddressDto | undefined
): Array<ChipData> {
	if (!values) {
		return [];
	}
	const summary: Array<ChipData> = [];

	// Add address type
	summary.push({
		label: 'Type',
		value: values.type ? values.type.toString() : '',
	});

	// Format the full address in standard format
	const addressLines = [];
	addressLines.push(values?.address?.line1 || '');
	if (values?.address?.line2) {
		addressLines.push(values?.address?.line2);
	}
	addressLines.push(
		`${values?.address?.city || ''}, ${values?.address?.state || ''} ${values?.address?.postalCode || ''}`
	);

	summary.push({
		label: 'Address',
		value: addressLines.join('\n'),
	});

	return summary;
}
