import { <PERSON><PERSON><PERSON>y, <PERSON><PERSON><PERSON>, WbSunny } from "@mui/icons-material";
import { IconButton, ListItemIcon, Menu, MenuItem, useColorScheme } from "@mui/material";
import { useRef, useState } from "react";

const icons = {
  light: <WbSunny />,
  dark: <NightsStay />,
  system: <Settings />,
};

export default function ThemeMenu() {
  const { mode = 'light', setMode } = useColorScheme();
  const [open, setOpen] = useState(false);

  const anchorEl = useRef<HTMLButtonElement | null>(null);

  const handleMenuClick = () => {
    setOpen(true);
  };

  const handleMenuClose = () => {
    setOpen(false);
  };

  return (
    <>
      <IconButton
        ref={anchorEl}
        onClick={handleMenuClick}
        color="inherit"
        aria-label="select color scheme"
      >
        {icons[mode]}
      </IconButton>
      <Menu
        anchorEl={anchorEl.current}
        open={open}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => setMode('light')} selected={mode === 'light'}>
          <ListItemIcon>
            {icons.light}
          </ListItemIcon>
          Light
        </MenuItem>
        <MenuItem onClick={() => setMode('dark')} selected={mode === 'dark'}>
          <ListItemIcon>{icons.dark}</ListItemIcon>
          Dark
        </MenuItem>
        <MenuItem onClick={() => setMode('system')} selected={mode === 'system'}>
          <ListItemIcon>{icons.system}</ListItemIcon>
          System
        </MenuItem>
      </Menu>
    </>
  )
}