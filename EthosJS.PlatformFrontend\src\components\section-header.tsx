import { Box, darken, styled, Typography } from "@mui/material"

const SectionHeaderStyled = styled(Typography)(({ theme }) => ({
    color: darken(theme.palette.primary.dark, 0.3),
}))

interface SectionHeaderProps {
    title: string;
    subtitle?: string;
    optional?: boolean;
}

export default function SectionHeader({ title, subtitle, optional }: SectionHeaderProps) {
    return (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, justifyContent: 'space-between' }}>
                <SectionHeaderStyled variant="h5" margin={0}>
                    {title}
                </SectionHeaderStyled>
                {
                    optional && <SectionHeaderStyled variant="body2" fontStyle="italic" >
                        (Optional)
                    </SectionHeaderStyled>
                }
            </Box>
            {
                subtitle && <SectionHeaderStyled variant="body2">
                    {subtitle}
                </SectionHeaderStyled>
            }
        </Box>
    )
}