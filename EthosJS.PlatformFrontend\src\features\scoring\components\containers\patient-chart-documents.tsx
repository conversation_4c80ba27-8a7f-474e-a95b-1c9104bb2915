import { NotesOutlined } from "@mui/icons-material";
import CollapsibleMainCardContainer from "@components/collapsible-main-card-container";
import List from '@components/list'
import { useState } from "react";
import { Icon<PERSON>utton, Stack } from "@mui/material";
import { Download, Edit, Eye } from "lucide-react";

const PatientCharts = () => {
   const [isExpanded, setIsExpanded] = useState<boolean>(false);

   const data = [
      {
         value: '1',
         title: 'Sleep Study Order & Prescription',
         subTitle: 'PSG_Study_Order_Dr_<PERSON>_Brown_01-01-2025.pdf Uploaded by Dr. <PERSON> · 1 day ago',
      },
      {
         value: '2',
         title: 'Sleep Study Order & Prescription',
         subTitle: 'PSG_Study_Order_Dr_<PERSON>_Brown_01-01-2025.pdf Uploaded by Dr. <PERSON> · 1 day ago',
      },
      {
         value: '3',
         title: 'Sleep Study Order & Prescription',
         subTitle: 'PSG_Study_Order_Dr_<PERSON>_<PERSON>_01-01-2025.pdf Uploaded by Dr. <PERSON> · 1 day ago',
      },
      {
         value: '4',
         title: 'Sleep Study Order & Prescription',
         subTitle: 'PSG_Study_Order_Dr_Sarah_Brown_01-01-2025.pdf Uploaded by Dr. <PERSON> · 1 day ago',
      }
   ]

   return (
      <CollapsibleMainCardContainer
         mainContainerProps={{
            title: 'Patient Chart Documents',
            icon: <NotesOutlined />,
            emphasis: isExpanded ? 'high' : 'low',
         }}
         onToggleCollapse={setIsExpanded}
         collapse={isExpanded}
      >
         <List
          items={data?.map((i) => {
            return ({
               ...i,
               extra: (
                  <Stack direction={'row'} gap={0}>
                     <IconButton>
                        <Download />
                     </IconButton>
                     <IconButton>
                        <Eye />
                     </IconButton>
                     <IconButton>
                        <Edit />
                     </IconButton>
                  </Stack>
               )
            })
         })}

         />
      </CollapsibleMainCardContainer>
   )
}

export default PatientCharts;