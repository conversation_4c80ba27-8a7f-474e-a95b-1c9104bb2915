import { createFileRoute, <PERSON> } from '@tanstack/react-router';

export const Route = createFileRoute('/')({
	component: Home,
});

import {
	AppBar,
	Toolbar,
	Typography,
	Container,
	Button,
	Grid2 as Grid,
	Card,
	CardContent,
	CardActions,
	Box,
	Stack,
} from '@mui/material';
import { useAccount, useMsal } from '@azure/msal-react';
import { loginRequest } from '@auth/msal';

export default function Home() {
	const { instance } = useMsal();
	const account = useAccount();

	const handleLogin = async () => {
		try {
			await instance.loginRedirect({
				scopes: loginRequest.scopes,
			});
		} catch (error) {
			console.error('Login failed: ', error);
		}
	};

	return (
		<>
			<AppBar position="static">
				<Toolbar>
					<Stack
						direction="column"
						flexGrow={1}
						justifyContent="center"
						alignItems="start">
						<Typography
							variant="h6"
							component="div">
							Ethos Landing Page - {import.meta.env.VITE_APP_VERSION}
						</Typography>
						<Typography
							variant="caption"
							component="div">
							{import.meta.env.VITE_TFVC_CHANGESET}
						</Typography>
					</Stack>

					{account ? (
						<Link
							to="/patients"
							style={{ color: 'white', textDecoration: 'none' }}>
							<Button color="inherit">Dashboard</Button>
						</Link>
					) : (
						<Button
							color="inherit"
							onClick={handleLogin}>
							Sign In
						</Button>
					)}
				</Toolbar>
			</AppBar>
			<Container maxWidth="lg">
				<Box sx={{ my: 4 }}>
					<Typography
						variant="h2"
						component="h1"
						gutterBottom>
						Welcome to Our Amazing Product
					</Typography>
					<Typography
						variant="h5"
						component="h2"
						gutterBottom>
						Discover how we can transform your business with our innovative solutions.
					</Typography>
					<Button
						variant="contained"
						size="large"
						sx={{ mt: 2 }}>
						Get Started
					</Button>
				</Box>

				<Grid
					container
					spacing={4}
					sx={{ my: 4 }}>
					{[1, 2, 3].map((item) => (
						<Grid
							size={{ xs: 12, md: 4 }}
							key={item}>
							<Card>
								<CardContent>
									<Typography
										variant="h5"
										component="div"
										gutterBottom>
										Feature {item}
									</Typography>
									<Typography
										variant="body2"
										color="text.secondary">
										Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor
										incididunt ut labore et dolore magna aliqua.
									</Typography>
								</CardContent>
								<CardActions>
									<Button size="small">Learn More</Button>
								</CardActions>
							</Card>
						</Grid>
					))}
				</Grid>
			</Container>

			<Box
				component="footer"
				sx={{ bgcolor: 'background.paper', py: 6 }}>
				<Container maxWidth="lg">
					<Typography
						variant="body2"
						color="text.secondary"
						align="center">
						© 2023 My Landing Page. All rights reserved.
					</Typography>
				</Container>
			</Box>
		</>
	);
}
