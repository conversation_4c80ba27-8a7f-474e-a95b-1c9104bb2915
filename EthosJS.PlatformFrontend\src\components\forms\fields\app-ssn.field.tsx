import { useFieldContext } from '@hooks/form-context';
import { TextField } from '@mui/material';
import { maskSocialSecurity } from '@utils/maskers';
import { useState } from 'react';
import { FieldPropType } from './FieldPropType';

export default function AppSsnField({ label, required, dataTestId }: FieldPropType) {
	const field = useFieldContext<string>();

	const [isFocused, setIsFocused] = useState(false);

	// Format SSN to *********** pattern
	const formatSSN = (ssn: string): string => {
		// Remove all non-digit characters
		const digitsOnly = ssn.replace(/\D/g, '');

		// Limit to 9 digits
		const limitedDigits = digitsOnly.substring(0, 9);

		// Format with dashes
		if (limitedDigits.length <= 3) {
			return limitedDigits;
		} else if (limitedDigits.length <= 5) {
			return `${limitedDigits.substring(0, 3)}-${limitedDigits.substring(3)}`;
		} else {
			return `${limitedDigits.substring(0, 3)}-${limitedDigits.substring(3, 5)}-${limitedDigits.substring(5)}`;
		}
	};

	// Handle change with formatting
	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const formattedValue = formatSSN(e.target.value);
		field.handleChange(formattedValue);
	};

	const shouldShowError = field.getMeta().isTouched || field.getMeta().isDirty;
	const errors = shouldShowError ? field.getMeta().errors.map((error) => error.message) : [];

	return (
		<TextField
			label={label}
			required={required}
			onFocus={() => {
				setIsFocused(true);
			}}
			onBlur={() => {
				setIsFocused(false);
				field.handleBlur();
			}}
			onChange={handleChange}
			value={
				!isFocused && field.state.value ? maskSocialSecurity(field.state.value) : field.state.value
			}
			error={shouldShowError && errors.length > 0}
			slotProps={{
				inputLabel: {
					shrink: field.state.value != null && field.state.value !== '',
				},
				input: {
					inputProps: {
						'data-testid': dataTestId,
					},
				},
			}}
			helperText={errors.join(', ')}
			fullWidth
		/>
	);
}
