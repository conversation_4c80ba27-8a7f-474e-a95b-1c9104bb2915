import { ReferenceDataSetKeyValueDto } from '@client/refdata';
import { getApiReferenceSetsValuesOptions } from '@client/refdata/@tanstack/react-query.gen';
import FormCard from '@components/form-card';
import { SvgIconComponent } from '@mui/icons-material';
import {
	Stack,
	Typography,
	FormControlLabel,
	Checkbox,
	Box,
	CircularProgress,
} from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { filter } from 'lodash';

interface ClinicalConsiderationsListProps {
	title: string;
	icon: SvgIconComponent;
	setName: string;
	category: string;
	isItemSelected: (id: number) => boolean;
	onSelect: (selectedItem: number, isChecked: boolean) => void;
}

export default function ClinicalConsiderationsList({
	title,
	setName,
	icon,
	category,
	isItemSelected,
	onSelect,
}: ClinicalConsiderationsListProps) {
	const { data, isFetching } = useQuery(
		getApiReferenceSetsValuesOptions({
			responseType: 'json',
			query: {
				setName,
			},
		})
	);

	const clinicalInfoItems = (data as { items: ReferenceDataSetKeyValueDto[] })?.items ?? [];
	const options = filter(clinicalInfoItems, {
		values: { category: category },
	});

	return (
		<FormCard
			title={title}
			icon={icon}>
			{isFetching ? (
				<Box sx={{ py: 2, display: 'flex', justifyContent: 'center' }}>
					<CircularProgress />
				</Box>
			) : null}
			<Stack spacing={2}>
				{options.map((option) => {
					if (!option.values || !option.id) return null;
					const itemId = option.id;
					return (
						<Box
							key={option.id}
							sx={{
								display: 'flex',
								justifyContent: 'space-between',
								alignItems: 'center',
							}}>
							{/* @ts-expect-error Type Mismatch */}
							<Typography>{option.values.name}</Typography>
							<FormControlLabel
								control={
									<Checkbox
										checked={isItemSelected(itemId)}
										onChange={(e) => onSelect(itemId, e.target.checked)}
										data-testid={`clinicalConsiderations.medicalStatus.${options.values.name}`}
									/>
								}
								label="Yes"
								labelPlacement="end"
							/>
						</Box>
					);
				})}
			</Stack>
		</FormCard>
	);
}
