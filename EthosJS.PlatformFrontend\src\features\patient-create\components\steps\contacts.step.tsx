import { PatientState, StepPropsNew } from '../../types/state-types';
import usePatient from '@features/patient-create/hooks/use-patient';
import { EthosWorkflowsApiCreatePatientInputDto } from '@client/workflows';
import { useMemo, useState } from 'react';
import usePatientValidation from '@features/patient-create/hooks/use-patient-validation';
import ContactsForm from '@features/patient-create/forms/contacts.form';
import { getNewPatientState } from '@features/patient-create/utils';
import NotificationBanner from '@components/notification-banner';
import NotificationSnackbar, { NotificationState } from '@components/notification-snackbar';
import dayjs from 'dayjs';

export default function AddContactsStep({ patientId, successCallback }: StepPropsNew) {
	const { patientData, updatePatient, updatePatientError, resetUpdatePatientMutation, saveDraft } =
		usePatient({
			patientId,
		});

	const { validateFormatted } = usePatientValidation();
	const [notification, setNotification] = useState<NotificationState>({
		message: '',
		severity: 'info',
	});

	const resetToast = () => {
		setNotification({
			message: '',
			severity: 'info',
		});
	};

	const { data } = patientData ?? {};

	const patientState = (data?._state as unknown as PatientState) ?? {};
	const contactInformation = data?.contactInformation;
	const stepStatus = patientState.stepState?.Contacts ?? 'NotStarted';

	const savedData = useMemo(() => {
		return {
			contactInformation,
		} as EthosWorkflowsApiCreatePatientInputDto;
	}, [contactInformation]);

	return (
		<>
			<NotificationSnackbar
				notification={notification}
				onCloseToast={resetToast}
			/>
			<NotificationBanner
				message={updatePatientError?.message}
				severity={updatePatientError ? 'error' : 'success'}
				scrollIntoView
				onClose={resetUpdatePatientMutation}
			/>
			<ContactsForm
				savedData={savedData}
				onSubmit={(newData) => {
					updatePatient(
						{
							...data,
							...newData,
						},
						getNewPatientState(patientState, 'Contacts', 'Addresses'),
						successCallback
					);
				}}
				onSaveDraft={(newData) => {
					saveDraft(
						{
							...data,
							...newData,
						},
						{
							flowState: {
								...patientState.flowState,
								lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
							},
							stepState: {
								...patientState.stepState,
							},
						},
						() => {
							setNotification({
								message: 'Draft saved successfully',
								severity: 'success',
							});
						}
					);
				}}
				onValidate={validateFormatted}
				isUpdate={stepStatus === 'Complete'}
			/>
		</>
	);
}
