import { IPrimitiveQuery } from './core';

// Insurance primitive query types
export interface InsuranceQ extends IPrimitiveQuery {
  $type: 'WithId' | 'WithPatientId';
}

// Helper functions for InsuranceQ
export const InsuranceQuery = {
  withId: (id: number): InsuranceQ => ({
    $type: 'WithId',
    Id: id
  }),

  withPatientId: (patientId: string): InsuranceQ => ({
    $type: 'WithPatientId',
    PatientId: patientId
  })
};
