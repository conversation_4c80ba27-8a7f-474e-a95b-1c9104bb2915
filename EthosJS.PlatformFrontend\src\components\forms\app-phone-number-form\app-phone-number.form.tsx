import { useAppForm } from '@hooks/app-form';
import { Box, Checkbox, FormLabel, FormHelperText, FormControlLabel, Stack } from '@mui/material';
import { useStore } from '@tanstack/react-form';
import ContactMethodContainer from '@components/contact-method-container';
import MainCardContainer, {
	MainCardContainerProps,
} from '@components/main-container/main-card-container';
import { Phone } from 'lucide-react';
import { useMemo } from 'react';
import { ValidationErrors } from '@app-types/validation';
import { EthosWorkflowsApiPersonalPhoneNumberDto } from '@client/workflows';
import { defaultValues, phoneNumberFormOptions } from './utils';
import { formHasErrors, formHasValues } from '../utils';

interface PhoneNumberFormProps {
	onAdd?: (values: EthosWorkflowsApiPersonalPhoneNumberDto) => void;
	onCancel?: (hasValues: boolean) => void;
	onDelete?: () => void;
	formValues?: EthosWorkflowsApiPersonalPhoneNumberDto;
	onValidate: (
		data: EthosWorkflowsApiPersonalPhoneNumberDto
	) => Promise<ValidationErrors | undefined>;
	isUpdate?: boolean;
	containerProps?: MainCardContainerProps;
}

export default function PhoneNumberForm({
	onAdd,
	onCancel,
	onDelete,
	onValidate,
	formValues,
	isUpdate,
	containerProps = {},
}: PhoneNumberFormProps) {
	const options = useMemo(() => phoneNumberFormOptions(formValues), [formValues]);
	const hasValues = formHasValues(formValues, defaultValues);
	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(value);
				return res;
			},
		},
		defaultState: {
			isDirty: hasValues,
			isPristine: !hasValues,
		},
	});

	const { values, canSubmit, isDirty, fieldMeta } = useStore(
		form.store,
		({ values, canSubmit, isDirty, fieldMeta }) => ({
			values,
			canSubmit,
			isDirty,
			fieldMeta,
		})
	);
	const hasErrors = formHasErrors(fieldMeta);
	return (
		<MainCardContainer
			title="Add Phone Number"
			icon={<Phone size={24} />}
			color={hasErrors ? 'error' : 'primary'}
			emphasis={hasErrors || hasValues ? 'high' : 'low'}
			descriptionSubheader="* Indicates a required field"
			descriptionText="To contact the patient, choose the patient's preferred time."
			containerSlot={
				<ContactMethodContainer>
					<form.AppField name="isPreferred">
						{({ state, handleChange }) => (
							<Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
								<Checkbox
									checked={state.value}
									onChange={(e) => handleChange(e.target.checked)}
									data-testid="phoneNumber.isPreferred"
								/>
								<Box sx={{ display: 'flex', flexDirection: 'column', color: 'primary.dark' }}>
									<FormLabel sx={{ textTransform: 'uppercase', color: 'inherit' }}>
										Contact Method
									</FormLabel>
									<FormHelperText sx={{ color: 'inherit' }}>
										This phone number is the patient's preferred primary contact method.
									</FormHelperText>
								</Box>
							</Box>
						)}
					</form.AppField>
					<form.AppField name="allowsVoice">
						{({ state, handleChange }) => (
							<FormControlLabel
								sx={{ color: 'primary.dark' }}
								control={
									<Checkbox
										checked={(state.value as boolean) ?? false}
										onChange={(e) => handleChange(e.target.checked)}
										data-testid="phoneNumber.allowsVoice"
									/>
								}
								label="CALL"
							/>
						)}
					</form.AppField>
					<form.AppField name="allowsSms">
						{({ state, handleChange }) => (
							<FormControlLabel
								sx={{ color: 'primary.dark' }}
								control={
									<Checkbox
										checked={(state.value as boolean) ?? false}
										onChange={(e) => handleChange(e.target.checked)}
										data-testid="phoneNumber.allowsSMS"
									/>
								}
								label="SMS"
							/>
						)}
					</form.AppField>
				</ContactMethodContainer>
			}
			footerProps={{
				primaryButton1: {
					label: !hasValues && isUpdate ? 'Edit' : 'Add',
					onClick: () => onAdd?.(values),
					disabled: !canSubmit || !isDirty,
					'data-testid': 'phoneNumber.submitButton',
				},
				primaryButton2: {
					label: 'Cancel',
					onClick: () => {
						form.reset();
						onCancel?.(hasValues);
					},
					'data-testid': 'phoneNumber.cancelButton',
				},
				secondaryButton1: hasValues
					? {
							label: 'Delete',
							onClick: onDelete,
							color: 'error',
							'data-testid': 'phoneNumber.deleteButton',
						}
					: undefined,
			}}
			{...containerProps}>
			<Stack
				direction="row"
				columnGap={2}
				rowGap={2}
				flexWrap="wrap"
				sx={{ width: '100%' }}>
				<Box
					sx={{
						minWidth: '120px',
						maxWidth: '120px',
						flex: '0 1 15%',
					}}>
					<form.AppField name="type">
						{(field) => (
							<field.AppSelectField
								label="Type"
								referenceDataSetName="phoneUse"
								dataTestId="phoneNumber.type"
							/>
						)}
					</form.AppField>
				</Box>
				<Box
					sx={{
						minWidth: '200px',
						flex: '1 1 auto',
					}}>
					<form.AppField
						name="value"
						children={(field) => (
							<field.AppPhoneNumberField
								label="Phone Number"
								required
								dataTestId="phoneNumber.value"
							/>
						)}
					/>
				</Box>
				<Box
					sx={{
						minWidth: '180px',
						flex: '0 1 35%',
					}}>
					<form.AppField
						name="preferredTime"
						children={(field) => (
							<field.AppAutocompleteField
								label="Preferred Time"
								referenceDataSetName="preferredContactTime"
								dataTestId="phoneNumber.preferredTime"
							/>
						)}
					/>
				</Box>
			</Stack>
		</MainCardContainer>
	);
}
