import { EthosWorkflowsApiPatientGuardianDto } from '@client/workflows';

const defaultValues: EthosWorkflowsApiPatientGuardianDto = {
	id: null!,
	names: [
		{
			firstName: '',
			lastName: '',
			middleName: '',
			prefix: null,
			suffix: null,
		},
	],
	identifiers: [],
	demographics: {
		dateOfBirth: '',
		gender: null!,
		birthSex: null!,
		maritalStatus: null!,
		race: null!,
		ethnicity: null!,
	},
	contactInformation: {
		phoneNumbers: [],
		emails: [],
		addresses: [],
		emergencyContacts: [],
	},
	relationshipToPatient: null!,
};

function guardianFormOptions(savedData?: EthosWorkflowsApiPatientGuardianDto) {
	return {
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	};
}

export { defaultValues, guardianFormOptions };
