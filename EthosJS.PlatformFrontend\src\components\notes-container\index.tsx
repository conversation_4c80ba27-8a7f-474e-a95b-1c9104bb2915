import React, { useState } from 'react';
import { Stack } from '@mui/material';
import MainCardContainer from '@components/main-container/main-card-container';
import { NotesContainerProps } from './types';
import { NoteForm } from './note-form';
import { NoteItem } from './note-item';

export type { Note, NotesContainerProps, NoteItemProps, NoteFormProps } from './types';
export { NoteItem } from './note-item';
export { NoteForm } from './note-form';

const NotesContainer: React.FC<NotesContainerProps> = ({
  title,
  icon,
  children,
  onAddNote,
  onDeleteNote,
  allowAdd = true,
  allowDelete = true,
  footerProps,
  color = 'primary',
  descriptionSubheader,
  descriptionText
}) => {
  const [isAddMode, setIsAddMode] = useState(false);
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);

  const handleAddClick = () => {
    setIsAddMode(true);
    setEditingNoteId(null);
  };

  const handleSave = (content: string) => {
    onAddNote?.(content);
    setIsAddMode(false);
  };

  const handleCancel = () => {
    setIsAddMode(false);
  };

  const handleEditNote = (noteId: string) => {
    setEditingNoteId(noteId);
    setIsAddMode(false);
  };

  const handleEditCancel = () => {
    setEditingNoteId(null);
  };

  const enhancedChildren = React.Children.map(children, (child) => {
    if (React.isValidElement(child) && child.type === NoteItem) {
      const childProps = child.props as any;
      return React.cloneElement(child, {
        ...childProps,
        isEditing: editingNoteId === childProps.note?.id,
        onStartEdit: () => handleEditNote(childProps.note?.id),
        onCancelEdit: handleEditCancel,
        onDelete:
          allowDelete && onDeleteNote
            ? () => {
                onDeleteNote(childProps.note?.id);
                if (editingNoteId === childProps.note?.id) {
                  setEditingNoteId(null);
                }
              }
            : childProps.onDelete
      });
    }
    return child;
  });


  // Show only headers when no content and not in add/edit mode
  const showOnlyHeaders =
    !isAddMode &&
    !editingNoteId &&
    (!enhancedChildren || React.Children.count(enhancedChildren) === 0);

  // Filter children to show only the editing note when in edit mode
  const filteredChildren = editingNoteId
    ? React.Children.toArray(enhancedChildren).filter((child) => {
        if (React.isValidElement(child) && child.type === NoteItem) {
          const childProps = child.props as any;
          return childProps.note?.id === editingNoteId;
        }
        return false;
      })
    : enhancedChildren;

  // If in add mode, show only the add form
  if (isAddMode) {
    return (
      <NoteForm
        onSave={handleSave}
        onCancel={handleCancel}
      />
    );
  }

  // If in edit mode, show only the editing note

  if (editingNoteId) {
    return <>{filteredChildren}</>;
  }

  // Normal view - show the container with all notes
  return (
    <MainCardContainer
      title={title}
      icon={icon}
      emphasis={!showOnlyHeaders ? 'high' : 'low'}
      color={color}
      descriptionSubheader={descriptionSubheader}
      descriptionText={descriptionText}
      primaryActionType={allowAdd && !isAddMode && !editingNoteId ? 'Add' : 'none'}
      onPrimaryAction={handleAddClick}
      footerProps={footerProps}>
      {!showOnlyHeaders && <Stack gap={"20px"}>{enhancedChildren}</Stack>}
    </MainCardContainer>
  );
};

export default NotesContainer;
