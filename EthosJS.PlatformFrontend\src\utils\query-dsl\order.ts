import { IPrimitiveQuery } from './core';

// Order primitive query types
export interface OrderQ extends IPrimitiveQuery {
  $type: 'WithId' | 'WithPatientId' | 'WithStatus' | 'WithStudyType' | 'WithDateRange';
}

// Helper functions for OrderQ
export const OrderQuery = {
  withId: (id: string): OrderQ => ({
    $type: 'WithId',
    Id: id
  }),

  withPatientId: (patientId: string): OrderQ => ({
    $type: 'WithPatientId',
    PatientId: patientId
  }),

  withStatus: (status: string): OrderQ => ({
    $type: 'WithStatus',
    Status: status
  }),

  withStudyType: (studyType: string): OrderQ => ({
    $type: 'WithStudyType',
    StudyType: studyType
  }),

  withDateRange: (startDate: string, endDate: string): OrderQ => ({
    $type: 'WithDateRange',
    StartDate: startDate,
    EndDate: endDate
  })
};
