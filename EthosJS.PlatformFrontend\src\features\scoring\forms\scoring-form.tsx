import List from "@components/list";
import MainCardContainer from "@components/main-container/main-card-container";
import StepCardControl from "@components/step-card-control";
import { Button, Checkbox, Stack, Typography } from "@mui/material"
import { useForm } from "@tanstack/react-form";
import { Signature } from "lucide-react";

interface ScoringFormData {
   checklist: string[];
   isConfirmed: boolean;
}

interface ScoringFormProps {
   onSubmit: (values: ScoringFormData) => void
}

const ScoringForm = ({ onSubmit }: ScoringFormProps) => {

   const form = useForm({
      defaultValues: {
         checklist: [],
         isConfirmed: false,
      } as ScoringFormData,
      validators: {
         onChange: ({ value: { isConfirmed } }) => {
            return isConfirmed ? undefined : 'Please confirm'
         },
      },
      onSubmit: ({ value }) => onSubmit(value)
   })

   const options = [
      { value: '1', title: 'Sufficient recording time (>6 hours)' },
      { value: '2', title: 'Signal quality acceptable' },
      { value: '3', title: 'All required electrodes present' },
      { value: '4', title: 'Calibration signals verified' },
      { value: '4.1', title: 'Sleep staging completed' },
      { value: '5', title: 'Respiratory events scored' },
      { value: '6', title: 'Arrhythmia review completed' },
      { value: '7', title: 'Movement events scored' },
      { value: '8', title: 'Report exported successfully' },
   ];

   return (
      <Stack
         gap={2}
         component={'form'}
         onSubmit={(e: React.FormEvent) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
         }}
      >
         <form.Field
            name="checklist"
            children={(field) => {
               const value = field.state.value;
               return (
                  <MainCardContainer
                     title="Quality Checklist"
                     customAction={(
                        <Button sx={() => {
                           return {
                              bgcolor: 'white',
                              color: 'black',
                              borderRadius: 2
                           }
                        }}>
                           {`${value.length} of ${options.length} items completed`}
                        </Button>
                     )}
                     emphasis="high"
                  >
                     <List
                        onSelectItems={(e) => field.handleChange(e as unknown as string[])}
                        selectedItems={value}
                        items={options}
                        selectable
                        selectMode="checkbox"
                     />
                  </MainCardContainer>
               )
            }}
         />
         <form.Field
            name="isConfirmed"
            children={(field) => {
               return (
                  <MainCardContainer title="Confirmation Scoring" icon={<Signature />}>
                     <Stack sx={{ justifyContent: 'space-between', alignItems: 'center' }} direction={'row'}>
                        <Stack sx={{ alignItems: 'center' }} direction={'row'} gap={1}>
                           <Signature />
                           <Typography>I confirm that scoring is complete and study is ready for interpretation</Typography>
                        </Stack>
                        <Checkbox
                           checked={field.state.value}
                           onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                              field.handleChange(e.target.checked);
                           }}
                        />
                     </Stack>
                  </MainCardContainer>
               )
            }}
         />
         <StepCardControl>
            <form.Subscribe
               selector={({ isDirty, canSubmit, isSubmitting }) => ({
                  isDirty,
                  canSubmit,
                  isSubmitting,
               })}>
               {({ isDirty, canSubmit, isSubmitting }) => {
                  return (
                     <Button
                        variant="contained"
                        color="primary"
                        type="submit"
                        loading={isSubmitting}
                        disabled={!isDirty || (!canSubmit)}>
                        {'Complete Scoring'}
                     </Button>
                  );
               }}
            </form.Subscribe>
         </StepCardControl>
      </Stack>
   )
}

export default ScoringForm;