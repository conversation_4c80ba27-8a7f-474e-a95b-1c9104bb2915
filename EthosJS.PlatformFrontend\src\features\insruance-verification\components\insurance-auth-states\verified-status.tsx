import MainCardContainer from '@components/main-container/main-card-container';
import { Chip } from '@mui/material';
import { ShieldCheck } from 'lucide-react';

export default function VerifiedStatus() {
	return (
		<MainCardContainer
			title="Insurance Verification"
			headerSize="medium"
			icon={<ShieldCheck />}
			color="success"
			emphasis="high"
			descriptionSubheader="What is Next? Prior authorization is required and is being processed."
			customAction={
				<Chip
					label="Verified"
					sx={{ borderRadius: 2, color: 'success.dark', backgroundColor: 'white' }}
				/>
			}
		/>
	);
}
