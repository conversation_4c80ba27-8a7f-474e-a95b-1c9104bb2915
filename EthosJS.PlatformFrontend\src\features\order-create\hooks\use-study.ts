import { PatientCreate, PatientRead } from '@auth/scopes';
import {
	EthosWorkflowsApiCreateStudyDto,
	EthosWorkflowsApiDraftDto,
	EthosWorkflowsApiStudyDto,
	SystemTextJsonNodesJsonNode,
} from '@client/workflows';
import {
	getApiStudyDraftByEntityIdOptions,
	postApiStudyDraftByEntityIdCommitMutation,
	putApiStudyDraftByEntityIdMutation,
} from '@client/workflows/@tanstack/react-query.gen';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';

export default function useStudy({ studyId }: { studyId: string | undefined }) {
	const queryClient = useQueryClient();

	const queryKey = getApiStudyDraftByEntityIdOptions({
		path: { entityId: studyId! },
		scopes: [PatientCreate.value, PatientRead.value],
		responseType: 'json',
	});

	const {
		data: studyData,
		isFetching: isFetchingStudyData,
		error: fetchStudyError,
	} = useQuery({
		...queryKey,
		enabled: !!studyId,
	});

	const {
		mutate: updateStudyMutation,
		isPending: isUpdatingStudy,
		error: updateStudyError,
		reset: resetUpdateStudyMutation,
	} = useMutation({
		...putApiStudyDraftByEntityIdMutation({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		}),
		onError(error) {
			console.error(error);
		},
		onSuccess(mutationData) {
			queryClient.setQueryData([queryKey], mutationData);
		},
	});

	const updateStudy = useCallback(
		async (
			data: Partial<EthosWorkflowsApiCreateStudyDto>,
			successCallback?: (studyId: string) => void
		) => {
			updateStudyMutation(
				{
					path: { entityId: studyId! },
					body: data as unknown as { [key: string]: SystemTextJsonNodesJsonNode },
				},
				{
					onSuccess(mutationData) {
						const { entityId } = mutationData;
						if (successCallback && entityId) {
							successCallback(entityId);
						}
					},
				}
			);
		},
		[updateStudyMutation, studyId]
	);

	const {
		mutate: commitDraftMutation,
		isPending: isCommittingDraft,
		error: commitDraftError,
	} = useMutation({
		...postApiStudyDraftByEntityIdCommitMutation({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		}),
		onError(error) {
			console.error(error);
		},
		onSuccess(mutationData) {
			queryClient.setQueryData([queryKey], mutationData);
		},
	});

	const commitStudyDraft = useCallback(
		(studyId: string, successCallback?: (studyId: string) => void) => {
			commitDraftMutation(
				{
					path: { entityId: studyId },
				},
				{
					onSuccess(mutationData) {
						const { id } = mutationData;
						if (successCallback && id) {
							successCallback(id);
						}
					},
				}
			);
		},
		[commitDraftMutation]
	);

	return {
		studyData: studyData as EthosWorkflowsApiDraftDto & { data: EthosWorkflowsApiStudyDto },
		isFetchingStudyData,
		fetchStudyError,
		updateStudy,
		isUpdatingStudy,
		updateStudyError,
		resetUpdateStudyMutation,
		commitStudyDraft,
		isCommittingDraft,
		commitDraftError,
	};
}
