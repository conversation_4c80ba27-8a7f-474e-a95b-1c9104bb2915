import React from 'react';
import { Box, Typography, Stack } from '@mui/material';
import MainCardContainer from '@components/main-container/main-card-container';
import { FileText } from 'lucide-react';
import { FileItemProps, FileFormData } from './types';
import { FileUploadForm } from './file-upload-form';

export const FileItem: React.FC<FileItemProps & { containerTitle?: string }> = ({
  file,
  allowEdit = true,
  onEdit,
  onDelete,
  size = 'medium',
  footerProps,
  icon,
  isEditing = false,
  onStartEdit,
  onCancelEdit,
  onSave,
  workflowId,
  containerTitle
}) => {
  const handlePrimaryAction = () => {
    if (!isEditing && allowEdit) {
      onStartEdit?.();
    }
  };

  const handleSave = (fileData: FileFormData) => {
    onSave?.(fileData);
  };

  const handleCancel = () => {
    onCancelEdit?.();
  };

  const handleDeleteClick = () => {
    onDelete?.();
  };

  if (isEditing) {
    return (
      <FileUploadForm
        title={containerTitle || file.title}
        icon={icon}
        onSave={handleSave}
        onCancel={handleCancel}
        workflowId={workflowId || ''}
        initialData={{
          category: file.category || '',
          documentType: file.documentType || file.title,
          issueDate: file.issueDate || '',
          expiryDate: file.expiryDate || '',
          fileId: file.fileId || '',
          fileName: file.fileName
        }}
        onDelete={onDelete}
        showUploadedFile={true}
      />
    );
  }

  return (
    <MainCardContainer
      title={file.title}
      icon={icon || <FileText />}
      emphasis="low"
      color="primary"
      headerSize={size}
      primaryActionType={allowEdit ? 'Edit' : 'none'}
      onPrimaryAction={handlePrimaryAction}
      descriptionSubheader={file.category}
      descriptionText={file.issueDate && file.expiryDate ? `Issue: ${file.issueDate} • Expiry: ${file.expiryDate}` : undefined}
      footerProps={{
        ...footerProps,
        ...(onDelete && {
          secondaryButton1: {
            label: 'Delete',
            onClick: handleDeleteClick
          }
        })
      }}>
      <Box sx={{ p: 1 }}>
        <Stack spacing={1}>
          <Typography variant="body1" fontWeight="medium">
            {file.fileName}
          </Typography>
          {file.fileSize && file.status && (
            <Typography variant="body2" color="text.secondary">
              {file.fileSize} • {file.status === 'completed' ? 'Upload completed' : file.status}
            </Typography>
          )}
          <Typography variant="body2" color="text.secondary">
            Uploaded by {file.uploadedBy} • {file.uploadedTime}
          </Typography>
        </Stack>
      </Box>
    </MainCardContainer>
  );
};
