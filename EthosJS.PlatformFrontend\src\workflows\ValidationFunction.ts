import { pascalToSpacedWords } from '@utils/generator-helper';
import { IValidationRule, ValidationType, ITypeAnnotation, createValidationRule } from './TypeDef';
import { IExpr } from './expressions';
import { evaluateExpr } from './expressions';

export interface ValidationError {
  type: ValidationType;
  message: string;
}

export class ValidationFunction {
  private rules: IValidationRule<IExpr<ITypeAnnotation>>[];

  constructor(rules: IValidationRule<IExpr<ITypeAnnotation>>[]) {
    // Ensure all rules have toJSON method
    this.rules = rules.map((rule) => ('toJSON' in rule ? rule : createValidationRule(rule)));
  }

  toJSON() {
    return this.rules.map((rule) => rule.toJSON());
  }

  validate(value: any): ValidationError[] {
    const context = {
      value
      // Add any additional context needed for expression evaluation
    };

    return this.rules
      .filter((rule) => {
        try {
          // Evaluate the rule's requirement expression
          const isValid = evaluateExpr(rule.require, context);
          // If the expression evaluates to false, this rule has been violated
          return !isValid;
        } catch (error) {
          console.error(`Error evaluating validation rule ${rule.name}:`, error);
          // If there's an error evaluating the rule, consider it a violation
          return true;
        }
      })
      .map((rule) => {
        const asJson = rule.toJSON();
        return {
          type: asJson.level === 'error' ? ValidationType.ERROR : ValidationType.WARNING,
          message: pascalToSpacedWords(asJson.name ?? 'Unknown validation violation')
        };
      });
  }

  /**
   * Factory method to create a validation function from validation rules
   */
  static create(rules: IValidationRule<IExpr<ITypeAnnotation>>[]): ValidationFunction {
    return new ValidationFunction(rules);
  }

  /**
   * Convenience method to validate a single value
   */
  static validate(rules: IValidationRule<IExpr<ITypeAnnotation>>[], value: any): ValidationError[] {
    return new ValidationFunction(rules).validate(value);
  }
}
