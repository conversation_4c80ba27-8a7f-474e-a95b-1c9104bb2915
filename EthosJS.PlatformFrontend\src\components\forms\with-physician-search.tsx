import { ComponentType, useEffect, useMemo, useState } from 'react';
import { AppPhysicianFormProps } from './app-physician-form/app-physician.form';
import { useQuery } from '@tanstack/react-query';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { debounce } from '@mui/material';
import { PhysicianQuery, Query } from '@utils/query-dsl';

import { postApiPhysicianSearchOptions } from '@client/workflows/@tanstack/react-query.gen';

export type PhysicianResponse = {
	id: string;
	name: {
		family: string;
		given: string[];
	};
	npi: string;
};

export function withPhysicianSearch<T extends AppPhysicianFormProps>(
	WrappedComponent: ComponentType<T>
) {
	return function WithPhysicianFormWrapper(props: T) {
		const [searchTerm, setSearchTerm] = useState<string>('');
		const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>('');

		const { data, isFetching, error } = useQuery({
			...postApiPhysicianSearchOptions({
				scopes: [PatientCreate.value, PatientRead.value],
				responseType: 'json',
				body: debouncedSearchTerm
					? (Query.or([
							Query.literal(PhysicianQuery.withFirstName(debouncedSearchTerm)),
							Query.literal(PhysicianQuery.withLastName(debouncedSearchTerm)),
						]) as any)
					: undefined,
			}),
		});

		const debouncedSearch = useMemo(
			() =>
				debounce((searchTerm: string) => {
					setDebouncedSearchTerm(searchTerm);
				}, 300),
			[]
		);

		useEffect(() => {
			if (searchTerm !== debouncedSearchTerm) {
				debouncedSearch(searchTerm);
			}
		}, [searchTerm]);

		const wrappedProps = {
			...props,
			options:
				data?.items?.map((item) => ({
					title: `${item.names?.[0]?.firstName ?? ''} ${item.names?.[0]?.lastName ?? ''}`,
					id: item.id ?? '',
					description: `${item.names?.[0]?.firstName ?? ''} ${item.names?.[0]?.lastName ?? ''}`,
					meta: {
						npi: item.id ?? '',
						name: `${item.names?.[0]?.firstName ?? ''} ${item.names?.[0]?.lastName ?? ''}`,
						category: `${item.names?.[0]?.firstName ?? ''} ${item.names?.[0]?.lastName ?? ''}`,
					},
				})) ?? [],
			searchTerm,
			onSearchChange: setSearchTerm,
			searchLoading: isFetching,
		} as T;

		return <WrappedComponent {...wrappedProps} />;
	};
}
