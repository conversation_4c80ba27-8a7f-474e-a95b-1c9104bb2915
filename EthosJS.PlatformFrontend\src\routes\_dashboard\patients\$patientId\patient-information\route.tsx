import Card from '@components/card';
import PatientCreateMenu from '@features/patient-create/components/menu';
import { Box } from '@mui/material';
import { createFileRoute, Outlet, redirect, useLocation } from '@tanstack/react-router';

export const Route = createFileRoute('/_dashboard/patients/$patientId/patient-information')({
	component: RouteComponent,
	beforeLoad: ({ params, location }) => {
		if (location.pathname === `/patients/${params.patientId}/patient-information`) {
			throw redirect({
				to: '/patients/$patientId/patient-information/basic-information',
				params: { patientId: params.patientId },
			});
		}
	},
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const navigate = Route.useNavigate();
	const { pathname } = useLocation();

	return (
		<Card
			sx={{
				flex: 1,
				minHeight: 0,
				display: 'flex',
				gap: 2,
				p: 2,
				pb: 0,
				borderRadius: 2,
				borderBottomLeftRadius: 0,
				borderBottomRightRadius: 0,
			}}>
			<PatientCreateMenu
				patientId={patientId}
				activePath={pathname}
				onClick={(path) => {
					navigate({
						to: `/patients/$patientId/patient-information${path}`,
						params: { patientId },
					});
				}}
			/>
			<Card
				color="primary"
				sx={{
					flex: 1,
					minHeight: 0,
					position: 'relative',
					height: '100%',
					borderBottomLeftRadius: 0,
					borderBottomRightRadius: 0,
				}}>
				<Box
					sx={{
						flex: 1,
						overflow: 'auto',
						height: 'calc(100% - 67px)',
						pb: 0,
					}}>
					<Outlet />
				</Box>
			</Card>
		</Card>
	);
}
