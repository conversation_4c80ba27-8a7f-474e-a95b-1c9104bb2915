import MenuItem, { IMenuItem } from '@components/global-navigation/menu-item';
import { Box, IconButton, Stack, Typography, useTheme } from '@mui/material';
import { ReactNode } from '@tanstack/react-router';
import { ChevronRight, Menu } from 'lucide-react';
import { useState } from 'react';
import Favicon from "../../../../public/favicon.svg"

interface ISidebar {
  header?: {
    logo?: {
      collapsed: {
        src: string
      };
      expanded: {
        src: string
      };
    };
  };

  collapsed?: boolean;
  footer?: {
    collapsed: ReactNode;
    expanded: ReactNode;
  };
  menuProps: {
    items: IMenuItem[];
  };
  brandingLabelProps: IBrandingLabelProps
}

const getRootStyles = (collapsed: boolean) => {
  let defaultStyles = {
    height: '100%',
    maxWidth: '17.8125rem',
    border: '1px solid',
    borderColor: 'divider',
    gap: 2
  };

  if (collapsed) {
    return {
      ...defaultStyles,
      width: '5.25rem'
    };
  }

  return {
    ...defaultStyles
  };
};

const SideBar = ({ footer, header, menuProps, collapsed: collapsedProp, brandingLabelProps }: ISidebar) => {
  const menuItems = menuProps?.items ?? [];

  const [collapsedLocal, setCollapsedLocal] = useState<boolean>(false);

  const collapased = collapsedProp ?? collapsedLocal;

  const onToggleCollapse = () => {
    setCollapsedLocal(!collapased);
  };

  return (
    <Box sx={getRootStyles(collapased)} component={Stack}>
      <Stack flexDirection="row" justifyContent="space-between" alignItems='center' maxHeight='80px' paddingBottom={0} paddingLeft={1.5} paddingRight={1.5}  >
        {
          collapased ? <img src={header?.logo?.collapsed.src} width='100%' height='100%' /> : <img src={header?.logo?.expanded.src} width='75%' height='50%' />
        }
        {!collapased && (
          <IconButton onClick={onToggleCollapse} >
            <Menu />
          </IconButton>
        )}
      </Stack>
      <Stack
        gap={1}
        flex={1}
        sx={{
          overflow: 'auto',
          overflowX: 'hidden',
          overflowY: 'auto',
          '&::-webkit-scrollbar': {
            display: 'none'
          }
        }}>
        {menuItems.map((item, index) => {
          return (
            <Box
              key={index}
              sx={{
                pl: collapased ? 2 : 2,
                pr: collapased ? 2 : 2,
              }}
            >
              <MenuItem collapsed={collapased} {...item} />
            </Box>
          );
        })}

      </Stack>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          gap: 2,
          pl: collapased ? 2 : 2,
          pr: collapased ? 2 : 2,
        }}>
        {footer && (collapased ? footer.collapsed : footer.expanded)}
        {collapased && (
          <IconButton onClick={onToggleCollapse}>
            <ChevronRight />
          </IconButton>
        )}
        <Box
          sx={() => {
            return {
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              gap: 1,
              marginBottom: 2,
              padding: 1
            };
          }}>
          {!collapased && <BrandingLabel {...brandingLabelProps} />}
        </Box>
      </Box>
    </Box>
  );
};

interface IBrandingLabelProps {
  label: string
}

const BrandingLabel = ({ label }: IBrandingLabelProps) => {
  const { palette } = useTheme();
  return (
    <>
      <img src={Favicon} />
      <Typography color={palette.primary.dark} variant='body1' >{label}</Typography>
    </>
  )
}

export default SideBar;
