import { postApiStudySearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import { Autocomplete, TextField } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { QueryDto, StudyQ } from '@utils/query-dsl';
import { find } from 'lodash';

interface StudySelectSearchProps {
	studyId?: string;
	studyQuery: QueryDto<StudyQ>;
	onChange: (studyId: string | null) => void;
}

export default function StudySelectSearch({
	studyId,
	studyQuery,
	onChange,
}: StudySelectSearchProps) {
	const { data: studies } = useQuery({
		...postApiStudySearchOptions({
			responseType: 'json',
			//body: studyQuery,
		}),
		enabled: !!studyQuery,
		initialData: {
			items: [],
		},
		select: (data) => data?.items ?? [],
	});

	const selectedStudy = find(studies, { id: studyId }) ?? null;

	return (
		<Autocomplete
			sx={{ minWidth: 200 }}
			value={selectedStudy ?? null}
			onChange={(_, newValue) => {
				onChange(newValue?.id ?? null);
			}}
			renderInput={(params) => (
				<TextField
					{...params}
					label="Study Id"
				/>
			)}
			options={studies}
			getOptionLabel={(option) => option.id}
			filterOptions={(x) => x}
		/>
	);
}
