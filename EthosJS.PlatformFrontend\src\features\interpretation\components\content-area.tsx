import { Stack } from "@mui/material"
import * as containers from "./containers";
import Card from "@components/card";
import InterpretForm from "../forms/interpret-form";

const { StudySummary, PatientChartDocuments, TechnicalNotes, InterpretNotesObservation, FinalNotesPhysicians } = containers;

const InterpretContentArea = () => {
   return (
      <Card>
         <Stack gap={1}>
            <StudySummary />
            <PatientChartDocuments />
            <TechnicalNotes />
            <InterpretNotesObservation />
            {/* <FinalNotesPhysicians /> */}
            <InterpretForm
               onSubmit={() => { }}
            />
         </Stack>
      </Card>
   )
}

export default InterpretContentArea;