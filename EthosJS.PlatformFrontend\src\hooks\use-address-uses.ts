import { GetApiReferenceSetsResponse, ReferenceDataSetKeyValueDto } from '@client/refdata';
import { getApiReferenceSetsValuesOptions } from '@client/refdata/@tanstack/react-query.gen';
import { useQuery } from '@tanstack/react-query';

type RefDataResponse = Omit<GetApiReferenceSetsResponse, 'items'> & {
	items: ReferenceDataSetKeyValueDto[];
};

export function useAddressUses() {
	const {
		data: addressUses,
		isFetching,
		error,
	} = useQuery({
		...getApiReferenceSetsValuesOptions({
			responseType: 'json',
			query: {
				setName: 'addressUse',
			},
		}),
		select: (data) => {
			if (!data) return [];
			const { items } = data as RefDataResponse;
			return items;
		},
	});

	return {
		addressUses,
		isFetching,
		error,
	};
}
