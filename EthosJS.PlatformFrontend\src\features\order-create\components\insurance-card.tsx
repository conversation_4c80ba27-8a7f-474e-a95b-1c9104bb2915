import { getApiInsuranceByIdOptions } from '@client/workflows/@tanstack/react-query.gen';
import MainCardContainer from '@components/main-container/main-card-container';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { useRefDataValue } from '../../../hooks/use-ref-data-value';
import ChipSummary from '@components/chip-summary';
import { Button, Stack } from '@mui/material';
import { filter } from 'lodash';
import { ShieldUser } from 'lucide-react';

const TABS = {
	BASIC: 'Basic Details',
	CONTACT: 'Contact',
	ADDRESS: 'Address',
} as const;

type Tab = (typeof TABS)[keyof typeof TABS];

interface InsuranceCardProps {
	insuranceId: string;
	slot: number;
}

export default function InsuranceCard({ insuranceId, slot }: InsuranceCardProps) {
	const [activeTab, setActiveTab] = useState<Tab>(TABS.BASIC);
	const {
		data: insuranceData,
		isFetching: isFetchingInsuranceData,
		error: fetchInsuranceError,
	} = useQuery({
		...getApiInsuranceByIdOptions({
			path: { id: insuranceId! },
			responseType: 'json',
		}),
		enabled: !!insuranceId,
	});

	const {
		insuranceCarrier,
		insuranceId: insuranceNumber,
		policyId,
		groupNumber,
		memberId,
	} = insuranceData ?? {};

	const { value: insuranceCarrierData, isFetching: isFetchingInsuranceCarrierData } =
		useRefDataValue({
			id: insuranceCarrier as number | null,
		});

	if (isFetchingInsuranceData || isFetchingInsuranceCarrierData) {
		return <div>Loading...</div>;
	}

	if (fetchInsuranceError) {
		return <div>Error fetching insurance data</div>;
	}

	return (
		<MainCardContainer
			icon={<ShieldUser />}
			headerSize="small"
			title={insuranceCarrierData?.title ?? 'N/A'}
			emphasis="low"
			color="gray"
			secondaryActionType="Chips"
			chipLabels={[
				{ label: slot === 1 ? 'Primary' : slot === 2 ? 'Secondary' : 'Tertiary', icon: undefined },
			]}>
			<>
				<Stack
					direction="row"
					spacing={2}
					mb={2}>
					{Object.values(TABS).map((tab) => (
						<Button
							key={tab}
							onClick={() => setActiveTab(tab)}
							variant={activeTab === tab ? 'contained' : 'outlined'}>
							{tab}
						</Button>
					))}
				</Stack>
				{activeTab === TABS.BASIC && (
					<ChipSummary
						variant="outlined"
						items={filter(
							[
								memberId && {
									label: 'Member ID',
									value: `#${memberId}`,
								},
								policyId && {
									label: 'Policy ID',
									value: `#${policyId}`,
								},
								groupNumber && {
									label: 'Group Number',
									value: `#${groupNumber}`,
								},
								// planType && {
								// 	label: 'Plan Type',
								// 	value: `#${groupNumber}`,
								// },
								insuranceNumber && {
									label: 'Insurance ID',
									value: insuranceNumber,
								},
							],
							(item) => item !== null && item !== undefined && item !== ''
						)}
					/>
				)}
				{activeTab === TABS.CONTACT && (
					<ChipSummary
						variant="outlined"
						items={[
							// phone && {
							// 	label: 'Phone',
							// 	value: phone,
							// },
							// fax && {
							// 	label: 'Fax',
							// 	value: fax,
							// },
							// email && {
							// 	label: 'E-Mail',
							// 	value: email,
							// },
						].filter(Boolean)}
					/>
				)}
				{activeTab === TABS.ADDRESS && (
					<ChipSummary
						variant="outlined"
						items={[
							// po && {
							// 	label: 'PO Box Address',
							// 	value: po,
							// },
						].filter(Boolean)}
					/>
				)}
			</>
		</MainCardContainer>
	);
}
