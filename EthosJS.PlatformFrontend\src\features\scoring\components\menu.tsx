import SecondaryMenu from '@components/menu/secondary-menu';
import { Calendar} from 'lucide-react';
import SelectionControls from './selection-controls';

interface ScoringMenuProps {
	patientId: string;
	orderId: string;
	studyId: string;
	activePath: string;
	onClick: (path: string) => void;
	onSelect: (selectedIds: { orderId: string; studyId?: string }) => void;
}

export default function ScoringMenu({
	patientId,
	orderId,
	studyId,
	onSelect,
}: ScoringMenuProps) {
	return (
		<SecondaryMenu
			headerProps={{
				title: 'Scoring',
				subtitle: undefined,
				icon: Calendar,
				showIcon: true,
				type: 'Workflow',
				progress: 0,
				color: 'success',
				description: 'Select Order ID, Study and Visit Date',
			}}
			topContainer={
				<SelectionControls
					studyId={studyId}
					patientId={patientId}
					orderId={orderId}
					onSelect={onSelect}
				/>
			}>
				<p></p>
		</SecondaryMenu>
	);
}
