import { createFileRoute } from '@tanstack/react-router';

import SchedulePreferencesStep from '@features/patient-create/components/steps/schedule-preferences';
import useOrderCreate from '@features/order-create/hooks/use-order-create';
import useStudyCreate from '@features/order-create/hooks/use-study-create';
import dayjs from 'dayjs';
export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/patient-information/schedule-preferences'
)({
	component: RouteComponent,
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const navigate = Route.useNavigate();

	const { createOrder } = useOrderCreate();
	const { createStudy } = useStudyCreate();

	const createStudyAndNavigate = (orderId: string) => {
		createStudy(
			{
				orderId,
			},
			(studyId) => {
				navigate({
					to: '/patients/$patientId/order/study',
					params: { patientId },
					search: { orderId, studyId },
				});
			}
		);
	};

	const createOrderAndStudy = () => {
		createOrder(
			{
				patientId,
			},
			{
				flowState: {
					progress: 0,
					status: 'InProgress',
					lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
				},
				stepState: {
					AddStudy: 'InProgress',
					AddCareLocation: 'NotStarted',
					AddPhysicians: 'NotStarted',
					ReviewAndSubmitOrder: 'NotStarted',
				},
			},
			createStudyAndNavigate
		);
	};

	return (
		<SchedulePreferencesStep
			patientId={patientId}
			successCallback={createOrderAndStudy}
		/>
	);
}
