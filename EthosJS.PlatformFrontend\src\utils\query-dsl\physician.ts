import { IPrimitiveQuery } from './core';

// Example primitive query types (to be defined in separate files)
export interface PhysicianQ extends IPrimitiveQuery {
	$type:
		| 'WithId'
		| 'WithFirstName'
		| 'WithLastName'
		| 'WithDateOfBirth'
		| 'WithApproximateName'
		| 'WithCareLocationId';
}
// Helper functions for PatientQ
export const PhysicianQuery = {
	withId: (id: string): PhysicianQ => ({
		$type: 'WithId',
		Id: id,
	}),

	withFirstName: (firstName: string): PhysicianQ => ({
		$type: 'WithFirstName',
		FirstName: firstName,
	}),

	withLastName: (lastName: string): PhysicianQ => ({
		$type: 'WithLastName',
		LastName: lastName,
	}),

	withDateOfBirth: (dob: string): PhysicianQ => ({
		$type: 'WithDateOfBirth',
		Dob: dob,
	}),
	withApproximateName: (approxName: string): PhysicianQ => ({
		$type: 'WithApproximateName',
		ApproxName: approxName,
	}),
	withCareLocationId: (careLocationId: string): PhysicianQ => ({
		$type: 'WithCareLocationId',
		CareLocationId: careLocationId,
	}),
};
