import { styled, Box, Typography } from '@mui/material';
import { ComponentType } from 'react';

const StyledFooter = styled(Box)(({ theme }) => ({
	gap: theme.spacing(1.2),
	padding: theme.spacing(1.75) + ' ' + theme.spacing(1.5),
	display: 'flex',
	alignItems: 'center',
	justifyContent: 'center',
	color: theme.palette.text.secondary,
	borderTop: `1px solid ${theme.palette.grey[200]}`,
}));

export interface MenuFooterProps {
	icon?: ComponentType<React.SVGProps<SVGSVGElement>>;
	text?: string;
}

export default function MenuFooter({ icon: Icon, text }: MenuFooterProps) {
	return (
		<StyledFooter>
			{Icon && (
				<Icon
					style={{
						width: '1.125rem',
						height: '1.125rem',
						flexShrink: 0,
						color: 'inherit',
					}}
				/>
			)}
			<Typography
				variant="body2"
				sx={{ fontWeight: 600, color: 'inherit' }}>
				{text}
			</Typography>
		</StyledFooter>
	);
}
