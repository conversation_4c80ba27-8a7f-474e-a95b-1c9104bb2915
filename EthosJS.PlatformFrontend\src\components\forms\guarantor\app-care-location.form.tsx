import { <PERSON><PERSON><PERSON><PERSON>, Stack, TextField, FormControlLabel, Radio, InputAdornment } from '@mui/material'

import Card from '@components/card'
import CardInfo from '@components/card-info'
import ChipSummary from '@components/chip-summary';

import { Search } from 'lucide-react';

import CardHeader from '@components/card-header';

const centers = [
    {
        id: '1',
        label: 'Tukwila Sleep Center',
        value: 'Tukwila Sleep Center',
        data: [
            [
                { value: '(555) 123-4567' },
                { value: '12500 Tukwila International Blvd, Seattle, WA 98168 · (2.5 miles)' }
            ],
            [
                { value: 'Next Available: 2 days' },
                { value: 'Mon-Fri: 8AM–5PM, Study Hours: 8PM–6AM' }
            ],
            [
                { value: 'Adult Sleep Studies' },
                { value: 'Home Sleep Testing' },
                { value: 'Pediatric Sleep Studies' },
                { value: 'Home Sleep Testing' }
            ]
        ]
    },
    {
        id: '2',
        label: 'South East Center',
        data: [
            [
                { value: '(555) 111-2222' },
                { value: '789 North East St, Bellevue, WA 98004 · (4.3 miles)' }
            ],
            [
                { value: 'Next Available: 1 day' },
                { value: 'Mon-Fri: 9AM–6PM, Study Hours: 9PM–7AM' }
            ],
            [
                { value: 'Adult Sleep Studies' },
                { value: 'Home Sleep Testing' }
            ]
        ]
    },
    {
        id: '2',
        label: 'South East Center',
        data: [
            [
                { value: '(555) 111-2222' },
                { value: '789 North East St, Bellevue, WA 98004 · (4.3 miles)' }
            ],
            [
                { value: 'Next Available: 1 day' },
                { value: 'Mon-Fri: 9AM–6PM, Study Hours: 9PM–7AM' }
            ],
            [
                { value: 'Adult Sleep Studies' },
                { value: 'Home Sleep Testing' }
            ]
        ]
    }
];

const CareLocationForm = () => {

    return (
        <Card>
    
            <Card elevation={0}>
                <CardInfo title="Care Location" />
                <CardInfo title='Select the care location where the study will be performed.'
                    subtitle='The physician selection will be based on your location choice.'
                />
            </Card>
            <CardContent>

                <Stack flexDirection='row' gap={4} >
                    <TextField
                        label="All Types"
                        select
                        sx={{ width: '20%' }}
                    />

                    <TextField
                        variant="outlined"
                        sx={{ width: '78%' }}
                        defaultValue='Search locations by name or address..'
                        select
                        InputProps={{
                            startAdornment: (
                                <InputAdornment position="start">
                                    <Search />
                                </InputAdornment>
                            ),
                        }}
                    />
                </Stack>

                {Array.isArray(centers) && centers?.map((item, index) => {

                    const { label: title, data } = item;

                    return (
                        <Card sx={{ mt: 2 }} key={index.toString()}>
                            <CardHeader
                                {...{
                                    title,
                                    action: (
                                        <FormControlLabel
                                            control={<Radio />}
                                            label=""
                                        />
                                    )
                                }}
                            />
                            <CardContent>
                                <Stack gap={1}>
                                    {data.map((d, dIndex) => {
                                        return (
                                            <ChipSummary
                                                key={dIndex.toString()}
                                                items={(
                                                    d.map(({ ...item }) => ({
                                                        label: '',
                                                        value: item.value,
                                                    }))
                                                )}
                                                hideSeperator
                                            />
                                        )
                                    })}
                                </Stack>
                            </CardContent>
                        </Card>
                    )
                })}

            </CardContent>
        </Card>
    )
}

export default CareLocationForm
