import { Store, useStore } from '@tanstack/react-store';
import { useEffect } from 'react';

type AppointmentDateTime = {
   id: string | null
   date: string | null
}

type CreateAppointmentState = {
   totalStep: number,
   activeStep: number
   values: {
      selectedAppointment: AppointmentDateTime
      documents: string[]
      documentDeliveryMethod: string[]
      isConfirmed?: boolean
   }
   isLastStep: boolean
};

const defaultState: CreateAppointmentState = {
   activeStep: 0,
   values: {
      selectedAppointment: {
         id: null,
         date: null
      },
      documents: [],
      documentDeliveryMethod: [],
      isConfirmed: false
   },
   totalStep: 3,
   isLastStep: false,
}

const store = new Store<CreateAppointmentState>(defaultState)

const actions = {
   setAppointmentDateTime: (selectedAppointment: AppointmentDateTime) => {
      store.setState((prevState) => ({ ...prevState, values: { ...prevState.values, selectedAppointment } }))
   },
   setValues: (values: Partial<CreateAppointmentState['values']>) => {
      store.setState((prevState) => ({ ...prevState, values: { ...prevState.values, ...values } }))
   },
   moveNext: () => {
      const totalStep = store.state.totalStep;
      if (store.state.activeStep < totalStep - 1) {
         store.setState((prevState) => ({ ...prevState, activeStep: prevState.activeStep + 1 }))
      }
   },
   moveBack: () => {
      store.setState((prevState) => ({ ...prevState, activeStep: prevState.activeStep - 1 }))
   },
   resetState: () => {
      store.setState(() => defaultState);
   },
}

const useCreateAppointmentStore = () => {

   const { activeStep, totalStep, isLastStep } = useStore(store, ({ activeStep, totalStep, isLastStep }) => ({ totalStep, activeStep, isLastStep }))

   useEffect(() => {
      if (activeStep >= totalStep - 1) {
         store.setState((prev) => ({ ...prev, isLastStep: true }))
      } else {
         if (isLastStep) {
            store.setState((prev) => ({ ...prev, isLastStep: false }))
         }
      }
   }, [activeStep, totalStep, isLastStep])

   return { store, actions }
};

export default useCreateAppointmentStore;