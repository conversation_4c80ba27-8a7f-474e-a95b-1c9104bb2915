import { Checkbox, Radio, Stack, StackProps, Typography } from "@mui/material";

interface RadioItemProps extends StackProps {
   item: { title: string; id: string; description: string; meta: Record<string, string> }
   isMultiple: boolean
   isSelected: boolean
}

const RadioItem = ({ item, isSelected: checked, isMultiple, ...rest }: RadioItemProps) => {

   const { title, meta } = item;

   return (
      <Stack
         direction={'row'}
         justifyContent={'space-between'}
         alignItems={'center'}
         style={styles.radioItem}
         {...rest}
      >
         <Stack gap={.5}>
            <Typography color='primary.dark' >{title}</Typography>
            <Typography variant='subtitle1' color='primary.dark'>
               {`NPI: ${meta?.npi} - ${meta?.category}`}
            </Typography>
         </Stack>

         {!isMultiple ? (
            <Radio {...{ checked }} />
         ) : (
            <Checkbox {...{ checked }} />
         )}
      </Stack>
   )
}

const styles = {
   radioItem: {
      cursor: 'pointer'
   }
}

export default RadioItem;