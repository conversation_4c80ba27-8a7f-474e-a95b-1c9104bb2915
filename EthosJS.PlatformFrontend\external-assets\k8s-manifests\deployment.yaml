apiVersion: apps/v1
kind: Deployment
metadata:
  name: ethosjs-platformfrontend
  namespace: ethos-ns-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ethosjs-platformfrontend
  template:
    metadata:
      labels:
        app: ethosjs-platformfrontend
    spec:
      containers:
        - name: ethosjs-platformfrontend
          image: ethoscrdev.azurecr.io/ethosjs-platformfrontend:2025.05.2.dev
          ports:
            - containerPort: 3000
          livenessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 30
          readinessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 10
            periodSeconds: 10
      restartPolicy: Always
