import { Security, Verified } from "@mui/icons-material"
import { Badge, Box, lighten, Stack, SxProps, Theme, Typography } from "@mui/material"

const ICON_SIZE = { height: 36, width: 36 };

const VerifiedBanner = () => {
   return (
      <Box sx={style.root}>
         <Stack direction={'row'} gap={2} alignItems={'center'}>
            <Security sx={ICON_SIZE} />
            <Stack>
               <Typography variant="h6">
                  Blue Cross Blue Shield
               </Typography>
               <Typography variant="body1">
                  Insurance is active and coverage has been confirmed for the requested services.
               </Typography>
            </Stack>
         </Stack>
         <Badge children={<Typography>Verified</Typography>} sx={style.badge} />
      </Box>
   )
};

const style: Record<string, SxProps<Theme>> = {
   root: ({ palette }) => {
      return {
         background: lighten(palette.success.light, .7),
         p: 2,
         color: palette.success.dark,
         flexDirection: 'row',
         display: 'flex',
         justifyContent: 'space-between',
         alignItems: 'center',
         borderTopLeftRadius: 6,
         borderTopRightRadius: 6,
      }
   },
   badge: ({ palette }) => {
      return {
         background: palette.success.dark,
         color: palette.common.white,
         px: 1,
         py: .5,
         fontSize: 14,
         borderRadius: 2
      }
   }
}

export default VerifiedBanner;