import { CardActions, darken, lighten, styled } from "@mui/material";

const StyledCardFooter = styled(CardActions)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    padding: theme.spacing(1.5),
    borderTop: `2px solid ${lighten(theme.palette.primary.main, 0.85)}`,
    backgroundColor: lighten(theme.palette.primary.light, 0.8),
    ...theme.applyStyles('dark', {
        borderTop: `2.5px solid ${lighten(theme.palette.primary.dark, 0.3)}`,
        backgroundColor: darken(theme.palette.primary.dark, 0.85),
    }),
}));

export default StyledCardFooter;