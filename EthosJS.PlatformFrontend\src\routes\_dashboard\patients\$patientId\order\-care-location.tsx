import { createFileRoute, redirect } from '@tanstack/react-router';
import CareLocationStep from '@features/order-create/components/steps/care-location.step';

// export const Route = createFileRoute('/_dashboard/patients/$patientId/order/care-location')({
// 	component: CareLocation,
// 	loaderDeps: ({ search }) => ({ ...search }),
// 	beforeLoad: ({ params, search }) => {
// 		const { patientId } = params;
// 		const { orderId } = search;
// 		if (!orderId) {
// 			throw redirect({
// 				to: '/patients/$patientId/order/study',
// 				params: { patientId: patientId },
// 				search,
// 			});
// 		}
// 	},
// });

function CareLocation() {
	// const { patientId } = Route.useParams();
	// const { orderId, studyId } = Route.useSearch();
	// const navigate = Route.useNavigate();

	// return (
	// 	<CareLocationStep
	// 		patientId={patientId}
	// 		orderId={orderId!}
	// 		successCallback={() => {
	// 			navigate({
	// 				to: '/patients/$patientId/order/physicians',
	// 				params: { patientId },
	// 				search: { orderId, studyId },
	// 			});
	// 		}}
	// 	/>
	// );
	return null;
}

export default CareLocation;
