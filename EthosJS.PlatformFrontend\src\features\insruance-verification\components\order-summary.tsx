import {
	getApiOrderByIdOptions,
	getApiPhysicianByIdOptions,
} from '@client/workflows/@tanstack/react-query.gen';
import ChipSummary from '@components/chip-summary';
import MainCardContainer from '@components/main-container/main-card-container';
import { Typography } from '@mui/material';
import { useQuery } from '@tanstack/react-query';

interface OrderSummaryProps {
	orderId: string;
}

export default function OrderSummary({ orderId }: OrderSummaryProps) {
	const { data: orderData, isFetching: isFetchingOrderData } = useQuery({
		...getApiOrderByIdOptions({
			path: { id: orderId! },
			responseType: 'json',
		}),
		enabled: !!orderId,
	});

	const { referringPhysicianId } = orderData ?? {};

	const { data: referringPhysicianData } = useQuery({
		...getApiPhysicianByIdOptions({
			path: { id: referringPhysicianId! },
			responseType: 'json',
		}),
		enabled: !!referringPhysicianId,
	});

	if (isFetchingOrderData) {
		return (
			<MainCardContainer
				title="Order Information"
				color="primary"
				emphasis="low">
				<Typography
					variant="body1"
					color="primary">
					Loading order data...
				</Typography>
			</MainCardContainer>
		);
	}

	if (!orderData) {
		return (
			<MainCardContainer
				title="Order Information"
				color="error"
				emphasis="low">
				<Typography
					variant="body1"
					color="error">
					Error loading order data
				</Typography>
			</MainCardContainer>
		);
	}

	return (
		<MainCardContainer
			title="Order Information"
			color="primary"
			emphasis="low">
			<ChipSummary
				items={[
					{
						label: 'Order ID',
						value: orderData?.id ? orderData?.id : '',
					},
					{
						label: 'Physician ID',
						value: referringPhysicianData?.id ? referringPhysicianData?.id : '',
					},
				]}
			/>
		</MainCardContainer>
	);
}
