import { SvgIconComponent } from "@mui/icons-material";
import { Box, Icon, styled } from "@mui/material";

const Contianer = styled(Box)(({ theme }) => ({
    color: theme.palette.primary.main,
    borderRadius: theme.shape.borderRadius,
    padding: theme.spacing(1),
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
}));

interface FeaturedIconProps {
    icon: SvgIconComponent,
}

export default function FeaturedIcon(props: FeaturedIconProps) {
    return (
        <Contianer>
            <Icon
                component={props.icon}
                sx={{
                    fontSize: '36px',
                    color: 'inherit',
                }} />
        </Contianer>
    )
}