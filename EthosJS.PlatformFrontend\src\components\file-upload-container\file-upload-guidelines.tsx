import { Box, Stack, Typography } from '@mui/material';
import { Info } from 'lucide-react';

const guidelines = [
  'Ensure all documents are clearly legible',
  'For insurance cards, include both front and back',
  'Sensitive documents are encrypted during upload',
  'You can upload multiple files at once'
];

export const UploadGuidelines: React.FC = () => {
  return (
    <Box
      sx={{
        width: '100%',
        padding: '12px',
        background: '#F9F5FF',
        borderRadius: '6px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        gap: '10px'
      }}>
      <Box sx={{ flex: 1 }}>
        <Typography
          sx={{
            color: '#422F7D',
            fontSize: '16px',
            fontWeight: 500,
            lineHeight: '16px',
            mb: 1.25
          }}>
          Upload Guidelines
        </Typography>

        <Stack component="ul" spacing={1.25} sx={{ pl: 2, m: 0 }}>
          {guidelines.map((item, idx) => (
            <Typography
              key={idx}
              component="li"
              sx={{
                color: '#422F7D',
                fontSize: '13px',
                fontWeight: 500,
                lineHeight: '12px'
              }}>
              {item}
            </Typography>
          ))}
        </Stack>
      </Box>

      <Box
        sx={{
          width: '30px',
          height: '30px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0
        }}>
        <Info color="#7E56D8" />
      </Box>
    </Box>
  );
};
