import { SystemTextJsonNodesJsonNode } from '@client/workflows';

type StateTypes = 'Complete' | 'NotStarted' | 'InProgress' | 'Error' | 'Warning';

type SchedulingStepNames =
    | 'AppointmentSelection'
    | 'InstructionsAndDocuments'
    | 'ReviewAndConfirm'
    | 'FollowUpCall'
    | 'AppointmentConfirmation';

interface SchedulingStepProps {
    patientId: string;
    orderId: string;
    studyId: string;
    appointmentId?: string;
    successCallback: () => void;
}

type SchedulingState = {
    flowState: {
        status: StateTypes;
        progress: number;
        lastUpdate: string;
        currentStep: SchedulingStepNames;
    };
    stepState: Record<SchedulingStepNames, StateTypes>;
    appointmentData: {
        selectedAppointment: {
            id: string | null;
            date: string | null;
            roomId: string | null;
            careLocationShiftId: string | null;
        };
        documents: string[];
        documentDeliveryMethod: string[];
        followUpStatus: 'confirmed' | 're-scheduled' | 'cancelled' | 'unable-to-reach' | 'none';
        isConfirmed: boolean;
    };
} & SystemTextJsonNodesJsonNode;

export type { 
    SchedulingState, 
    StateTypes, 
    SchedulingStepNames, 
    SchedulingStepProps 
};