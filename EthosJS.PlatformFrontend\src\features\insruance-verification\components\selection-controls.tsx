import { Autocomplete, Stack, TextField } from '@mui/material';
import { filter, find } from 'lodash';
import {
	postApiOrderSearchOptions,
	postApiStudySearchOptions,
} from '@client/workflows/@tanstack/react-query.gen';
import {
	EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
	EthosModelQueryDto1EthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
} from '@client/workflows';
import { OrderQuery, Query, StudyQuery } from '@utils/query-dsl';
import { useQuery } from '@tanstack/react-query';

interface SelectionControlsProps {
	patientId: string;
	orderId: string;
	studyId?: string;
	insuranceId?: string;
	onChange: (orderId: string, studyId?: string, insuranceId?: string) => void;
}

export default function SelectionControls({
	patientId,
	orderId,
	studyId,
	insuranceId,
	onChange,
}: SelectionControlsProps) {
	const { data: orders } = useQuery({
		...postApiOrderSearchOptions({
			responseType: 'json',
			body: Query.literal(
				OrderQuery.withPatientId(patientId)
			) as unknown as EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!patientId,
		initialData: {
			items: [],
		},
		select: (data) => data?.items ?? [],
	});
	const { data: studies } = useQuery({
		...postApiStudySearchOptions({
			responseType: 'json',
			body: Query.literal(
				StudyQuery.withPatientId(patientId)
			) as unknown as EthosModelQueryDto1EthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!orderId && !!patientId,
		initialData: {
			items: [],
		},
		select: (data) => filter(data?.items, { orderId }) ?? [],
	});

	const selectedOrder = find(orders, { id: orderId }) ?? null;
	const selectedStudy = find(studies, { id: studyId }) ?? null;

	const { insurances } = selectedStudy ?? {};

	return (
		<Stack
			direction="column"
			gap={2}
			sx={{ py: 2 }}>
			<Autocomplete
				sx={{ minWidth: 200 }}
				//@ts-expect-error Type Mismatch
				value={selectedOrder ?? null}
				onChange={(_, newValue) => {
					onChange(newValue.id, studyId);
				}}
				disableClearable
				renderInput={(params) => (
					<TextField
						{...params}
						label="Order Id"
					/>
				)}
				options={orders}
				getOptionLabel={(option) => option.careLocationId}
				filterOptions={(x) => x}
			/>
			<Autocomplete
				sx={{ minWidth: 200 }}
				value={selectedStudy ?? null}
				onChange={(_, newValue) => {
					onChange(orderId, newValue?.id);
				}}
				renderInput={(params) => (
					<TextField
						{...params}
						label="Study Id"
					/>
				)}
				options={studies}
				getOptionLabel={(option) => option.id}
				filterOptions={(x) => x}
			/>
			<Autocomplete
				disablePortal
				sx={{ minWidth: 200 }}
				value={insuranceId ?? null}
				onChange={(_, newValue) => {
					onChange(orderId, studyId ?? '', newValue ?? '');
				}}
				renderInput={(params) => (
					<TextField
						{...params}
						label="Insurance Id"
					/>
				)}
				options={insurances ?? []}
				filterOptions={(x) => x}
			/>
		</Stack>
	);
}
