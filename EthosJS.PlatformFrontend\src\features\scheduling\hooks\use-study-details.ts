import {
	getApiOrderByIdOptions,
	getApiStudyByIdOptions,
} from '@client/workflows/@tanstack/react-query.gen';
import { useQueries } from '@tanstack/react-query';

export default function useStudyDetails({ studyId }: { studyId: string }) {
	const {
		data: studyData,
		isFetching: isFetchingStudyData,
		error: fetchStudyError,
	} = useQueries({
		queries: [
			{
				...getApiStudyByIdOptions({
					path: { id: studyId! },
					responseType: 'json',
				}),
				enabled: !!studyId,
			},
			{
				...getApiOrderByIdOptions({
					path: { id: studyId! },
					responseType: 'json',
				}),
				enabled: !!studyId,
			},
		],
		combine: (results) => {
			return {
				data: results[0].data,
				isFetching: results.some((result) => result.isFetching),
				error: results[0].error,
			};
		},
	});

	return {
		studyData,
		isFetchingStudyData,
		fetchStudyError,
	};
}
