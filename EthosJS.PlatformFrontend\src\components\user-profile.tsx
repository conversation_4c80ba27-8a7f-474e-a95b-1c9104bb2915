import { useMemo, useRef, useState } from 'react';
import { Logout } from '@mui/icons-material';
import { Avatar, Box, ListItemIcon, ListItemText, Menu, MenuItem, Typography } from '@mui/material';
import { useMsal } from '@azure/msal-react';

interface UserProfileProps {
    name: string;
    role: string;
    imageUrl: string;
}

export default function UserProfile(props: UserProfileProps) {
    const {
        name,
        role,
        imageUrl
    } = props;

    const { instance } = useMsal();
    const [open, setOpen] = useState(false);
    const anchorEl = useRef<HTMLDivElement | null>(null);

    const handleLogout = useMemo(() => async () => {
        try {
            await instance.logoutRedirect();
        } catch (error) {
            console.error(error);
        }
    }, []);

    return (
        <>
            <Box
                ref={anchorEl}
                onClick={() => { setOpen(!open) }}
                id="profile-menu-button"
                aria-controls={open ? 'profile-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={open ? 'true' : undefined}
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    cursor: 'pointer',
                    '&:hover': {
                        backgroundColor: 'rgba(0, 0, 0, 0.04)',
                        borderRadius: 1,
                    },
                    p: 1,
                    borderRadius: 8,
                }}
            >
                <Box mr={2}>
                    <Typography
                        variant="subtitle1"
                        component="div"
                        sx={{ fontWeight: 'bold', lineHeight: 'normal' }}
                    >
                        {name}
                    </Typography>
                    <Typography
                        variant="caption"
                        component="div"
                        sx={{ lineHeight: 'normal' }}
                    >
                        {role}
                    </Typography>
                </Box>
                {/* Circular profile image */}
                <Avatar
                    alt={`${name} profile image`}
                    src={imageUrl} // Replace with your actual image path
                    sx={{ width: 35, height: 35 }}
                />
            </Box >
            <Menu
                anchorEl={anchorEl.current}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                id="profile-menu"
                open={open}
                onClose={() => { setOpen(false) }}
                MenuListProps={{
                    'aria-labelledby': 'profile-menu-button',
                }}>
                <MenuItem onClick={() => {
                    setOpen(false);
                    handleLogout();
                }}>
                    <ListItemIcon>
                        <Logout />
                    </ListItemIcon>
                    <ListItemText>Logout</ListItemText>
                </MenuItem>
            </Menu>
        </>
    );
}
