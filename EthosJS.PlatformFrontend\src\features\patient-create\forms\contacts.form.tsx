import StepCardControl from '@components/step-card-control';
import { Box, CardContent, Button } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { formOptions, useStore } from '@tanstack/react-form';
import { Mail, Phone, Siren } from 'lucide-react';

import { ValidationErrors } from '@app-types/validation';
import { ContactsData } from '../types';
import { FormProps } from '@components/forms/predefined-form-props';
import { useMemo } from 'react';
import EmailForm, { emailFormOptions } from '@components/forms/app-email-form';
import EmergencyContactForm, {
	defaultValues as emergencyContactFormDefaultValues,
} from '@components/forms/app-emergency-contact-form';
import { getErrorsForIndexField } from '@utils/forms';
import ArrayFormContainer from '@components/forms/app-array-form-container';
import PhoneNumberForm, { phoneNumberFormOptions } from '@components/forms/app-phone-number-form';
import {
	formatEmailSummary,
	formatEmergencyContactSummary,
	formatPhoneNumberSummary,
} from './formatters';

function contactsOptions(savedData?: ContactsData) {
	return formOptions({
		defaultValues: {
			contactInformation: {
				phoneNumbers: [],
				emails: [],
				emergencyContacts: [],
				...savedData?.contactInformation,
			},
		} as ContactsData,
	});
}

export default function Contacts({
	onSubmit,
	onSaveDraft,
	savedData,
	onValidate,
	isUpdate,
}: FormProps<ContactsData>) {
	const options = useMemo(() => contactsOptions(savedData), [savedData]);

	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(value);
				return res;
			},
		},
		onSubmit: async ({ value }) => {
			onSubmit(value);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	return (
		<Box
			component="form"
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
				<form.AppField name="contactInformation">
					{() => (
						<>
							<form.AppField
								name="contactInformation.phoneNumbers"
								mode="array">
								{({ pushValue, removeValue, state, replaceValue }) => {
									return (
										<ArrayFormContainer
											pushValue={pushValue}
											removeValue={removeValue}
											replaceValue={replaceValue}
											dataTestId="contacts.phoneNumbers"
											state={{
												value: state.value ?? [],
											}}
											onValidate={async (data, index) => {
												const phoneNumbers =
													state.value?.map((_, j) => {
														if (j === index) {
															return data;
														}
														return state.value?.[j];
													}) ?? [];
												const vals = {
													contactInformation: {
														phoneNumbers,
													},
												} as ContactsData;
												const res = await onValidate(vals);
												return getErrorsForIndexField(
													`contactInformation.phoneNumbers[${index}]`,
													res
												);
											}}
											renderForm={function (props) {
												return <PhoneNumberForm {...props} />;
											}}
											defaultValues={phoneNumberFormOptions().defaultValues}
											formatSummary={formatPhoneNumberSummary}
											mainCardContainerProps={{
												title: 'Phone Numbers',
												icon: <Phone />,
												color: 'primary',
												emphasis: state.value && state.value.length > 0 ? 'high' : 'low',
												primaryActionType: 'Add',
											}}
										/>
									);
								}}
							</form.AppField>
							<form.AppField
								name="contactInformation.emails"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }): Promise<ValidationErrors | undefined> => {
										const vals = {
											contactInformation: {
												emails: value,
											},
										} as ContactsData;
										const res = await onValidate(vals);
										return res;
									},
								}}
								mode="array">
								{({ pushValue, removeValue, state, replaceValue }) => {
									return (
										<ArrayFormContainer
											pushValue={pushValue}
											removeValue={removeValue}
											replaceValue={replaceValue}
											dataTestId="contacts.emails"
											state={{
												value: state.value ?? [],
											}}
											onValidate={async (data, index) => {
												const emails =
													state.value?.map((_, j) => {
														if (j === index) {
															return data;
														}
														return state.value?.[j];
													}) ?? [];
												const vals = {
													contactInformation: {
														emails,
													},
												} as ContactsData;
												const res = await onValidate(vals);
												return getErrorsForIndexField(`contactInformation.emails[${index}]`, res);
											}}
											isUpdate={isUpdate}
											renderForm={function (props) {
												return <EmailForm {...props} />;
											}}
											defaultValues={emailFormOptions().defaultValues}
											formatSummary={formatEmailSummary}
											mainCardContainerProps={{
												title: 'Emails',
												icon: <Mail />,
												color: 'primary',
												emphasis: state.value && state.value.length > 0 ? 'high' : 'low',
												primaryActionType: 'Add',
											}}
										/>
									);
								}}
							</form.AppField>
							<form.AppField
								name="contactInformation.emergencyContacts"
								mode="array">
								{({ pushValue, removeValue, state, replaceValue }) => {
									return (
										<ArrayFormContainer
											pushValue={pushValue}
											removeValue={removeValue}
											replaceValue={replaceValue}
											dataTestId="contacts.emergencyContacts"
											state={{
												value: state.value ?? [],
											}}
											onValidate={async (data, index) => {
												const emergencyContacts =
													state.value?.map((_, j) => {
														if (j === index) {
															return data;
														}
														return state.value?.[j];
													}) ?? [];
												const vals = {
													contactInformation: {
														emergencyContacts,
													},
												} as ContactsData;
												const res = await onValidate(vals);
												return getErrorsForIndexField(
													`contactInformation.emergencyContacts[${index}]`,
													res
												);
											}}
											renderForm={function (props) {
												return <EmergencyContactForm {...props} />;
											}}
											defaultValues={emergencyContactFormDefaultValues}
											formatSummary={formatEmergencyContactSummary}
											mainCardContainerProps={{
												title: 'Emergency Contacts',
												icon: <Siren />,
												color: 'primary',
												emphasis: state.value && state.value.length > 0 ? 'high' : 'low',
												primaryActionType: 'Add',
											}}
										/>
									);
								}}
							</form.AppField>
						</>
					)}
				</form.AppField>
			</CardContent>
			<StepCardControl>
				{!isUpdate && (
					<Button
						variant="outlined"
						color="primary"
						onClick={() => onSaveDraft(values)}>
						Save Draft
					</Button>
				)}
				<form.Subscribe
					selector={({ isDirty, canSubmit, errorMap, isSubmitting }) => ({
						isDirty,
						canSubmit,
						errorMap,
						isSubmitting,
					})}
					children={({ isDirty, canSubmit, isSubmitting }) => {
						return (
							<Button
								variant="contained"
								color="primary"
								type="submit"
								loading={isSubmitting}
								disabled={!isDirty || !canSubmit}>
								{isUpdate ? 'Update' : 'Next'}
							</Button>
						);
					}}
				/>
			</StepCardControl>
		</Box>
	);
}
