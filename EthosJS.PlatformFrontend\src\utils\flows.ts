import { Status } from '@components/left-menu';
import { ComponentType } from 'react';
import { find } from 'lodash';
import { AddNewOrderState, AddNewPatientState } from '@client/workflows';

export interface WorkflowStep {
  name: string;
  key: string;
  icon: unknown;
  component: ComponentType<any>;
  status: Status;
  data: Record<string, unknown>;
  from: string | undefined;
  to: string;
}

export type WorkflowSteps<T> = Array<T>;

export type StepTransition = {
  key: string;
  timestamp: string;
  data: unknown;
};

export type StateData = AddNewOrderState | AddNewPatientState;

export interface IWorkflowState<T extends WorkflowStep> {
  progress: number;
  status: Status;
  lastUpdate: string;
  steps: WorkflowSteps<T>;
  currentStep: WorkflowSteps<T>[number] | null;
  selectedStep: WorkflowSteps<T>[number] | null;
}

export function calculateStepStatus<T extends WorkflowStep>(step: T, data: StateData): T {
  const { pastTransitions, draftTransitions } = data;
  const { key } = step;
  const pastTransitionsArray: Array<StepTransition> = Array.isArray(pastTransitions)
    ? pastTransitions
    : [];
  const draftTransitionsArray: Array<StepTransition> = Array.isArray(draftTransitions)
    ? draftTransitions
    : [];

  const completedTransition = pastTransitionsArray.find((transition) => transition.key === key);
  if (completedTransition) {
    const stepData = (completedTransition.data as Record<string, any>) ?? {};
    return {
      ...step,
      data: stepData,
      status: Status.Completed,
      stepMenuDisable: false
    } as T;
  }

  const draftTransition = draftTransitionsArray.find((transition) => transition.key === key);
  const InProgress = pastTransitionsArray[pastTransitionsArray.length - 1]?.key === step.from;

  if (InProgress || draftTransition) {
    const stepData = (draftTransition?.data as Record<string, any>) ?? {};
    return {
      ...step,
      data: stepData,
      status: InProgress ? Status.InProgress : Status.MissingInformation,
      stepMenuDisable: false
    } as T;
  }
  return step;
}

export function calculateWorkflowStatus<T extends WorkflowStep>(
  steps: WorkflowSteps<T>,
  data: AddNewOrderState
): IWorkflowState<T> {

  const { pastTransitions, draftTransitions } = data;
  const newSteps = steps.map((step) => calculateStepStatus(step, data));
  const progress = newSteps.filter((step) => step.status === Status.Completed).length;
  const progressPercentage = (progress / steps.length) * 100;
  const drafts = draftTransitions as Array<StepTransition>;
  const pasts = pastTransitions as Array<StepTransition>;
  const lastDraft = drafts?.length > 0 ? drafts[drafts.length - 1] : null;
  const lastPast = pasts?.length > 0 ? pasts[pasts.length - 1] : null;

  let lastUpdate = '';
  if (lastDraft && lastPast) {
    lastUpdate =
      lastDraft.timestamp > lastPast.timestamp ? lastDraft.timestamp : lastPast.timestamp;
  } else if (lastPast) {
    lastUpdate = lastPast.timestamp;
  } else if (lastDraft) {
    lastUpdate = lastDraft.timestamp;
  } else {
    lastUpdate = '';
  }

  return {
    steps: newSteps,
    progress: progressPercentage,
    status: progressPercentage === 100 ? Status.Completed : Status.InProgress,
    lastUpdate,
    currentStep: find(newSteps, (step) => step.status === Status.InProgress) ?? null,
    selectedStep: null
  };
}
