import { Field, ArrayField } from '@components/workflow(deprecated)/step-form-generator';
import { AppFormType } from '@hooks/app-form';
import BasicFieldComponent from './app-basic-field';
import AppGenerativeForm from './app-generative.form';
import { getFieldDefaultValue, pascalToSpacedWords } from '@utils/generator-helper';
import AddInfoCard from '@components/add-info-card';

export function FormFieldComponent({ form, fieldDef }: { form: AppFormType; fieldDef: Field }) {
	const { type, label: rawLabel, validation } = fieldDef;

	const label = pascalToSpacedWords(rawLabel);

	if (type === 'Basic') {
		return (
			<BasicFieldComponent
				form={form}
				fieldDef={fieldDef}
			/>
		);
	}

	if (type === 'Options') {
		return (
			<form.AppField
				name={fieldDef.name}
				children={(field) => {
					return (
						<field.AppSelectField
							label={label}
							options={fieldDef.options}
						/>
					);
				}}
			/>
		);
	}
	if (type === 'Checkbox') {
		return (
			<form.AppField
				name={fieldDef.name}
				children={(field) => {
					return <field.AppCheckboxField label={pascalToSpacedWords(label)} />;
				}}
			/>
		);
	}

	if (type === 'Form') {
		return (
			<AppGenerativeForm
				form={form}
				formField={fieldDef}
			/>
		);
	}

	if (type === 'Array') {
		const { name, label, innerType } = fieldDef as ArrayField;
		return (
			<form.AppField
				name={name}
				mode="array"
				children={({ state: { value }, pushValue, removeValue, replaceValue }) => {
					const lastValue = Array.isArray(value) && value.length > 0 ? value.length : 0;
					return (
						<AddInfoCard
							onClick={() => {
								pushValue(`${name}[${lastValue}]` as never, getFieldDefaultValue(innerType));
							}}
							title={pascalToSpacedWords(label)}
							showHeader={lastValue === 0}>
							{Array.isArray(value)
								? value.map((_, index) => {
										const newName = `${name}[${index}]`;
										if (innerType.type === 'Form') {
											//TODO: Add passing add and remove functions to the form
										}
										return (
											<FormFieldComponent
												key={newName}
												form={form}
												fieldDef={{
													...innerType,
													name: newName,
												}}
											/>
										);
									})
								: null}
						</AddInfoCard>
					);
				}}
			/>
		);
	}

	return <>No Component</>;
}
