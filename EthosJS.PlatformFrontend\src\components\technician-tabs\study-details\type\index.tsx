import Card from "@components/card";
import CardHeader from "@components/card-header";
import { Box, CardContent, Stack, Typography } from "@mui/material";
import styles from "@components/technician-tabs/styles";
import Summary from "@components/technician-tabs/summary";

type PSG = {
   inLab: string
   CPTCode: string
   ETCO2: string
   extendedEEG: string
   videoRecording: string
   transcutaneousCO2: string
   parasomniaAssessment: string
}

interface StudyDetailsTypeProp {
   title?: string
   values: {
      PSG: Partial<PSG>,
   }
}


const StudyDetailsType = (props: StudyDetailsTypeProp) => {

   const { title, values: { PSG } } = props;

   return (
      <Card>
         <CardHeader
            {...{
               title: <Typography>{title}</Typography>,
               emphasis: 'dark'
            }}
         />
         <Box sx={styles.cardBody}>
            <Stack gap={2}>
               <Stack>
                  <Card >
                     <CardHeader
                        {...{
                           title: <Typography>{'PSG (Polysomnography)'}</Typography>,
                        }}
                     />
                     <CardContent>
                        <Summary
                           data={[
                              {
                                 value: PSG?.inLab as string,
                              },
                              {
                                 label: 'CPT Code',
                                 value: PSG?.CPTCode as string
                              },
                              {
                                 value: PSG?.ETCO2 as string
                              },
                              {
                                 value: PSG?.extendedEEG as string
                              },
                              {
                                 value: PSG?.videoRecording as string
                              },
                              {
                                 value: PSG?.transcutaneousCO2 as string
                              },
                              {
                                 value: PSG?.parasomniaAssessment as string
                              },
                           ]}
                        />
                     </CardContent>
                  </Card>
               </Stack>
            </Stack>
         </Box>
      </Card>
   )
}

export default StudyDetailsType;