import { Grid2 as Grid, Typography } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-form';
import { Mail, Phone } from 'lucide-react';
import { MapPin } from 'lucide-react';
import MainCardContainer, {
	MainCardContainerProps,
} from '@components/main-container/main-card-container';
import { useMemo } from 'react';
import { defaultValues, guardianFormOptions } from './utils';
import PhoneNumberForm, { phoneNumberFormOptions } from '../app-phone-number-form';
import { ValidationErrors } from '@app-types/validation';
import EmailForm, { emailFormOptions } from '../app-email-form';
import AddressForm, { addressFormOptions, formatAddressSummary } from '../app-address-form';
import {
	formatEmailSummary,
	formatPhoneNumberSummary,
} from '@features/patient-create/forms/formatters';
import { EthosWorkflowsApiPatientGuardianDto } from '@client/workflows';
import { formHasValues } from '../utils';
import ArrayFormContainer from '../app-array-form-container';
import { getErrorsForIndexField } from '@utils/forms';
import { useAddressUses } from '@hooks/use-address-uses';
import { find } from 'lodash';

interface GuardianFormProps {
	onAdd?: (values: EthosWorkflowsApiPatientGuardianDto) => void;
	onCancel?: (shouldRemove: boolean) => void;
	onDelete?: () => void;
	formValues: EthosWorkflowsApiPatientGuardianDto;
	onValidate?: (data: EthosWorkflowsApiPatientGuardianDto) => Promise<ValidationErrors | undefined>;
	isUpdate?: boolean;
	containerProps?: MainCardContainerProps;
}

export default function GuardianForm({
	onAdd,
	onCancel,
	onDelete,
	formValues,
	onValidate,
	isUpdate,
	containerProps = {},
}: GuardianFormProps) {
	const options = useMemo(() => guardianFormOptions(formValues), [formValues]);
	const hasValues = formHasValues(formValues, defaultValues);
	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				if (!onValidate) {
					return;
				}
				const res = await onValidate(value);
				return res;
			},
		},
		defaultState: {
			isDirty: hasValues,
			isPristine: !hasValues,
		},
	});

	const values = useStore(form.store, (state) => state.values);

	const { addressUses } = useAddressUses();
	const physicalAddressUse = find(addressUses ?? [], (use) => {
		if (!use.key) return false;
		return use.key?.value === 'Physical';
	});

	console.log('values', values);
	console.log('hasValues', hasValues);
	console.log('isUpdate', isUpdate);
	return (
		<MainCardContainer
			title="Add Guardian"
			emphasis="high"
			descriptionSubheader="* Indicates a required field"
			descriptionText={`Enter the guardian's information below.`}
			footerProps={{
				primaryButton1: {
					label: hasValues && isUpdate ? 'Update' : 'Add',
					onClick: () => onAdd?.(values),
					'data-testid': 'guardian.submitButton',
				},
				primaryButton2: {
					label: 'Cancel',
					variant: 'outlined',
					color: 'error',
					onClick: () => {
						form.reset();
						onCancel?.(hasValues);
					},
					'data-testid': 'guardian.cancelButton',
				},
				secondaryButton1: {
					label: 'Delete',
					variant: 'outlined',
					color: 'error',
					onClick: () => {
						onDelete?.();
					},
					'data-testid': 'guardian.deleteButton',
				},
			}}
			{...containerProps}>
			<Grid
				container
				spacing={2}>
				{/* Basic Information */}
				<Grid
					container
					spacing={2}
					size={12}>
					<Grid size={12}>
						<Typography
							variant="h6"
							sx={{ mb: 1, color: 'primary.dark' }}>
							Guardian Information
						</Typography>
					</Grid>
					<Grid size={{ xs: 12, sm: 6 }}>
						<form.AppField
							name="relationshipToPatient"
							children={(field) => (
								<field.AppSelectField
									label="Guardian Type"
									required
									referenceDataSetName="relationship"
									referenceDataFilter="relationshipType eq Guardian"
									data-testid="guardianBasicInformation.guardianType"
								/>
							)}
						/>
					</Grid>
				</Grid>
				<Grid
					container
					spacing={2}
					size={12}>
					<Grid size={{ xs: 12, sm: 6, md: 4 }}>
						<form.AppField
							name="names[0].prefix"
							children={(field) => (
								<field.AppAutocompleteField
									label="Prefix"
									referenceDataSetName="namePrefix"
									data-testid="guardianBasicInformation.prefix"
								/>
							)}
						/>
					</Grid>
				</Grid>
				<Grid size={4}>
					<form.AppField
						name="names[0].firstName"
						children={(field) => (
							<field.AppTextField
								label="Legal First Name"
								required
								data-testid="guardianBasicInformation.firstName"
							/>
						)}
					/>
				</Grid>
				<Grid size={4}>
					<form.AppField
						name="names[0].middleName"
						children={(field) => (
							<field.AppTextField
								label="Middle Name"
								data-testid="guardianBasicInformation.middleName"
							/>
						)}
					/>
				</Grid>
				<Grid size={4}>
					<form.AppField
						name="names[0].lastName"
						children={(field) => (
							<field.AppTextField
								label="Legal Last Name"
								required
								data-testid="guardianBasicInformation.lastName"
							/>
						)}
					/>
				</Grid>
				<Grid
					container
					spacing={2}
					size={12}>
					<Grid size={{ xs: 12, sm: 6, md: 4 }}>
						<form.AppField
							name="names[0].suffix"
							children={(field) => (
								<field.AppAutocompleteField
									label="Suffix"
									referenceDataSetName="nameSuffix"
									data-testid="guardianBasicInformation.suffix"
								/>
							)}
						/>
					</Grid>
				</Grid>
				{/* Demographics Information */}
				<Grid size={12}>
					<Typography
						variant="h6"
						sx={{ mb: 1, color: 'primary.dark' }}>
						Guardian Demographics
					</Typography>
				</Grid>
				<Grid size={{ xs: 12, sm: 6 }}>
					<form.AppField
						name="demographics.gender"
						children={(field) => (
							<field.AppAutocompleteField
								label="Gender"
								referenceDataSetName="gender"
								data-testid="guardianDemographics.gender"
							/>
						)}
					/>
				</Grid>
				<Grid size={{ xs: 12, sm: 6 }}>
					<form.AppField
						name="demographics.dateOfBirth"
						children={(field) => (
							<field.AppDateField
								label="Date of Birth"
								required
								data-testid="guardianDemographics.dateOfBirth"
							/>
						)}
					/>
				</Grid>
				<Grid size={{ xs: 12, sm: 6 }}>
					<form.AppField
						name="identifiers[0].value"
						children={(field) => (
							<field.AppSsnField
								label="SSN"
								required
								data-testid="guardianDemographics.ssn"
							/>
						)}
					/>
				</Grid>
				{/* <Grid size={{ xs: 12, sm: 6 }}>
					<form.AppField
						name="guardianDemographics.idType"
						children={(field) => (
							<field.AppSelectField
								label="ID Type"
								referenceDataSetName="idType"
								data-testid="guardianDemographics.idType"
							/>
						)}
					/>
				</Grid>
				*/}
				<Grid size={{ xs: 12, sm: 6 }}>
					<form.AppField
						name="id"
						children={(field) => (
							<field.AppTextField
								label="ID"
								data-testid="guardian.id"
							/>
						)}
					/>
				</Grid>
				<Grid size={12}>
					<form.AppField
						name="contactInformation.phoneNumbers"
						mode="array">
						{({ pushValue, removeValue, state, replaceValue }) => {
							return (
								<ArrayFormContainer
									pushValue={pushValue}
									removeValue={removeValue}
									replaceValue={replaceValue}
									dataTestId="guardian.phoneNumbers"
									state={{
										value: state.value ?? [],
									}}
									onValidate={async (data, index) => {
										const phoneNumbers =
											state.value?.map((_, j) => {
												if (j === index) {
													return data;
												}
												return state.value?.[j];
											}) ?? [];
										const vals = {
											contactInformation: {
												phoneNumbers,
											},
										} as EthosWorkflowsApiPatientGuardianDto;
										if (!onValidate) {
											return;
										}
										const res = await onValidate(vals);
										return getErrorsForIndexField(`contactInformation.phoneNumbers[${index}]`, res);
									}}
									renderForm={function (props) {
										return <PhoneNumberForm {...props} />;
									}}
									defaultValues={phoneNumberFormOptions().defaultValues}
									formatSummary={formatPhoneNumberSummary}
									mainCardContainerProps={{
										title: 'Phone Numbers',
										icon: <Phone />,
										color: 'primary',
										emphasis: state.value && state.value.length > 0 ? 'high' : 'low',
										primaryActionType: 'Add',
									}}
								/>
							);
						}}
					</form.AppField>
				</Grid>
				<Grid size={12}>
					<form.AppField
						name="contactInformation.emails"
						mode="array">
						{({ pushValue, removeValue, state, replaceValue }) => {
							return (
								<ArrayFormContainer
									pushValue={pushValue}
									removeValue={removeValue}
									replaceValue={replaceValue}
									dataTestId="guardian.emails"
									state={{
										value: state.value ?? [],
									}}
									onValidate={async (data, index) => {
										const emails =
											state.value?.map((_, j) => {
												if (j === index) {
													return data;
												}
												return state.value?.[j];
											}) ?? [];
										const vals = {
											contactInformation: {
												emails,
											},
										} as EthosWorkflowsApiPatientGuardianDto;
										if (!onValidate) {
											return;
										}
										const res = await onValidate(vals);
										return getErrorsForIndexField(`contactInformation.emails[${index}]`, res);
									}}
									renderForm={function (props) {
										return <EmailForm {...props} />;
									}}
									defaultValues={emailFormOptions().defaultValues}
									formatSummary={formatEmailSummary}
									mainCardContainerProps={{
										title: 'Emails',
										icon: <Mail />,
										color: 'primary',
										emphasis: state.value && state.value.length > 0 ? 'high' : 'low',
										primaryActionType: 'Add',
									}}
								/>
							);
						}}
					</form.AppField>
				</Grid>
				<Grid size={12}>
					<form.AppField
						name="contactInformation.addresses"
						mode="array">
						{({ pushValue, removeValue, state, replaceValue }) => {
							return (
								<ArrayFormContainer
									pushValue={pushValue}
									removeValue={removeValue}
									replaceValue={replaceValue}
									dataTestId="guardian.addresses"
									state={{
										value: state.value ?? [],
									}}
									onValidate={async (data, index) => {
										const addresses =
											state.value?.map((_, j) => {
												if (j === index) {
													return data;
												}
												return state.value?.[j];
											}) ?? [];
										const vals = {
											contactInformation: {
												addresses,
											},
										} as EthosWorkflowsApiPatientGuardianDto;
										if (!onValidate) {
											return;
										}
										const res = await onValidate(vals);
										return getErrorsForIndexField(`contactInformation.addresses[${index}]`, res);
									}}
									renderForm={function (props) {
										if (!physicalAddressUse) {
											return null;
										}
										return (
											<AddressForm
												addressUse="Physical"
												addressUseRefValue={Number(physicalAddressUse?.id)}
												{...props}
											/>
										);
									}}
									defaultValues={
										addressFormOptions(undefined, physicalAddressUse?.id).defaultValues
									}
									formatSummary={formatAddressSummary}
									mainCardContainerProps={{
										title: 'Addresses',
										icon: <MapPin />,
										color: 'primary',
										emphasis: state.value && state.value.length > 0 ? 'high' : 'low',
										primaryActionType: 'Add',
									}}
								/>
							);
						}}
					</form.AppField>
				</Grid>
				{/* <Grid size={12}>
						<form.AppField
							name="legalDocuments"
							mode="array">
							{({ pushValue, removeValue, state, replaceValue }) => (
								<MainCardContainer
									title="Legal Documents"
									icon={<FileText />}
									color="primary"
									emphasis="low"
									primaryActionType="Add"
									onPrimaryAction={() => pushValue([])}
									data-testid="legalDocuments.container">
									{state.value.length > 0 &&
										state.value.map((_, i) => {
											return (
												<ArrayFieldContainer
													key={i}
													initialEditState={true}
													items={[]}
													title="Legal Document">
													{({ setEdit }) => (
														<LegalDocumentForm
															workflowId={workflowId}
															formValues={state.value[i]}
															onAdd={(data) => {
																replaceValue(i, data);
																setEdit(false);
															}}
															onCancel={() => {
																if (!hasValues) {
																	removeValue(i);
																}
																setEdit(false);
															}}
															onDelete={() => {
																removeValue(i);
																setEdit(false);
															}}
														/>
													)}
												</ArrayFieldContainer>
											);
										})}
								</MainCardContainer>
							)}
						</form.AppField>
					</Grid> */}
			</Grid>
		</MainCardContainer>
	);
	// 	<Card emphasis="dark">
	// 		<CardHeader
	// 			emphasis="dark"
	// 			title="Add Guardians"
	// 			avatar={<UserPlus size={32} />}
	// 			slotProps={{
	// 				title: { variant: 'h6' },
	// 			}}
	// 		/>
	// 		<CardContent>
	// 			<Grid
	// 				container
	// 				spacing={2}>
	// 				{/* Basic Information */}
	// 				<Grid
	// 					container
	// 					spacing={2}
	// 					size={12}>
	// 					<Grid size={12}>
	// 						<Typography
	// 							variant="h6"
	// 							sx={{ mb: 1, color: 'primary.dark' }}>
	// 							Guardian Information
	// 						</Typography>
	// 					</Grid>
	// 					<Grid size={{ xs: 12, sm: 6 }}>
	// 						<form.AppField
	// 							name="guardianBasicInformation.guardianType"
	// 							children={(field) => (
	// 								<field.AppSelectField
	// 									label="Guardian Type"
	// 									required
	// 									referenceDataSetName="relationship"
	// 									referenceDataFilter="relationshipType eq Guardian"
	// 									data-testid="guardianBasicInformation.guardianType"
	// 								/>
	// 							)}
	// 						/>
	// 					</Grid>
	// 				</Grid>
	// 				<Grid
	// 					container
	// 					spacing={2}
	// 					size={12}>
	// 					<Grid size={{ xs: 12, sm: 6, md: 4 }}>
	// 						<form.AppField
	// 							name="guardianBasicInformation.prefix"
	// 							children={(field) => (
	// 								<field.AppAutocompleteField
	// 									label="Prefix"
	// 									referenceDataSetName="namePrefix"
	// 									data-testid="guardianBasicInformation.prefix"
	// 								/>
	// 							)}
	// 						/>
	// 					</Grid>
	// 				</Grid>
	// 				<Grid size={4}>
	// 					<form.AppField
	// 						name="guardianBasicInformation.firstName"
	// 						children={(field) => (
	// 							<field.AppTextField
	// 								label="Legal First Name"
	// 								required
	// 								data-testid="guardianBasicInformation.firstName"
	// 							/>
	// 						)}
	// 					/>
	// 				</Grid>
	// 				<Grid size={4}>
	// 					<form.AppField
	// 						name="guardianBasicInformation.middleName"
	// 						children={(field) => (
	// 							<field.AppTextField
	// 								label="Middle Name"
	// 								data-testid="guardianBasicInformation.middleName"
	// 							/>
	// 						)}
	// 					/>
	// 				</Grid>
	// 				<Grid size={4}>
	// 					<form.AppField
	// 						name="guardianBasicInformation.lastName"
	// 						children={(field) => (
	// 							<field.AppTextField
	// 								label="Legal Last Name"
	// 								required
	// 								data-testid="guardianBasicInformation.lastName"
	// 							/>
	// 						)}
	// 					/>
	// 				</Grid>
	// 				<Grid
	// 					container
	// 					spacing={2}
	// 					size={12}>
	// 					<Grid size={{ xs: 12, sm: 6, md: 4 }}>
	// 						<form.AppField
	// 							name="guardianBasicInformation.suffix"
	// 							children={(field) => (
	// 								<field.AppAutocompleteField
	// 									label="Suffix"
	// 									referenceDataSetName="nameSuffix"
	// 									data-testid="guardianBasicInformation.suffix"
	// 								/>
	// 							)}
	// 						/>
	// 					</Grid>
	// 				</Grid>
	// 				{/* Demographics Information */}
	// 				<Grid size={12}>
	// 					<Typography
	// 						variant="h6"
	// 						sx={{ mb: 1, color: 'primary.dark' }}>
	// 						Guardian Demographics
	// 					</Typography>
	// 				</Grid>
	// 				<Grid size={{ xs: 12, sm: 6 }}>
	// 					<form.AppField
	// 						name="guardianDemographics.gender"
	// 						children={(field) => (
	// 							<field.AppAutocompleteField
	// 								label="Gender"
	// 								referenceDataSetName="gender"
	// 								data-testid="guardianDemographics.gender"
	// 							/>
	// 						)}
	// 					/>
	// 				</Grid>
	// 				<Grid size={{ xs: 12, sm: 6 }}>
	// 					<form.AppField
	// 						name="guardianDemographics.dateOfBirth"
	// 						children={(field) => (
	// 							<field.AppDateField
	// 								label="Date of Birth"
	// 								required
	// 								data-testid="guardianDemographics.dateOfBirth"
	// 							/>
	// 						)}
	// 					/>
	// 				</Grid>
	// 				<Grid size={{ xs: 12, sm: 6 }}>
	// 					<form.AppField
	// 						name="guardianDemographics.ssn"
	// 						children={(field) => (
	// 							<field.AppSsnField
	// 								label="SSN"
	// 								required
	// 								data-testid="guardianDemographics.ssn"
	// 							/>
	// 						)}
	// 					/>
	// 				</Grid>
	// 				{/* <Grid size={{ xs: 12, sm: 6 }}>
	//                      <form.AppField
	//                          name="guardianDemographics.relationShipToPatient"
	//                          children={(field) => (
	//                              <field.AppSelectField
	//                                  label="Relationship to Patient"
	//                                  required
	//                                  referenceDataSetName="relationship"
	//                                  referenceDataFilter="relationshipType eq Guardian"
	//                              />
	//                          )}
	//                      />
	//                  </Grid> */}
	// 				<Grid size={{ xs: 12, sm: 6 }}>
	// 					<form.AppField
	// 						name="guardianDemographics.idType"
	// 						children={(field) => (
	// 							<field.AppSelectField
	// 								label="ID Type"
	// 								referenceDataSetName="idType"
	// 								data-testid="guardianDemographics.idType"
	// 							/>
	// 						)}
	// 					/>
	// 				</Grid>
	// 				<Grid size={{ xs: 12, sm: 6 }}>
	// 					<form.AppField
	// 						name="guardianDemographics.idNumber"
	// 						children={(field) => (
	// 							<field.AppTextField
	// 								label="ID Number"
	// 								data-testid="guardianDemographics.idNumber"
	// 							/>
	// 						)}
	// 					/>
	// 				</Grid>
	// 				<Grid size={12}>
	// 					<form.AppField
	// 						name="phoneNumbers"
	// 						mode="array">
	// 						{({ pushValue, removeValue, state, replaceValue }) => (
	// 							<MainCardContainer
	// 								title="Phone Numbers"
	// 								icon={<Phone />}
	// 								color="primary"
	// 								emphasis="low"
	// 								primaryActionType="Add"
	// 								onPrimaryAction={() => pushValue(phoneNumberFormOptions().defaultValues)}
	// 								data-testid="phoneNumbers.container">
	// 								{state.value.length > 0 &&
	// 									state.value.map((_, i) => {
	// 										return (
	// 											<ArrayFieldContainer
	// 												key={i}
	// 												initialEditState={true}
	// 												items={formatPhoneNumberSummary(state.value[i])}
	// 												title="Phone Number"
	// 												showHeader={false}>
	// 												{({ setEdit }) => (
	// 													<PhoneNumberForm
	// 														formValues={state.value[i]}
	// 														onAdd={(data) => {
	// 															replaceValue(i, data);
	// 															setEdit(false);
	// 														}}
	// 														onCancel={() => {
	// 															if (!hasValues) {
	// 																removeValue(i);
	// 															}
	// 															setEdit(false);
	// 														}}
	// 														onDelete={() => {
	// 															removeValue(i);
	// 															setEdit(false);
	// 														}}
	// 														onValidate={function (
	// 															_data: EthosWorkflowsApiEmailContactDto
	// 														): Promise<ValidationErrors | undefined> {
	// 															return { fields: {} }
	// 														}}
	// 													/>
	// 												)}
	// 											</ArrayFieldContainer>
	// 										);
	// 									})}
	// 							</MainCardContainer>
	// 						)}
	// 					</form.AppField>
	// 				</Grid>
	// 				<Grid size={12}>
	// 					<form.AppField
	// 						name="emails"
	// 						mode="array">
	// 						{({ pushValue, removeValue, state, replaceValue }) => (
	// 							<MainCardContainer
	// 								title="Emails"
	// 								icon={<Mail />}
	// 								color="primary"
	// 								emphasis="low"
	// 								primaryActionType="Add"
	// 								onPrimaryAction={() => pushValue(emailFormOptions().defaultValues)}
	// 								data-testid="emails.container">
	// 								{state.value.length > 0 &&
	// 									state.value.map((_, i) => {
	// 										return (
	// 											<ArrayFieldContainer
	// 												key={i}
	// 												initialEditState={true}
	// 												items={formatEmailSummary(state.value[i])}
	// 												title="Email">
	// 												{({ setEdit }) => (
	// 													<EmailForm
	// 														formValues={state.value[i]}
	// 														onAdd={(data) => {
	// 															replaceValue(i, data);
	// 															setEdit(false);
	// 														}}
	// 														onCancel={(shouldRemove) => {
	// 															if (shouldRemove) {
	// 																removeValue(i);
	// 															}
	// 															setEdit(false);
	// 														}}
	// 														onDelete={() => {
	// 															removeValue(i);
	// 															setEdit(false);
	// 														}}
	// 														onValidate={function (
	// 															_data: EthosWorkflowsApiEmailContactDto
	// 														): Promise<ValidationErrors | undefined> {
	// 															return { fields: {} }
	// 														}}
	// 													/>
	// 												)}
	// 											</ArrayFieldContainer>
	// 										);
	// 									})}
	// 							</MainCardContainer>
	// 						)}
	// 					</form.AppField>
	// 				</Grid>
	// 				<Grid size={12}>
	// 					<form.AppField
	// 						name="addresses"
	// 						mode="array">
	// 						{({ pushValue, removeValue, state, replaceValue }) => (
	// 							<MainCardContainer
	// 								title="Addresses"
	// 								icon={<MapPin />}
	// 								color="primary"
	// 								emphasis="low"
	// 								primaryActionType="Add"
	// 								onPrimaryAction={() => pushValue(addressFormOptions().defaultValues)}
	// 								data-testid="addresses.container">
	// 								{state.value.length > 0 &&
	// 									state.value.map((_, i) => {
	// 										const hasValues =
	// 											JSON.stringify(state.value[i]) !==
	// 											JSON.stringify(addressFormOptions().defaultValues);
	// 										return (
	// 											<ArrayFieldContainer
	// 												key={i}
	// 												initialEditState={true}
	// 												items={[]}
	// 												title="Address">
	// 												{({ setEdit }) => (
	// 													<AddressForm
	// 														formValues={state.value[i]}
	// 														onAdd={(data) => {
	// 															replaceValue(i, data);
	// 															setEdit(false);
	// 														}}
	// 														onCancel={() => {
	// 															if (!hasValues) {
	// 																removeValue(i);
	// 															}
	// 															setEdit(false);
	// 														}}
	// 														onDelete={() => {
	// 															removeValue(i);
	// 															setEdit(false);
	// 														}}
	// 														addressUse="Physical"
	// 														addressUseRefValue={0}
	// 													/>
	// 												)}
	// 											</ArrayFieldContainer>
	// 										);
	// 									})}
	// 							</MainCardContainer>
	// 						)}
	// 					</form.AppField>
	// 				</Grid>
	// 				{/* <Grid size={12}>
	// 					<form.AppField
	// 						name="legalDocuments"
	// 						mode="array">
	// 						{({ pushValue, removeValue, state, replaceValue }) => (
	// 							<MainCardContainer
	// 								title="Legal Documents"
	// 								icon={<FileText />}
	// 								color="primary"
	// 								emphasis="low"
	// 								primaryActionType="Add"
	// 								onPrimaryAction={() => pushValue([])}
	// 								data-testid="legalDocuments.container">
	// 								{state.value.length > 0 &&
	// 									state.value.map((_, i) => {
	// 										return (
	// 											<ArrayFieldContainer
	// 												key={i}
	// 												initialEditState={true}
	// 												items={[]}
	// 												title="Legal Document">
	// 												{({ setEdit }) => (
	// 													<LegalDocumentForm
	// 														workflowId={workflowId}
	// 														formValues={state.value[i]}
	// 														onAdd={(data) => {
	// 															replaceValue(i, data);
	// 															setEdit(false);
	// 														}}
	// 														onCancel={() => {
	// 															if (!hasValues) {
	// 																removeValue(i);
	// 															}
	// 															setEdit(false);
	// 														}}
	// 														onDelete={() => {
	// 															removeValue(i);
	// 															setEdit(false);
	// 														}}
	// 													/>
	// 												)}
	// 											</ArrayFieldContainer>
	// 										);
	// 									})}
	// 							</MainCardContainer>
	// 						)}
	// 					</form.AppField>
	// 				</Grid> */}
	// 			</Grid>
	// 		</CardContent>
	// 		<CardFooter sx={{ justifyContent: 'flex-end' }}>
	// 			<form.Subscribe selector={({ isDirty }) => ({ isDirty })}>
	// 				{({ isDirty }) => (
	// 					<FormFooter
	// 						onCancel={() => {
	// 							form.reset();
	// 							onCancel?.(!hasValues);
	// 						}}
	// 						onSubmit={() => onAdd?.(values)}
	// 						isUpdate={hasValues}
	// 						isDirty={isDirty}
	// 						onDelete={onDelete}
	// 					/>
	// 				)}
	// 			</form.Subscribe>
	// 		</CardFooter>
	// 	</Card>
	// );
}
