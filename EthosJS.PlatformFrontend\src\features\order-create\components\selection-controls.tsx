import { Autocomplete, Stack, TextField } from '@mui/material';
import { filter, find } from 'lodash';
import { postApiStudySearchOptions } from '@client/workflows/@tanstack/react-query.gen';

import { useQuery } from '@tanstack/react-query';
import useOrderSearch from '../hooks/use-order-search';
import { useEffect } from 'react';

interface SelectionControlsProps {
	patientId: string;
	orderId: string;
	studyId?: string;
	insuranceId?: string;
	onChange: (selectedIds: { orderId: string; studyId?: string }) => void;
}

export default function SelectionControls({
	patientId,
	orderId,
	studyId,
	onChange,
}: SelectionControlsProps) {
	const { data: orders } = useOrderSearch({ patientId });
	const { data: studies } = useQuery({
		...postApiStudySearchOptions({
			responseType: 'json',
			// TODO: Since no data, just removed it
			// body: Query.literal(
			// 	StudyQuery.withOrderId(orderId)
			// ) as unknown as EthosModelQueryDto1EthosModelStudyQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!orderId,
		initialData: {
			items: [],
		},
		select: (data) => filter(data?.items, { orderId }) ?? [],
	});

	useEffect(() => {
		if (studies?.length > 1 && !studyId) {
			onChange({ orderId, studyId: studies[0].id });
		}
	}, [onChange, orderId, studies, studyId]);

	const selectedOrder = find(orders, { id: orderId }) ?? null;
	const selectedStudy = find(studies, { id: studyId }) ?? null;

	if (!orders.length) {
		return null;
	}

	return (
		<Stack
			direction="column"
			gap={2}
			sx={{ py: 2 }}>
			<Autocomplete
				sx={{ minWidth: 200 }}
				//@ts-expect-error Type Mismatch
				value={selectedOrder ?? null}
				onChange={(_, newValue) => {
					onChange({ orderId: newValue.id, studyId });
				}}
				disableClearable
				renderInput={(params) => (
					<TextField
						{...params}
						label="Order Id"
					/>
				)}
				options={orders}
				getOptionLabel={(option) => option.careLocationId}
				filterOptions={(x) => x}
			/>
			<Autocomplete
				sx={{ minWidth: 200 }}
				value={selectedStudy ?? null}
				onChange={(_, newValue) => {
					onChange({ orderId, studyId: newValue?.id });
				}}
				renderInput={(params) => (
					<TextField
						{...params}
						label="Study Id"
					/>
				)}
				options={studies}
				getOptionLabel={(option) => option.id}
				filterOptions={(x) => x}
			/>
		</Stack>
	);
}
