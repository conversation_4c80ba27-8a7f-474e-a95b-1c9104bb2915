import { Edit } from "@mui/icons-material";
import { Box, darken, IconButton, lighten, styled, Typography, SxProps, Theme, Chip } from "@mui/material";
import IconContainer from "./icon-container";
import { object } from "zod";


const Container = styled(Box)<{ withBorder: boolean }>(({ theme, withBorder }) => ({
    padding: theme.spacing(2),
    display: 'flex',
    gap: theme.spacing(1),
    backgroundColor: lighten(theme.palette.primary.light, 0.85),
    color: darken(theme.palette.primary.dark, 0.5),
    position: 'relative',
    border: withBorder ? `1.5px solid ${lighten(theme.palette.primary.main, 0.85)}` : 'none',
    borderRadius: withBorder ? theme.spacing(1) : 0,
}))


interface InfoSummaryProps {
    values: Record<string, string>;
    sx?: SxProps<Theme>;
    action?: React.ReactNode;
}

export default function InfoSummary({ sx, values, action }: InfoSummaryProps) {
    return (
        <Container withBorder={true} sx={sx}>
            {Object.keys(values).map((key) => (<Chip key={key} label={<><Box component="span" sx={{ fontWeight: 'bold' }}>{key}</Box>{values[key]}</>} />))}
            {
                action && <Box sx={{ mr: -1, mt: -1 }}>
                    {action}
                </Box>
            }
        </Container>
    )
}