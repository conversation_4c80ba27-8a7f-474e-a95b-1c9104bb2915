import StepCardControl from '@components/step-card-control';
import { Box, Button, CardContent } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { formOptions, useStore } from '@tanstack/react-form';
import { FormProps } from '@components/forms/predefined-form-props';
import { GuardiansData } from '../types';
import { useMemo } from 'react';
import GuardianForm, { guardianFormOptions } from '@components/forms/app-guardian-form';
import dayjs from 'dayjs';
import ArrayFormContainer from '@components/forms/app-array-form-container';
import { getErrorsForIndexField } from '@utils/forms';
import { HandHeart } from 'lucide-react';
import { ChipData } from '@components/chip-summary';
import { formatEmailSummary, formatPhoneNumberSummary } from './formatters';
import { formatAddressSummary } from '@components/forms/app-address-form';
import { map } from 'lodash';
import { guardiansTransformer } from './transformers';

const defaultValues: GuardiansData = {
	guardians: [],
};

function guardiansFormOptions(savedData?: GuardiansData) {
	return formOptions({
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	});
}

export default function GuardiansForm({
	onSubmit,
	onSaveDraft,
	onValidate,
	savedData,
	patientDob,
	isUpdate,
}: FormProps<GuardiansData> & { patientDob: string }) {
	const options = useMemo(() => guardiansFormOptions(savedData), [savedData]);
	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(value);
				return res;
			},
		},
		onSubmit: async ({ value }) => {
			const vals = {
				guardians: map(value.guardians, (guardian) => guardiansTransformer(guardian)) ?? [],
			};
			onSubmit(vals);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	const isUnder18 = dayjs().diff(dayjs(patientDob), 'year') < 18;

	return (
		<Box
			component="form"
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
				<form.AppField
					name="guardians"
					mode="array">
					{({ pushValue, removeValue, state, replaceValue }) => {
						return (
							<ArrayFormContainer
								pushValue={pushValue}
								removeValue={removeValue}
								replaceValue={replaceValue}
								dataTestId="guardians"
								mainCardContainerProps={{
									title: 'Guardians',
									icon: <HandHeart />,
								}}
								isUpdate={isUpdate}
								state={{
									value: state.value ?? [],
								}}
								onValidate={async (data, index) => {
									const guardians =
										state.value?.map((_, j) => {
											if (j === index) {
												return data;
											}
											return state.value?.[j];
										}) ?? [];
									const vals = {
										guardians,
									} as GuardiansData;
									const res = await onValidate(vals);
									return getErrorsForIndexField(`guardians[${index}]`, res);
								}}
								renderForm={function (props) {
									return <GuardianForm {...props} />;
								}}
								tabContainerHeaderProps={(data) => {
									const { names } = data;
									const firstName = names?.[0]?.firstName ?? '';
									let fullName = firstName;
									const middleName = names?.[0]?.middleName ?? '';
									if (middleName) {
										fullName += ` ${middleName}`;
									}
									const lastName = names?.[0]?.lastName ?? '';
									if (lastName) {
										fullName += ` ${lastName}`;
									}
									return {
										title: fullName,
									};
								}}
								defaultValues={guardianFormOptions().defaultValues}
								displayMode="tab"
								tabItems={[
									{
										name: 'Basic Details',
										value: 'basic-details',
										formatTabSummary: (data) => {
											if (!data) {
												return [];
											}
											const { names, demographics } = data;
											const nameChips =
												names?.map((name) => {
													return {
														label: 'Name',
														value: `${name.firstName} ${name.lastName}`,
													};
												}) ?? [];
											const { gender, dateOfBirth, ethnicity } = demographics ?? {};
											return [
												...nameChips,
												{
													label: 'Gender',
													value: gender?.toString() ?? 'N/A',
													hideSeperator: true,
												},
												{
													label: 'Date of Birth',
													value: dateOfBirth ? dayjs(dateOfBirth).format('MM/DD/YYYY') : 'N/A',
													hideSeperator: true,
												},
												{
													label: 'Ethnicity',
													value: ethnicity?.toString() ?? 'N/A',
													hideSeperator: true,
												},
											] as Array<ChipData>;
										},
									},
									{
										name: 'Contact Info',
										value: 'contact-info',
										mode: 'array',
										formatTabSummary: (data) => {
											if (!data) {
												return [];
											}
											const resolvedData = data.contactInformation;
											const phoneNumbersChips =
												resolvedData?.phoneNumbers.map((phone) => {
													return formatPhoneNumberSummary(phone);
												}) ?? [];
											const emailsChips =
												resolvedData?.emails.map((email) => {
													return formatEmailSummary(email);
												}) ?? [];
											return [...phoneNumbersChips, ...emailsChips];
										},
									},
									{
										name: 'Address',
										value: 'address',
										mode: 'array',
										formatTabSummary: (data) => {
											if (!data) {
												return [];
											}
											const { contactInformation } = data;
											return (
												contactInformation?.addresses.map((address) => {
													return formatAddressSummary(address);
												}) ?? []
											);
										},
									},
								]}
							/>
						);
					}}
				</form.AppField>
			</CardContent>
			<StepCardControl>
				{!isUpdate && (
					<Button
						variant="outlined"
						color="primary"
						data-testid="guardians.saveDraftButton"
						onClick={() => onSaveDraft(values)}>
						Save Draft
					</Button>
				)}
				<form.Subscribe
					selector={({ isDirty, canSubmit, isSubmitting }) => ({
						isDirty,
						canSubmit,
						isSubmitting,
					})}>
					{({ isDirty, canSubmit, isSubmitting }) => {
						return (
							<Button
								variant="contained"
								color="primary"
								type="submit"
								loading={isSubmitting}
								data-testid="guardians.submitButton"
								disabled={!isDirty || (!canSubmit && isUnder18)}>
								{isUpdate ? 'Update' : 'Next'}
							</Button>
						);
					}}
				</form.Subscribe>
			</StepCardControl>
		</Box>
	);
}
