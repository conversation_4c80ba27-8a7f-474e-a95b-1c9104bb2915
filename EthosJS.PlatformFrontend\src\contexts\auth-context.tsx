import { createContext, useContext, useState, useEffect, PropsWithChildren } from 'react';
import { useMsal, useAccount } from '@azure/msal-react';

interface AuthContextType {
	roles: string[];
	scopes: string[];
	hasRole: (role: string) => boolean;
	hasScope: (scope: string) => boolean;
	hasAnyRole: (roles: string[]) => boolean;
	hasAnyScope: (scopes: string[]) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: PropsWithChildren) => {
	const { instance } = useMsal();
	const account = useAccount();
	const [roles, setRoles] = useState<string[]>([]);
	const [scopes, setScopes] = useState<string[]>([]);

	useEffect(() => {
		const extractUserInfo = async () => {
			// if (account) {
			// 	// Extract roles from ID token claims
			// 	const idTokenClaims = account.idTokenClaims as any;
			// 	const userRoles = idTokenClaims?.roles || [];
			// 	// Get token and extract scopes
			// 	try {
			// 		const response = await instance.acquireTokenSilent({ account });
			// 		// Scopes are typically in the access token
			// 		const userScopes = response.scopes || [];
			// 		setRoles(userRoles);
			// 		setScopes(userScopes);
			// 	} catch (error) {
			// 		console.error('Failed to get token:', error);
			// 	}
			// }
		};

		extractUserInfo();
	}, [account, instance]);

	const hasRole = (role: string) => roles.includes(role);
	const hasScope = (scope: string) => scopes.includes(scope);
	const hasAnyRole = (roleList: string[]) => roleList.some((role) => roles.includes(role));
	const hasAnyScope = (scopeList: string[]) => scopeList.some((scope) => scopes.includes(scope));

	return (
		<AuthContext.Provider value={{ roles, scopes, hasRole, hasScope, hasAnyRole, hasAnyScope }}>
			{children}
		</AuthContext.Provider>
	);
};

export const useAuth = () => {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error('useAuth must be used within an AuthProvider');
	}
	return context;
};
