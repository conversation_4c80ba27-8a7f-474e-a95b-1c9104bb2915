import {
	EthosModelQueryDto1EthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
	EthosWorkflowsApiCreateStudyDto,
} from '@client/workflows';
import { postApiInsuranceSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import LoadingComponent from '@components/loading-component';
import MainCardContainer from '@components/main-container/main-card-container';
import StepCardControl from '@components/step-card-control';
import AssociatedInsurances from '@features/order-create/forms/associated-insurance-card';
import useOrder from '@features/order-create/hooks/use-order';
import useStudy from '@features/order-create/hooks/use-study';
import { OrderState, StepProps } from '@features/order-create/types';
import { useAppForm } from '@hooks/app-form';
import { Box, Button } from '@mui/material';
import { formOptions } from '@tanstack/react-form';
import { useQuery } from '@tanstack/react-query';
import { InsuranceQuery, Query } from '@utils/query-dsl';
import dayjs from 'dayjs';
import { Shield } from 'lucide-react';
import { useMemo } from 'react';

const defaultValues = {
	insurances: [] as EthosWorkflowsApiCreateStudyDto['insurances'],
};

const associatedInsuranceFormOptions = (
	savedData?: EthosWorkflowsApiCreateStudyDto['insurances']
) => {
	return formOptions({
		defaultValues: {
			...defaultValues,
			...(savedData ? { insurances: savedData } : {}),
		},
	});
};

interface AssociatedInsuranceStepProps extends StepProps {
	studyId: string;
}

export default function AssociatedInsuranceStep({
	patientId,
	orderId,
	studyId,
	successCallback,
}: AssociatedInsuranceStepProps) {
	const { orderData, updateOrder } = useOrder({ orderId });
	const { studyData, updateStudy } = useStudy({
		studyId,
	});

	const onSuccessUpdateStudy = () => {
		const { data } = orderData ?? {};
		const orderState = (data?._state as unknown as OrderState) ?? {};
		const { flowState, stepState } = orderState;
		updateOrder(
			{
				...data,
			},
			{
				flowState: {
					...flowState,
					status: 'InProgress',
					progress: 50,
					lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
				},
				stepState: {
					...stepState,
					AddAssociatedInsurances: 'Complete',
					ReviewAndSubmitOrder: 'InProgress',
				},
			},
			successCallback
		);
	};

	const onSubmit = (data: Partial<EthosWorkflowsApiCreateStudyDto>) => {
		updateStudy(
			{
				...studyData?.data,
				...data,
			},
			onSuccessUpdateStudy
		);
	};

	const options = useMemo(
		() => associatedInsuranceFormOptions(studyData?.data?.insurances),
		[studyData?.data?.insurances]
	);
	console.log('options', options);
	const form = useAppForm({
		...options,
		onSubmit: async ({ value }) => {
			onSubmit(value);
		},
	});

	const {
		data: insuranceData,
		isFetching: isFetchingInsuranceData,
		error: fetchInsuranceError,
	} = useQuery({
		...postApiInsuranceSearchOptions({
			responseType: 'json',
			body: Query.literal(
				InsuranceQuery.withPatientId(patientId!)
			) as unknown as EthosModelQueryDto1EthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!patientId,
		initialData: {
			items: [],
		},
		select: (data) => data?.items ?? [],
	});

	if (fetchInsuranceError) {
		return <div>Error fetching insurance data</div>;
	}

	if (isFetchingInsuranceData) {
		return <LoadingComponent />;
	}

	return (
		<Box
			sx={{
				flex: 1,
				overflow: 'auto',
				height: 'calc(100% - 67px)',
				pb: 0,
			}}
			component="form"
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<MainCardContainer
				title="Associated Insurances"
				color="primary"
				emphasis="high"
				icon={<Shield />}
				descriptionSubheader="Select the appropriate insurance for the study.">
				<AssociatedInsurances
					form={form}
					insurances={insuranceData}
				/>
			</MainCardContainer>
			<StepCardControl>
				<Button
					variant="outlined"
					color="primary">
					Save Draft
				</Button>
				<form.Subscribe>
					{({ isDirty, canSubmit, isSubmitting }) => (
						<Button
							variant="contained"
							color="primary"
							type="submit"
							loading={isSubmitting}
							disabled={!isDirty || !canSubmit}>
							Next
						</Button>
					)}
				</form.Subscribe>
			</StepCardControl>
		</Box>
	);
}
