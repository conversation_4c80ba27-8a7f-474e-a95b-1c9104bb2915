import CardHeader from "@components/card-header";
import { <PERSON>, <PERSON><PERSON>ontent, Divider, Stack, Typography } from "@mui/material";

import Card from "@components/card";
import styles from "@components/technician-tabs/styles";
import Summary from "@components/technician-tabs/summary";

type PhoneNumber = {
   phoneType: string
   phoneNumber: string
   contactMethod: string
   preferredTime: string
}

type EmailAddress = {
   emailType: string
   emailAddress: string
   sameAs: string
}

type PhysicalAddress = {
   type: string
}

type BillingAddress = {
   type: string
}

type DeliveryAddress = {
   sameAs?: string
}

interface PatientInfoProp {
   title?: string
   values: {
      phoneNumbers: Array<{
         prefered: boolean,
         data: Partial<PhoneNumber>
      }>
      emailAddresses: Array<{
         prefered: boolean,
         data: EmailAddress
         extra: Array<{ label?: string, value?: string }>
      }>
      physicalAddresses: Array<{
         prefered: boolean,
         data: PhysicalAddress
         extra: Array<{ label?: string, value?: string }>
      }>
      billingAddresses: Array<{
         prefered: boolean,
         data: BillingAddress
         extra: Array<{ label?: string, value?: string }>
      }>
      deliveryAddresses: Array<{
         prefered: boolean,
         data: DeliveryAddress
         extra: Array<{ label?: string, value?: string }>
      }>
   }
}

const Contact = (props: PatientInfoProp) => {

   const { title, values: { phoneNumbers = [], emailAddresses = [], physicalAddresses = [], billingAddresses = [], deliveryAddresses = [] } } = props;

   return (
      <Card>
         <CardHeader
            {...{
               title: <Typography>{title}</Typography>,
               emphasis: 'dark'
            }}
         />
         <Box sx={styles.cardBody}>
            <Stack gap={2}>
               <Stack>
                  <Card sx={{ borderBottomLeftRadius: 0, borderBottomRightRadius: 0 }}>
                     <CardHeader
                        {...{
                           title: <Typography>{'Phone Number (2)'}</Typography>,
                        }}
                     />
                     <CardContent>
                        <Stack gap={2}>
                           {phoneNumbers?.map(({ data, prefered }, index) => {

                              let source = [

                                 {
                                    label: 'Phone Type',
                                    value: data?.phoneType as string,
                                 },
                                 {
                                    label: 'Phone Number',
                                    value: data?.phoneNumber as string
                                 },
                                 {
                                    label: 'Contact Method',
                                    value: data?.contactMethod as string
                                 },
                                 {
                                    label: 'Preferred Time',
                                    value: data?.preferredTime as string
                                 },
                              ];

                              if (prefered) {
                                 source = [
                                    {
                                       label: 'Preferred',
                                       skipSeperator: true,
                                       highlighted: true
                                    },
                                    ...source
                                 ] as any
                              }

                              return (
                                 <>
                                    <Summary
                                       key={index}
                                       data={source}
                                    />
                                    {index < phoneNumbers?.length - 1 && <Divider />}
                                 </>
                              )
                           })}
                        </Stack>
                     </CardContent>
                  </Card>
                  <Card sx={{ borderRadius: 0 }}>
                     <CardHeader
                        {...{
                           title: <Typography>{'E-Mail Address'}</Typography>,
                        }}
                     />
                     <CardContent>
                        {emailAddresses?.map(({ data, extra }) => {

                           let source = [
                              {
                                 label: 'E-Mail Type',
                                 value: data?.emailType as string
                              },
                              {
                                 label: 'E-Mail Address',
                                 value: data?.emailAddress as string
                              },
                              {
                                 label: 'Same As',
                                 value: data?.sameAs as string
                              },
                           ]

                           if (extra?.length) {
                              source = source.concat(extra as any)
                           }

                           return (
                              <Summary
                                 data={source}
                              />
                           )
                        })}
                     </CardContent>
                  </Card>
                  <Card sx={{ borderRadius: 0 }}>
                     <CardHeader
                        {...{
                           title: <Typography>{'Physical Address (3)'}</Typography>,
                        }}
                     />
                     <CardContent>
                        <Stack gap={2}>
                           {physicalAddresses?.map(({ data, extra }) => {

                              let source = [
                                 {
                                    label: 'Address Type',
                                    value: data?.type as string,
                                 },
                              ]

                              if (extra?.length) {
                                 source = source.concat(extra as any)
                              }

                              return (
                                 <>
                                    <Summary
                                       data={source}
                                    />
                                    <Divider />
                                 </>
                              )
                           })}
                        </Stack>
                     </CardContent>
                  </Card>

                  <Card sx={{ borderRadius: 0 }}>
                     <CardHeader
                        {...{
                           title: <Typography>{'Billing Address'}</Typography>,
                        }}
                     />
                     <CardContent>
                        <Stack gap={2}>
                           {billingAddresses?.map(({ data, extra }) => {

                              let source = [
                                 {
                                    label: 'Address Type',
                                    value: data?.type as string,
                                 },
                              ]

                              if (extra?.length) {
                                 source = source.concat(extra as any)
                              }

                              return (
                                 <>
                                    <Summary
                                       data={source}
                                    />
                                    <Divider />
                                 </>
                              )
                           })}
                        </Stack>
                     </CardContent>
                  </Card>

                  <Card sx={{ borderRadius: 0 }}>
                     <CardHeader
                        {...{
                           title: <Typography>{'Delivery Address'}</Typography>,
                        }}
                     />
                     <CardContent>
                        <Stack gap={2}>
                           {deliveryAddresses?.map(({  extra }) => {

                              return (
                                 <>
                                    <Summary
                                       data={extra}
                                    />
                                    <Divider />
                                 </>
                              )
                           })}
                        </Stack>
                     </CardContent>
                  </Card>

               </Stack>
            </Stack>
         </Box>
      </Card>
   )
};

export default Contact;