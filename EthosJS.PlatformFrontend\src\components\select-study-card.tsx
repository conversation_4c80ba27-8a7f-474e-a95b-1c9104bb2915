import { BorderColor, SvgIconComponent, TextSnippetOutlined } from "@mui/icons-material";
import { Box, lighten, styled, Theme, Typography, RadioProps, Badge, Stack, Radio, Chip } from "@mui/material";
import { light } from "@mui/material/styles/createPalette";
import { ClockIcon } from "@mui/x-date-pickers-pro";


const selectedMixin = (theme: Theme) => ({
    borderColor: theme.palette.primary.main,
    borderWidth: 2,
})

type StyleProps = { checked?: boolean }

const Container = styled(Box, { shouldForwardProp: prop => prop !== 'checked' })<StyleProps>(({ theme, checked }) => ({
    position: "relative",
    width: "100%",
    padding: theme.spacing(1.5),
    border: `1px solid ${lighten(theme.palette.primary.light, 0.8)}`,
    borderRadius: theme.spacing(1),
    color: theme.palette.primary.main,
    backgroundColor: lighten(theme.palette.primary.light, 0.9),
    "&:hover": {
        cursor: "pointer",
        backgroundColor: theme.palette.action.hover,
        "& #radio-button-box": {
            display: 'block',
        }
    },
    ...(checked && selectedMixin(theme)),
    borderLeftWidth: 11,
}));

const RadioButtonBox = styled(Box, { shouldForwardProp: prop => prop !== 'checked' })<StyleProps>(({ theme, checked }) => ({
    position: "absolute",
    right: 0,
    top: 0,
    padding: theme.spacing(0.5),
    display: checked ? 'block' : 'none',
}));

interface SelectStudyCardProps extends RadioProps {
    title: string;
    description: string;
    chipLabel: string;
    chipIcon?: SvgIconComponent;
    cptCode?: string;
    duration?: string;
    onClick?: () => void;
}

export default function SelectStudyCard({
    title,
    description,
    cptCode,
    duration,
    chipLabel,
    chipIcon: ChipIcon,
    checked,
    ...radioProps
}: SelectStudyCardProps) {
    return (
        <Container checked={checked}>
            <Stack direction="row" justifyContent="flex-start" alignItems="center" spacing={1}>
                <Typography variant="h6">{title}</Typography>
                <Chip
                    label={chipLabel}
                    size="small"
                    variant="outlined"
                    sx={{
                        borderRadius: 1,
                    }}
                    {
                    ...(ChipIcon && { icon: <ChipIcon /> })
                    }
                />
            </Stack>
            <Typography variant="body2">
                {description}
            </Typography>
            <Stack direction="row" justifyContent="flex-start" alignItems="center" spacing={1}>
                {
                    duration && <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', lineHeight: 1 }}>
                        <ClockIcon sx={{ mr: 1 }} /> {duration}
                    </Typography>
                }
                {
                    cptCode && <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', lineHeight: 1 }}>
                        <TextSnippetOutlined sx={{ mr: 1 }} /> CPT: {cptCode}
                    </Typography>
                }
            </Stack>
            <RadioButtonBox id="radio-button-box" checked={checked}>
                <Radio {...radioProps} checked={checked} />
            </RadioButtonBox>
        </Container>
    );
}