import { createFileRoute, redirect } from '@tanstack/react-router';
import PhysiciansStep from '@features/order-create/components/steps/physicians.step';

export const Route = createFileRoute('/_dashboard/patients/$patientId/order/physicians')({
	component: RouteComponent,
	loaderDeps: ({ search }) => ({ ...search }),
	beforeLoad: ({ params, search }) => {
		const { patientId } = params;
		const { orderId } = search;
		if (!orderId) {
			throw redirect({
				to: '/patients/$patientId/order/study',
				params: { patientId: patientId },
				search,
			});
		}
	},
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const navigate = Route.useNavigate();

	return (
		<PhysiciansStep
			patientId={patientId}
			orderId={orderId!}
			successCallback={() => {
				navigate({
					to: '/patients/$patientId/order/confirmation',
					params: { patientId },
					search: { orderId, studyId },
				});
			}}
		/>
	);
}
