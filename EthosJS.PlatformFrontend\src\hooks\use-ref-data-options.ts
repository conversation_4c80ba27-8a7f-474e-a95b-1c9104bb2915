import { GetApiReferenceSetsValuesResponses, ReferenceDataSetKeyValueDto } from '@client/refdata';
import { getApiReferenceSetsValuesOptions } from '@client/refdata/@tanstack/react-query.gen';
import { useQuery } from '@tanstack/react-query';
import { map } from 'lodash';

type RefDataResponse = Omit<GetApiReferenceSetsValuesResponses, 'items'> & {
	items: Array<ReferenceDataSetKeyValueDto & { attributeKey: string }>;
};
function convertRefData(data: RefDataResponse | undefined) {
	if (!data) return [];
	return map(data.items, (item) => {
		const { key, values } = item;
		const { value } = (key as { value: string | null }) ?? { value: null };
		const { description } = (values as { description: string }) ?? { description: null };
		return {
			title: value ?? 'Unknown',
			description: description?.toString() ?? '',
			value: item.id?.toString() ?? '',
		};
	});
}

export function useRefDataOptions({ setName, filter }: { setName: string; filter?: string }) {
	const {
		data: options,
		isFetching,
		error,
	} = useQuery({
		...getApiReferenceSetsValuesOptions({
			responseType: 'json',
			query: { setName, filter },
		}),
		select: (data) => convertRefData(data as RefDataResponse),
	});

	return {
		options,
		isFetching,
		error,
	};
}
