import { Box } from '@mui/material';
import { createFileRoute, Outlet, useLocation } from '@tanstack/react-router';
import { z } from 'zod';
import SchedulingMenu from '@features/scheduling/components/menu';
import Card from '@components/card';

const validateSearch = z.object({
	orderId: z.string().optional(),
	studyId: z.string().optional(),
});

export const Route = createFileRoute('/_dashboard/patients/$patientId/schedule')({
	component: RouteComponent,
	validateSearch,
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const { orderId, studyId } = Route.useSearch();
	const navigate = Route.useNavigate();
	const { pathname } = useLocation();

	// const { store, actions } = useScheduleStore();

	// const { steps } = useStore(store, (state) => state);

	// const onNavigateStep = (selectedStep: (typeof scheduleSteps)[number]) => {
	// 	const search = { orderId: orderId, studyId: studyId };
	// 	navigate({
	// 		to: selectedStep.navigationURL,
	// 		params: { patientId },
	// 		search,
	// 	});
	// };

	// const onFieldChange = (fieldName: string, fieldValue: string) => {
	// 	navigate({
	// 		to: '/patients/$patientId/schedule/appointment-creation/dashboard',
	// 		params: { patientId },
	// 		search: (prev) => ({ ...prev, [fieldName]: fieldValue }),
	// 	});
	// };

	return (
		<Card
			sx={{
				flex: 1,
				minHeight: 0,
				display: 'flex',
				gap: 2,
				p: 2,
				pb: 0,
				borderRadius: 2,
				borderBottomLeftRadius: 0,
				borderBottomRightRadius: 0,
			}}>
			<SchedulingMenu
				patientId={patientId}
				orderId={orderId!}
				studyId={studyId!}
				activePath={pathname}
				onClick={(path) => {
					navigate({
						to: `/patients/$patientId/schedule${path}`,
						params: { patientId },
						search: { orderId, studyId },
					});
				}}
				onSelect={({ orderId, studyId }) => {
					navigate({
						to: '/patients/$patientId/schedule/appointment-creation/dashboard',
						params: { patientId },
						search: { orderId, studyId },
					});
				}}
			/>
			<Box sx={{ flex: 1, overflow: 'auto', height: '100%' }}>
				<Outlet />
			</Box>
		</Card>
	);
}
