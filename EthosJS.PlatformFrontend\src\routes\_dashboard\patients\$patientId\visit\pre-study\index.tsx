import { createFileRoute } from '@tanstack/react-router';
import { z } from 'zod';
import Steps from '@features/visits/components/-steps';

const validateSearch = z.object({
	orderId: z.string(),
	studyId: z.string().optional(),
});

export const Route = createFileRoute('/_dashboard/patients/$patientId/visit/pre-study/')({
	component: RouteComponent,
	validateSearch,
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const search = Route.useSearch();
	const navigate = Route.useNavigate();

	const onCancel = () => {
		navigate({
			to: '/patients/$patientId/visit',
			params: { patientId },
			search,
		});
	};

	return (
		<Steps
			studyId={search.studyId as string}
			patientId={patientId}
			onCancel={onCancel}
		/>
	);
}
