import { ValidationErrors } from '@app-types/validation';

/**
 * Interface for workflow form components
 */
export interface PredefinedFormProps<T extends Record<string, any>> {
	onSubmit: (data: T) => void;
	onSaveDraft: (data: T) => void;
	onValidate: (data: T) => Record<string, any> | undefined;
	savedData?: T;
	workflowId?: string;
	isLoading: boolean;
}

export interface FormProps<T> {
	onSubmit: (data: T) => void;
	onSaveDraft: (data: T) => void;
	savedData?: T;
	onValidate: (data: T) => Promise<ValidationErrors | undefined>;
	isUpdate?: boolean;
}
/**
 * Generic interface for form components that follow a standard pattern
 * of adding, canceling, deleting, and managing form values.
 *
 * @template T The type of form data being managed
 */
export interface GenericFormProps<T> {
	/**
	 * Callback function when form is submitted/added
	 * @param values The form values to be added
	 */
	onAdd?: (values: T) => void;

	/**
	 * Callback function when form is canceled
	 */
	onCancel?: () => void;

	/**
	 * Callback function when form data is deleted
	 */
	onDelete?: () => void;

	/**
	 * Current values of the form
	 */
	formValues: T;
}
