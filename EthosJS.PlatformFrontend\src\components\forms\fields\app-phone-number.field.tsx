import { useFieldContext } from '@hooks/form-context';
import { InputAdornment, TextField, TextFieldProps } from '@mui/material';
import { digitsOnly, maskPhoneNumber } from '@utils/maskers';
import { FieldPropType } from './FieldPropType';
import { Phone } from 'lucide-react';

export default function AppPhoneNumberField({
	label,
	required,
	name,
	dataTestId,
}: FieldPropType &
	Omit<TextFieldProps<'outlined'>, 'label' | 'required' | 'variant'> & { dataTestId?: string }) {
	const field = useFieldContext<string>();

	const shouldShowError = field.getMeta().isTouched || field.getMeta().isDirty;
	const errors = shouldShowError ? field.getMeta().errors.map((error) => error.message) : [];
	return (
		<TextField
			label={label}
			required={required}
			value={maskPhoneNumber(field.state.value as string)}
			onChange={(e) => field.handleChange(digitsOnly(e.target.value))}
			onBlur={field.handleBlur}
			error={shouldShowError && errors.length > 0}
			helperText={shouldShowError ? errors.join(', ') : undefined}
			fullWidth
			placeholder="(*************"
			slotProps={{
				input: {
					inputMode: 'tel',
					startAdornment: (
						<InputAdornment position="start">
							<Phone />
						</InputAdornment>
					),
					name,
					inputProps: {
						'data-testid': dataTestId,
					},
				},
			}}
		/>
	);
}
