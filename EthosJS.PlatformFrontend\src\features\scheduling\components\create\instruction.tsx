import List from "@components/list";
import MainCardContainer from "@components/main-container/main-card-container";
import StatusBanner from "@components/status.banner";
import { Status } from "@config/status";
import useCreateAppointmentStore from "@hooks/use-create-appointment-store";
import { PhoneAndroid } from "@mui/icons-material";
import { Stack, Typography } from "@mui/material";
import { useForm } from "@tanstack/react-form";
import { Mail, PrinterIcon } from "lucide-react";
import { useImperativeHandle } from "react";

interface Instructions {
   formRef: React.RefObject<ReturnType<typeof useForm> | null>
}

const Instructions = ({ formRef }: Instructions) => {
   return (
      <Stack gap={2}>
         <AttentionInfo />
         <Form formRef={formRef} />
      </Stack>
   )
}

const availableDocuments = [
   { id: 'passport', name: 'Passport', category: 'Identity', isRequired: true },
   { id: 'drivers_license', name: 'Driver\'s License', category: 'Identity', isRequired: true },
   { id: 'birth_certificate', name: 'Birth Certificate', category: 'Identity' },
   { id: 'social_security', name: 'Social Security Card', category: 'Identity' },
   { id: 'bank_statement', name: 'Bank Statement', category: 'Financial' },
   { id: 'tax_return', name: 'Tax Return', category: 'Financial' },
   { id: 'pay_stub', name: 'Pay Stub', category: 'Financial' },
   { id: 'utility_bill', name: 'Utility Bill', category: 'Proof of Address' },
   { id: 'lease_agreement', name: 'Lease Agreement', category: 'Proof of Address' },
   { id: 'insurance_policy', name: 'Insurance Policy', category: 'Legal' },
   { id: 'employment_letter', name: 'Employment Letter', category: 'Legal' },
   { id: 'medical_records', name: 'Medical Records', category: 'Medical' }
];

const documentDeliveryMethod = [
   {
      id: 'email',
      title: 'Email',
      isRecommended: true,
      subTitle: '<EMAIL>',
      icon: <Mail />
   },
   {
      id: 'SMS with download link ',
      title: 'SMS with download link ',
      subTitle: '<EMAIL>',
      icon: <PhoneAndroid />
   },
   {
      id: 'physical-copies',
      title: 'Physical copies',
      subTitle: '6563 McDonald Ave, Gig Harbor WA, 98335',
      icon: <PrinterIcon />
   }
];

type DefaultValueTypes = { documents: string[], documentDeliveryMethod: string[] };


const Form = ({ formRef }: Pick<Instructions, 'formRef'>) => {

   const { store, actions } = useCreateAppointmentStore();

   const form = useForm({
      defaultValues: {
         documents: store.state.values.documents,
         documentDeliveryMethod: store.state.values.documentDeliveryMethod,
      } as DefaultValueTypes,
      onSubmit({ value }) {
         actions.setValues(value);
         actions.moveNext();
      },
   });

   useImperativeHandle(formRef, () => form as unknown as ReturnType<typeof useForm>);

   return (
      <Stack
         gap={2}
         component={'form'}
      >
         <MainCardContainer
            title="Standard Documents to Include"
            primaryActionType="Add"
         >
            <form.Field
               name="documents"
               validators={{
                  onChange: ({ value }) => {
                     if (value.includes('drivers_license') && value.includes('passport')) return undefined;
                     return 'Select at least two required documents';
                  },
               }}
            >
               {(field) => {
                  return (
                     <>
                        <List<{ isRequired: boolean }>
                           items={availableDocuments.map((item) => {
                              return {
                                 title: item.name,
                                 value: item.id,
                                 meta: {
                                    isRequired: item.isRequired,
                                 }
                              }
                           })}
                           selectable={true}
                           selectMode='checkbox'
                           selectedItems={field.state.value}
                           renderTitle={(e) => (
                              <Stack direction={'row'} gap={1.5} alignItems={'center'}>
                                 <Typography variant="h6" color="primary.dark" >{e?.title}</Typography>
                                 {e?.meta?.isRequired && (
                                    <Typography variant="body2" color="warning.main" fontWeight={600} >{'*Required'}</Typography>
                                 )}
                              </Stack>
                           )}
                           onSelectItems={(e) => {
                              field.setValue(e as string[]);
                           }}
                        />
                        {field.state.meta.errors?.length ? (
                           <Typography role="alert" color="error.main" >{field.state.meta.errors.join(', ')}</Typography>
                        ) : null}
                     </>
                  )
               }}
            </form.Field>
         </MainCardContainer>
         <MainCardContainer title="Document Delivery Method">
            <form.Field
               name="documentDeliveryMethod"
               validators={{
                  onChange: ({ value }) => {
                     return !value.length ? 'Select at least one delivery method' : undefined;
                  },
               }}
            >
               {(field) => {
                  return (
                     <>
                        <List<{ isRecommended: boolean }>
                           items={documentDeliveryMethod.map((item) => {
                              return {
                                 title: item.title,
                                 subTitle: item.subTitle,
                                 value: item.id,
                                 icon: item.icon,
                                 meta: {
                                    isRecommended: item.isRecommended,
                                 }
                              }
                           })}
                           selectable={true}
                           selectMode='checkbox'
                           selectedItems={field.state.value}
                           renderTitle={(e) => (
                              <Stack direction={'row'} gap={1.5} alignItems={'center'}>
                                 <Typography variant="h6" color="primary.dark" >
                                    {e?.title} {e?.meta?.isRecommended && '(Recommended)'}
                                 </Typography>
                              </Stack>
                           )}
                           onSelectItems={(e) => {
                              field.setValue(e as string[]);
                           }}
                        />
                        {field.state.meta.errors?.length ? (
                           <Typography role="alert" color="error.main" >{field.state.meta.errors.join(', ')}</Typography>
                        ) : null}
                     </>
                  )
               }}
            </form.Field>
         </MainCardContainer>
      </Stack>
   )
}

const AttentionInfo = () => {
   return (
      <StatusBanner
         status={Status.Process}
         title={"Day of Study Instructions:"}
         titleProp={{
            variant: 'h6'
         }}
         renderContent={({ title }) => {
            const instructions = [
               'Please shower before your appointment and wash your hair. Do not apply hair products.',
               'Avoid caffeine, alcohol, and naps on the day of your study.',
               'Take all regular medications unless otherwise instructed by your physician.',
               'Bring comfortable sleepwear, any medications you need to take during your stay, and personal toiletries.',
               'Bring your insurance card and photo ID.',
               'Arrive 15 minutes early to complete paperwork.',
            ];
            return (
               <Stack component={'ul'} sx={{ color: title.foregroundColor, gap: .2, paddingInlineStart: 3, marginBlockStart: 0 }}>
                  {instructions.map((instruction, index) => {
                     return (
                        <Typography sx={{ fontWeight: 600 }} component={'li'} key={index}>
                           {instruction}
                        </Typography>
                     )
                  })}
               </Stack>
            )
         }}
      />
   )
}


export default Instructions;