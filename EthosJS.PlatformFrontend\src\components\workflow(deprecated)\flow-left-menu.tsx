import { Flow } from "src/workflows/Flow";
import LeftMenu from "../left-menu";
import { AccountTree } from "@mui/icons-material";
import { useStore } from "@tanstack/react-store";

export default function FlowLeftMenu({ onClick, flow, flowNumber }: { onClick: () => void; flow: Flow; flowNumber: number }) {
    const { status } = useStore(flow.getStore(), ({ status }) => ({
        status,
    }));

    // Determine if the flow is selected based on whether it has a selected step

    return (
        <LeftMenu
            name={flow.displayName}
            icon={AccountTree}
            status={status}
            stepNumber={flowNumber}
            onClick={onClick}
        />
    );
}