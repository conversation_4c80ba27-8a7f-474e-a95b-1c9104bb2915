import { Box, lighten, ListItem, ListItemProps, Typography, useTheme } from "@mui/material"
import { cloneElement, isValidElement, useId } from "react"

interface IMenuItem extends Partial<ListItemProps> {
   title: string
   leadingIcon?: React.ReactNode
   collapsed?: boolean
}

const MenuItem: React.FC<IMenuItem> = (props) => {

   const { palette } = useTheme();

   const { title, leadingIcon, collapsed, ...rest } = props;

   const prefix = useId();

   const textId = `${prefix}-title`;
   const iconSlotId = `${prefix}-leading-icon`;

   return (
      <ListItem
         {...rest}
         sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: 1,
            p: 1,
            borderRadius: 1,
            userSelect: 'none',
            cursor: 'pointer',
            background: palette.grey[50],
            border: '1px solid',
            borderColor: palette.grey[100],
            '&:hover': {
               backgroundColor: lighten(palette.primary.light, .8),
               borderColor: lighten(palette.primary.main, .7),
               color: palette.primary.main,
               [`& .${iconSlotId}`]: {
                  backgroundColor: lighten(palette.primary.main, .1),
                  borderColor: lighten(palette.primary.main, .7),
                  color: palette.common.white
               }
            },
            '&:active': {
               backgroundColor: lighten(palette.primary.main, .1),
               borderColor: lighten(palette.primary.main, .1),
               [`& .${iconSlotId}`]: {
                  borderColor: palette.common.white,
                  backgroundColor: palette.common.white,
                  color: lighten(palette.primary.main, .1)
               },
               [`& .${textId}`]: {
                  color: palette.common.white
               },
            },
         }}
      >
         <Box
            sx={{
               borderRadius: '0.25rem',
               background: palette.grey[100],
               display: 'flex',
               alignItems: 'center',
               width: '2rem',
               height: '2rem',
               justifyContent: 'center'
            }}
            className={iconSlotId}
         >
            {leadingIcon && isValidElement(leadingIcon) ?
               cloneElement(leadingIcon, {
                  style:
                     { width: '1.125rem', height: '1.125rem', flexShrink: 0 },
               } as any) : null}
         </Box>
         {!collapsed && (
            <Typography
               className={textId}
            >
               {title}
            </Typography>
         )}
      </ListItem>
   )
}


export type { IMenuItem };

export default MenuItem;