import { Box, CardContent, Grid, MenuItem, TextField } from "@mui/material";
import { LocalPhoneOutlined } from "@mui/icons-material";
import { useStore } from "@tanstack/react-store";

import AddInfoCard from "@components/add-info-card";
import ArrayFieldContainer from "@components/array-field-container";
import Card from "@components/card";
import Card<PERSON>eader from "@components/card-header";
import FormFooter from "@components/form-footer";
import StyledCardFooter from "@components/card-footer";
import { useAppForm, withForm } from "@hooks/app-form";
import { MapPin } from "lucide-react";

interface FormValues {
   sameAs: string | null;
   address: string;
}

const defaultValues: FormValues = {
   sameAs: '',
   address: '',
};

enum FormNames {
   SameAs = 'sameAs',
   Address = 'address',
}

const formatAddressSummary = (values: FormValues) => {
   const MAP: { [key: string]: { label: string } } = {
      [FormNames.SameAs]: {
         label: 'Same As'
      },
      [FormNames.Address]: {
         label: 'Address'
      }
   };

   return Object.entries(values)?.reduce((acc: Array<{ label: string, value: string }>, cur) => {
      const [key, value] = cur;

      if (MAP?.[key]?.label && value) {
         acc.push({
            label: MAP?.[key]?.label,
            value
         });
      }

      return acc;
   }, []) ?? [];
};

const PhysicalAddressForm = withForm<{ physicalAddresses: FormValues[] }>({
   defaultValues: {
      physicalAddresses: []
   },
   render: function Render({ form }) {
      return (
         <form.AppField
            name="physicalAddresses"
            mode="array"
         >
            {({ pushValue, removeValue, state, replaceValue }) => (
               <AddInfoCard
                  title="Physical Address *"
                  icon={<MapPin />}
                  onClick={() => pushValue(defaultValues)}
                  showHeader={state.value.length === 0}
               >
                  {state.value.map((item, i) => (
                     <ArrayFieldContainer
                        key={i}
                        initialEditState={true}
                        items={formatAddressSummary(item)}
                        title="Physical Address"
                     >
                        {({ setEdit }) => (
                           <PhysicalAddress
                              formValues={state.value[i]}
                              onAdd={(data) => {
                                 replaceValue(i, data);
                                 setEdit(false);
                              }}
                              onCancel={() => {
                                 removeValue(i);
                                 setEdit(false);
                              }}
                           />
                        )}
                     </ArrayFieldContainer>
                  ))}
               </AddInfoCard>
            )}
         </form.AppField>
      );
   }
});

interface PhysicalAddressProps {
   onAdd?: (values: FormValues) => void;
   onCancel?: () => void;
   onDelete?: () => void;
   formValues: FormValues;
}

const PhysicalAddress = ({ onAdd, onCancel, onDelete, formValues }: PhysicalAddressProps) => {
   const hasValues = JSON.stringify(formValues) !== JSON.stringify(defaultValues);
   const form = useAppForm({
      defaultValues: {
         ...defaultValues,
         ...formValues
      },
      defaultState: {
         isDirty: hasValues,
         isPristine: !hasValues,
      }
   });

   const values = useStore(form.store, (state) => state.values);

   return (
      <Card emphasis="dark">
         <CardHeader
            emphasis="dark"
            title="Physical Address"
            avatar={<MapPin size={24} />}
            slotProps={{
               title: { variant: 'h6' }
            }}
         />
         <CardContent>
            <Grid container spacing={3}>
               <Grid item xs={6}>
                  <form.AppField name={FormNames.SameAs}>
                     {({ state, handleChange }) => (
                        <TextField
                           fullWidth
                           select
                           label="Same As"
                           value={state.value}
                           onChange={(e) => handleChange(e.target.value as any)}
                        >
                           <MenuItem value="pra">Patient’s Residential Address </MenuItem>
                        </TextField>
                     )}
                  </form.AppField>
               </Grid>
               <Grid item xs={6}>
                  <form.AppField
                     name={FormNames.Address}
                     children={(field) => (
                        <field.AppTextField label={'Address'} required />
                     )}
                  />
               </Grid>
            </Grid>
         </CardContent>
         <StyledCardFooter sx={{ justifyContent: 'flex-end' }}>
            <form.Subscribe selector={({ isDirty }) => ({ isDirty })}>
               {({ isDirty }) => (
                  <FormFooter
                     onCancel={() => {
                        form.reset();
                        onCancel?.();
                     }}
                     onSubmit={() => onAdd?.(values)}
                     isUpdate={hasValues}
                     isDirty={isDirty}
                     onDelete={onDelete}
                  />
               )}
            </form.Subscribe>
         </StyledCardFooter>
      </Card>
   );
};

export default PhysicalAddressForm;
