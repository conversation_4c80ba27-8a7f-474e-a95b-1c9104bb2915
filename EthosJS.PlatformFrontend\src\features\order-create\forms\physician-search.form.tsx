import { PatientCreate, PatientRead } from '@auth/scopes';
import {
	EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
	EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPhysicianDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null,
} from '@client/workflows';
import { postApiPhysicianSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import AppPhysicianForm from '@components/forms/app-physician-form';
import { AppPhysicianFormProps } from '@components/forms/app-physician-form/app-physician.form';
import { useQuery } from '@tanstack/react-query';
import { PhysicianQuery, Query } from '@utils/query-dsl';
import { debounce, map } from 'lodash';
import { useMemo, useEffect, useState } from 'react';

function getQuery(searchTerm: string) {
	if (searchTerm === '') return;
	return Query.literal(PhysicianQuery.withApproximateName(searchTerm));
}

function getCareLocationQuery(careLocationId: string) {
	return Query.literal(PhysicianQuery.withCareLocationId(careLocationId));
}

function getCombinedQuery(searchTerm: string, careLocationId: string) {
	const searchQuery = getQuery(searchTerm);
	const careLocationQuery = getCareLocationQuery(careLocationId);
	if (!searchQuery && !careLocationQuery) return;
	if (!searchQuery) return careLocationQuery;
	if (!careLocationQuery) return searchQuery;
	return Query.and([searchQuery, careLocationQuery]);
}

interface PhysicianSearchFormProps extends AppPhysicianFormProps {
	careLocationId?: string;
}

export default function PhysicianSearchForm({
	careLocationId,
	...props
}: PhysicianSearchFormProps) {
	const [searchTerm, setSearchTerm] = useState<string>('');
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string>('');

	const { data: physicianData, isFetching: isPhysicianFetching } = useQuery({
		...postApiPhysicianSearchOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			body: getQuery(
				debouncedSearchTerm
			) as unknown as EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
			// body: getCombinedQuery(
			// 	debouncedSearchTerm,
			// 	careLocationId!
			// ) as unknown as EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		select: (data) => {
			const { items } =
				data as EthosUtilitiesPaginationPagedResponse1EthosWorkflowsApiPhysicianDto_EthosWorkflows_Version_1000_Culture_neutral_PublicKeyToken_null;
			return items;
		},
	});

	const debouncedSearch = useMemo(
		() =>
			debounce((searchTerm: string) => {
				setDebouncedSearchTerm(searchTerm);
			}, 300),
		[]
	);

	useEffect(() => {
		if (searchTerm !== debouncedSearchTerm) {
			debouncedSearch(searchTerm);
		}
	}, [debouncedSearch, debouncedSearchTerm, searchTerm]);

	return (
		<AppPhysicianForm
			{...props}
			options={
				map(physicianData, (item) => ({
					title: `${item.names?.[0]?.firstName ?? ''} ${item.names?.[0]?.lastName ?? ''}`,
					id: item.id,
					description: `${item.names?.[0]?.firstName ?? ''} ${item.names?.[0]?.lastName ?? ''}`,
					meta: {
						npi: item.id,
					},
				})) ?? []
			}
			searchLoading={isPhysicianFetching}
			searchTerm={searchTerm}
			onSearchChange={setSearchTerm}
		/>
	);
}
