import StepCardControl from '@components/step-card-control';
import { Box, Card, CardContent, Button, Stack } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-form';
import { MedicalInformation, NightsStay, History, Person, Note } from '@mui/icons-material';
import FormCard from '@components/form-card';
import { useMemo } from 'react';
import { filter } from 'lodash';
import { FormProps } from '@components/forms/predefined-form-props';
import { ClinicalConsiderationsData } from '../types';
import ClinicalConsiderationsList from '../components/clinical-considerations-list';

const defaultValues: ClinicalConsiderationsData = {
	clinicalConsiderations: [] as number[],
	schedulingPreferences: {
		technicianPreference: null!,
		preferredDayOfWeek: [] as number[],
	},
	additionalPatientNotes: '',
	caregiverInformation: '',
};

function clinicalConsiderationsOptions(savedData?: ClinicalConsiderationsData) {
	return {
		defaultValues: {
			...defaultValues,
			...(savedData as typeof defaultValues),
		},
	};
}

export default function ClinicalConsiderationsForm({
	onSubmit,
	onSaveDraft,
	savedData,
	isUpdate,
}: FormProps<ClinicalConsiderationsData>) {
	const options = useMemo(() => clinicalConsiderationsOptions(savedData), [savedData]);
	const form = useAppForm({
		...options,
		onSubmit: async ({ value }) => {
			onSubmit(value);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	return (
		<Box
			component="form"
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<Card elevation={0}>
				<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
					<form.AppField name="clinicalConsiderations">
						{() => {
							return (
								<Stack spacing={2}>
									<ClinicalConsiderationsList
										title="Medical Status"
										icon={MedicalInformation}
										setName="clinicalConsideration"
										category="Medical Status"
										isItemSelected={(id) => values?.clinicalConsiderations?.includes(id) ?? false}
										onSelect={(id, isChecked) => {
											if (isChecked) {
												form.setFieldValue('clinicalConsiderations', [
													...(values.clinicalConsiderations ?? []),
													id,
												]);
											} else {
												form.setFieldValue(
													'clinicalConsiderations',
													filter(values.clinicalConsiderations, (item) => item !== id)
												);
											}
										}}
									/>
									<ClinicalConsiderationsList
										title="Sleep Disorder Risk Factors"
										icon={NightsStay}
										setName="clinicalConsideration"
										category="Sleep Disorder Risk Factors"
										isItemSelected={(id) => values?.clinicalConsiderations?.includes(id) ?? false}
										onSelect={(id, isChecked) => {
											if (isChecked) {
												form.setFieldValue('clinicalConsiderations', [
													...(values.clinicalConsiderations ?? []),
													id,
												]);
											} else {
												form.setFieldValue(
													'clinicalConsiderations',
													filter(values.clinicalConsiderations, (item) => item !== id)
												);
											}
										}}
									/>
									<ClinicalConsiderationsList
										title="Medical History"
										icon={History}
										setName="clinicalConsideration"
										category="Medical History"
										isItemSelected={(id) => values?.clinicalConsiderations?.includes(id) ?? false}
										onSelect={(id, isChecked) => {
											if (isChecked) {
												form.setFieldValue('clinicalConsiderations', [
													...(values.clinicalConsiderations ?? []),
													id,
												]);
											} else {
												form.setFieldValue(
													'clinicalConsiderations',
													filter(values.clinicalConsiderations, (item) => item !== id)
												);
											}
										}}
									/>
								</Stack>
							);
						}}
					</form.AppField>

					{/* Additional Notes */}
					<form.AppField name="additionalPatientNotes">
						{(field) => (
							<FormCard
								title="Additional Patient Notes"
								icon={Note}>
								<field.AppTextField
									fullWidth
									multiline
									rows={4}
									label="Additional Patient Notes"
									data-testid="clinicalConsiderations.additionalPatientNotes"
								/>
							</FormCard>
						)}
					</form.AppField>

					{/* Caregiver Information */}
					<form.AppField name="caregiverInformation">
						{(field) => (
							<FormCard
								title="Caregiver Information"
								icon={Person}>
								<field.AppTextField
									fullWidth
									multiline
									rows={4}
									label="Caregiver Information"
									data-testid="clinicalConsiderations.caregiverInformation"
								/>
							</FormCard>
						)}
					</form.AppField>
				</CardContent>
			</Card>
			<StepCardControl>
				{!isUpdate && (
					<Button
						variant="outlined"
						color="primary"
						onClick={() => onSaveDraft(values)}
						data-testid="clinicalConsiderations.saveDraftButton">
						Save Draft
					</Button>
				)}
				<form.Subscribe
					selector={({ isDirty, canSubmit, isSubmitting }) => ({
						isDirty,
						canSubmit,
						isSubmitting,
					})}>
					{({ isDirty, canSubmit, isSubmitting }) => (
						<Button
							variant="contained"
							color="primary"
							type="submit"
							disabled={!isDirty || !canSubmit || isSubmitting}
							data-testid="clinicalConsiderations.submitButton">
							Next
						</Button>
					)}
				</form.Subscribe>
			</StepCardControl>
		</Box>
	);
}
