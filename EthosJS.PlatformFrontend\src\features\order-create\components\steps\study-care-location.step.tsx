import { OrderState, StepProps } from '@features/order-create/types';
import useOrder from '@features/order-create/hooks/use-order';
import useStudy from '@features/order-create/hooks/use-study';
import dayjs from 'dayjs';
import NotificationBanner from '@components/notification-banner';
import NotificationSnackbar from '@components/notification-snackbar';
import { NotificationState } from '@components/notification-snackbar';
import { useState } from 'react';
import StudyFormNew from '@features/order-create/forms/study-form.new';
import { EthosWorkflowsApiCreateStudyDto } from '@client/workflows';

interface StudyCareLocationProps extends Omit<StepProps, 'successCallback'> {
	studyId?: string;
	successCallback: (studyId: string) => void;
}

export default function StudyCareLocationStep({
	orderId,
	studyId,
	successCallback,
}: StudyCareLocationProps) {
	const { orderData, updateOrder } = useOrder({ orderId });

	const { data } = orderData ?? {};
	const orderState = (data?._state as unknown as OrderState) ?? {};
	const { flowState, stepState } = orderState;

	const { studyData, updateStudy, updateStudyError, resetUpdateStudyMutation } = useStudy({
		studyId,
	});
	const savedData = studyData?.data;

	const [notification, setNotification] = useState<NotificationState>({
		message: '',
		severity: 'info',
	});

	const resetToast = () => {
		setNotification({
			message: '',
			severity: 'info',
		});
	};

	const onStudyUpdateSuccess = (careLocationId: string, newStudyId: string) => {
		updateOrder(
			{
				...orderData?.data,
				careLocationId,
			},
			{
				flowState: {
					...flowState,
					status: 'InProgress',
					progress: 25,
					lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
				},
				stepState: {
					...stepState,
					AddStudy: 'Complete',
					AddAssociatedInsurances: 'InProgress',
				},
			},
			() => {
				if (newStudyId) {
					successCallback(newStudyId);
				}
			}
		);
	};

	return (
		<>
			<NotificationSnackbar
				notification={notification}
				onCloseToast={resetToast}
			/>
			<NotificationBanner
				message={updateStudyError?.message}
				severity="error"
				scrollIntoView
				onClose={resetUpdateStudyMutation}
			/>
			<StudyFormNew
				orderId={orderId!}
				savedData={{
					studyData: {
						encounterType: savedData?.encounterType,
						studyType: savedData?.studyType,
						studyAttributes: Array.isArray(savedData?.studyAttributes)
							? savedData?.studyAttributes
							: [],
					},
					careLocationId: orderData?.data?.careLocationId ?? null,
				}}
				onSubmit={({ studyData, careLocationId }) => {
					updateStudy(
						{
							...data,
							...(studyData as unknown as EthosWorkflowsApiCreateStudyDto),
						},
						(studyId) => {
							if (studyId && careLocationId) {
								onStudyUpdateSuccess(careLocationId, studyId);
							}
						}
					);
				}}
				onSaveDraft={({ studyData, careLocationId }) => {
					updateStudy(
						{
							...(studyData as unknown as EthosWorkflowsApiCreateStudyDto),
						},
						() => {
							setNotification({
								message: 'Draft saved successfully',
								severity: 'success',
							});
						}
					);
					updateOrder(
						{
							...orderData?.data,
							careLocationId: careLocationId ?? undefined,
						},
						{
							flowState: {
								...flowState,
								lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
							},
							stepState: {
								...stepState,
							},
						}
					);
				}}
			/>
		</>
	);
}
