import { FormProps } from "@components/forms/predefined-form-props";
import { SchedulingPreferencesData } from "../types";
import { useQuery } from "@tanstack/react-query";
import { getApiReferenceSetsValuesOptions } from "@client/refdata/@tanstack/react-query.gen";
import { useMemo } from "react";
import { ReferenceDataSetKeyValueDto } from "@client/refdata";
import { useAppForm } from "@hooks/app-form";
import { useStore } from '@tanstack/react-form';
import { Button, Checkbox, FormControlLabel, FormLabel, Radio, RadioGroup, Stack, Typography } from "@mui/material";
import ClinicalConsiderationsList from "../components/clinical-considerations-list";
import { Accessibility, Language, Note, Schedule } from "@mui/icons-material";
import { filter, includes } from "lodash";
import FormCard from "@components/form-card";
import StepCardControl from "@components/step-card-control";

const defaultValues: SchedulingPreferencesData = {
   specialAccomodations: [],
   mobilityAssistance: [],
   schedulingPreferences: {
      technicianPreference: null,
      preferredDayOfWeek: [],
      additionalPatientNotes: ''
   },
};


function clinicalConsiderationsOptions(savedData?: SchedulingPreferencesData) {
   return {
      defaultValues: {
         ...defaultValues,
         ...(savedData as typeof defaultValues),
      },
   };
}

export default function SchedulePreferenceForm({ savedData, onSubmit }: FormProps<SchedulingPreferencesData>) {

   const options = useMemo(() => clinicalConsiderationsOptions(savedData), [savedData]);

   const { data: technicianPreferenceOptions } =
      useQuery(
         getApiReferenceSetsValuesOptions({
            responseType: 'json',
            query: {
               setName: 'technicianPreference',
            },
         })
      );

   const { data: weekDayOptions } = useQuery(
      getApiReferenceSetsValuesOptions({
         responseType: 'json',
         query: {
            setName: 'weekday',
         },
      })
   );

   const technicianPreferenceOptionsItems =
      (technicianPreferenceOptions as { items: ReferenceDataSetKeyValueDto[] })?.items ?? [];

   const weekDayOptionsItems = (weekDayOptions as { items: ReferenceDataSetKeyValueDto[] })
      ?.items ?? [
         { key: { value: 'Monday' }, id: 1 },
         { key: { value: 'Tuesday' }, id: 2 },
         { key: { value: 'Wednesday' }, id: 3 },
         { key: { value: 'Thursday' }, id: 4 },
         { key: { value: 'Friday' }, id: 5 },
         { key: { value: 'Saturday' }, id: 6 },
         { key: { value: 'Sunday' }, id: 7 },
      ];

   const isUpdate = false;


   const onSaveDraft = (values: SchedulingPreferencesData) => {
      console.log(values)
   }

   const form = useAppForm({
      ...options,
      onSubmit: async ({ value }) => {
         onSubmit(value);
      },
   });

   const values = useStore(form.store, (state) => state.values);


   return (
      <Stack
         gap={2}
         p={2}
         component="form"
         // sx={{ height: '100%' }}
         onSubmit={(e) => {
            e.preventDefault();
            form.handleSubmit();
         }}
      >
         <form.AppField name="specialAccomodations">
            {() => {
               return (
                  <ClinicalConsiderationsList
                     title="Special Accommodations"
                     icon={Language}
                     setName="clinicalConsideration"
                     category="Special Accommodations"
                     isItemSelected={(id) => values?.specialAccomodations?.includes(id) ?? false}
                     onSelect={(id, isChecked) => {
                        if (isChecked) {
                           form.setFieldValue('specialAccomodations', [
                              ...(values.specialAccomodations ?? []),
                              id,
                           ]);
                        } else {
                           form.setFieldValue(
                              'specialAccomodations',
                              filter(values.specialAccomodations, (item) => item !== id)
                           );
                        }
                     }}
                  />
               )
            }}
         </form.AppField>

         <form.AppField name="mobilityAssistance">
            {() => {
               return (
                  <ClinicalConsiderationsList
                     title="Mobility and Assistance"
                     icon={Accessibility}
                     setName="clinicalConsideration"
                     category="Mobility & Assistance"
                     isItemSelected={(id) => values?.mobilityAssistance?.includes(id) ?? false}
                     onSelect={(id, isChecked) => {
                        if (isChecked) {
                           form.setFieldValue('mobilityAssistance', [
                              ...(values.mobilityAssistance ?? []),
                              id,
                           ]);
                        } else {
                           form.setFieldValue(
                              'mobilityAssistance',
                              filter(values.mobilityAssistance, (item) => item !== id)
                           );
                        }
                     }}
                  />
               )
            }}
         </form.AppField>

         <form.AppField name='schedulingPreferences'>
            {() => {
               return (
                  <FormCard
                     title="Scheduling Preferences"
                     icon={Schedule}>
                     <FormLabel sx={{ my: 1 }}>Technician Preference</FormLabel>
                     <form.AppField name="schedulingPreferences.technicianPreference">
                        {(field) => (
                           <RadioGroup
                              row
                              value={field.state.value}
                              onChange={(e) => field.handleChange(Number(e.target.value))}>
                              {technicianPreferenceOptionsItems.map((option) => (
                                 <FormControlLabel
                                    key={option.id}
                                    value={option.id}
                                    control={
                                       <Radio
                                          data-testid={`schedulingPreferences.technicianPreference.${option.id}`}
                                       />
                                    }
                                    label={(option?.values as { name: string })?.name}
                                 />
                              ))}
                           </RadioGroup>
                        )}
                     </form.AppField>
                     <Typography sx={{ my: 1 }}>Preferred Day(s) of Week</Typography>
                     <form.AppField name="schedulingPreferences.preferredDayOfWeek">
                        {({ state, handleChange }) => (
                           <Stack
                              direction={'row'}
                              sx={{ flexWrap: 'wrap', gap: 2 }}>
                              {weekDayOptionsItems.map((option) => (
                                 <FormControlLabel
                                    key={option.id}
                                    control={
                                       <Checkbox
                                          checked={includes(state.value, option.id)}
                                          onChange={(e) => {
                                             const prevValue =
                                                filter(state.value, (id) => typeof id === 'number') ?? [];
                                             if (e.target.checked && typeof option.id === 'number') {
                                                handleChange([...prevValue, option.id]);
                                             } else {
                                                handleChange(filter(prevValue, (id) => id !== option.id));
                                             }
                                          }}
                                          data-testid={`schedulingPreferences.preferredDaysOfWeek.${option.id}`}
                                       />
                                    }
                                    label={(option?.values as { name: string })?.name}
                                 />
                              ))}
                           </Stack>
                        )}
                     </form.AppField>
                  </FormCard>
               )
            }}
         </form.AppField>

         {/* Additional Notes */}
         <form.AppField name="schedulingPreferences.additionalPatientNotes">
            {(field) => (
               <FormCard
                  title="Additional Patient Notes"
                  icon={Note}>
                  <field.AppTextField
                     fullWidth
                     multiline
                     rows={4}
                     label="Additional Patient Notes"
                     data-testid="clinicalConsiderations.additionalPatientNotes"
                  />
               </FormCard>
            )}
         </form.AppField>

         <StepCardControl>
            {!isUpdate && (
               <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => onSaveDraft(values)}
                  data-testid="clinicalConsiderations.saveDraftButton">
                  Save Draft
               </Button>
            )}
            <form.Subscribe
               selector={({ isDirty, canSubmit, isSubmitting }) => ({
                  isDirty,
                  canSubmit,
                  isSubmitting,
               })}>
               {({ isDirty, canSubmit, isSubmitting }) => (
                  <Button
                     variant="contained"
                     color="primary"
                     type="submit"
                     disabled={!isDirty || !canSubmit || isSubmitting}
                     data-testid="clinicalConsiderations.submitButton">
                     Submit & Create Order
                  </Button>
               )}
            </form.Subscribe>
         </StepCardControl>
      </Stack>
   )
}