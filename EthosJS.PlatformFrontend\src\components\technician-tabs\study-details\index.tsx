import { Grid, GridProps } from "@mui/material";
import StudyDetailsType from "./type";
import StudyDetailsLocation from "./location";
import StudyDetailsPhysicians from "./physicians";


interface PatientInfoProp {
   title?: string
}

const GRID_PROPS: Partial<GridProps> = {
   item: true,
   xs: 12
}

const StudyDetails = (props: PatientInfoProp) => {

   const { } = props;

   return (
      <Grid container spacing={2}>
         <Grid {...GRID_PROPS}>
            <StudyDetailsType
               title="Patient Information"
               values={{
                  PSG: {
                     inLab: 'In-Lab',
                     CPTCode: '214214',
                     ETCO2: 'EtCO₂ Monitoring',
                     extendedEEG: 'Extended',
                     videoRecording: 'Video Recording',
                     transcutaneousCO2: 'Transcutaneous CO2',
                     parasomniaAssessment: 'Parasomnia Assessment',
                  }
               }}
            />
         </Grid>
         <Grid {...GRID_PROPS}>
            <StudyDetailsLocation
               title="Study Location"
               values={{
                  PSG: {
                     inLab: 'In-Lab',
                     CPTCode: '214214',
                     ETCO2: 'EtCO₂ Monitoring',
                     extendedEEG: 'Extended',
                     videoRecording: 'Video Recording',
                     transcutaneousCO2: 'Transcutaneous CO2',
                     parasomniaAssessment: 'Parasomnia Assessment',
                  }
               }}
            />
         </Grid>
         <Grid {...GRID_PROPS}>
            <StudyDetailsPhysicians
               title="Physicians (3)"
            />
         </Grid>
      </Grid>
   )
};

export default StudyDetails;