import MainCardContainer from '@components/main-container/main-card-container';
import { Stack, Typography } from '@mui/material';
import PatientAppointmentAvailableDateTimeBanner from './patient-appointment-available-banner';

const AppointmentConfirmation = () => {
	const notes = [
		{
			name: '<PERSON>',
			updatedDateTime: 'May 7, 2025 at 9:15 AM',
			comment:
				'<PERSON>poke with <PERSON> directly. He confirmed he will attend the sleep study appointment on October 22, 2025 at 11:00 AM. I reviewed preparation instructions with him, and he confirmed he has received them and will follow them. He mentioned he will have his daughter drive him to the appointment. He had no additional questions about the procedure.',
		},
		{
			name: '<PERSON>',
			updatedDateTime: 'May 7, 2025 at 9:15 AM',
			comment:
				'Spoke with <PERSON> directly. He confirmed he will attend the sleep study appointment on October 22, 2025 at 11:00 AM. I reviewed preparation instructions with him, and he confirmed he has received them and will follow them. He mentioned he will have his daughter drive him to the appointment. He had no additional questions about the procedure.',
		},
		{
			name: '<PERSON>',
			updatedDateTime: 'May 7, 2025 at 9:15 AM',
			comment:
				'Spoke with <PERSON> directly. He confirmed he will attend the sleep study appointment on October 22, 2025 at 11:00 AM. I reviewed preparation instructions with him, and he confirmed he has received them and will follow them. He mentioned he will have his daughter drive him to the appointment. He had no additional questions about the procedure.',
		},
	];

	return (
		<MainCardContainer
			emphasis="high"
			color="success"
			title="Appointment Confirmed by Phone"
			primaryActionType="Edit">
			<Stack gap={2}>
				<PatientAppointmentAvailableDateTimeBanner />
				<MainCardContainer
					title="Call Notes"
					color="gray"
					primaryActionType="Add">
					<Stack gap={2}>
						{notes.map((note, index) => {
							return (
								<MainCardContainer
									key={index}
									title={`Added by ${note?.name}, ${note?.updatedDateTime}`}
									primaryActionType="Edit">
									<Typography>{note.comment}</Typography>
								</MainCardContainer>
							);
						})}
					</Stack>
				</MainCardContainer>
			</Stack>
		</MainCardContainer>
	);
};

export default AppointmentConfirmation;
