import DemographicsForm from '@components/forms/app-demographics.form';
import EmailForm from '@components/forms/app-email-form/app-email.form';
import PatientInformationForm from '@components/forms/app-patient-information.form';
import PhoneNumberForm from '@components/forms/app-phone-number-form/app-phone-number.form';
import PhysicalMeasurementsForm from '@components/forms/app-physical-measurements.form';
import { JSXElementConstructor } from 'react';
import PatientInformation from '@components/forms/create-patient/patient-information.form';
import Contacts from '@components/forms/create-patient/contacts.form';
import AddressesForm from '@components/forms/create-patient/addresses.form';
import InsurancesForm from '@components/forms/create-patient/insurances.form';
import GuardiansForm from '@components/forms/create-patient/guardians.form';

import StudyPreferencesForm from '@components/forms/app-study-preferences.form';
import AppGuarantorForm from '@components/forms/app-guarantor.form';

export const PredefinedFormMap: Record<string, JSXElementConstructor<any>> = {
	PatientInformation: PatientInformationForm,
	Demographics: DemographicsForm,
	PhysicalMeasurements: PhysicalMeasurementsForm,
	PhoneNumberContact: PhoneNumberForm,
	EmailContact: EmailForm,
	AddBasicInformationStep: PatientInformation,
	AddContactsStep: Contacts,
	AddAddressesStep: AddressesForm,
	AddInsurancesStep: InsurancesForm,
	AddGuardiansStep: GuardiansForm,
	StudyPreferences: StudyPreferencesForm,
	AppGuarantor: AppGuarantorForm,
};
