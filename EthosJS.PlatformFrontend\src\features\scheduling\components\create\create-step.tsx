import CollapsibleMainCardContainer from '@components/collapsible-main-card-container';
import SmartButtonOutlinedIcon from '@mui/icons-material/SmartButtonOutlined';

import { JSX, useMemo, useRef, useState, useTransition } from 'react';

import useCreateAppointmentStore from '@hooks/use-create-appointment-store';
import { useStore } from '@tanstack/react-store';
import SelectAppointmentDate from './select-appointment-date';
import Instructions from './instruction';
import Review from './review';
import { LoaderIcon } from 'lucide-react';
import { Box } from '@mui/material';
import { useForm } from '@tanstack/react-form';
import { useQuery } from '@tanstack/react-query';
import { postApiRoomSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import NotificationSnackbar, { NotificationState } from '@components/notification-snackbar';
import useOrderDetails from '@features/scheduling/hooks/use-order-detailts';

interface StepProps {
	patientId: string;
	studyId: string;
	orderId: string;
	onSuccessCallback: (appointmentId: string) => void;
	onCancelCreate: () => void;
}

const CreateStep = ({
	patientId,
	studyId,
	orderId,
	onSuccessCallback,
	onCancelCreate,
}: StepProps) => {
	const [notificationState, setNotification] = useState({
		showToast: false,
		notification: { message: '', severity: 'info' } as NotificationState,
	});

	const [isPending, startTransition] = useTransition();

	const { store, actions } = useCreateAppointmentStore();
	const {
		totalStep,
		activeStep,
		values: { selectedAppointment, isConfirmed },
		isLastStep,
	} = useStore(store, (state) => state);

	// const { mutateAsync: createPatientAppointment, isPending: isCreating } = useMutation({
	// 	...postApiPatientAppointmentMutation({
	// 		scopes: [PatientCreate.value, PatientRead.value],
	// 		responseType: 'json',
	// 	}),
	// 	onSuccess: () => {
	// 		navigate({
	// 			to: '/patients/$patientId/schedule/appointment-creation/dashboard/preview',
	// 			params: { patientId },
	// 			search,
	// 		});
	// 	},
	// 	onSettled(data, error) {
	// 		if (error) {
	// 			setNotification({
	// 				showToast: true,
	// 				notification: {
	// 					message: (error.response?.data.message as string) ?? 'Something went wrong',
	// 					severity: 'error',
	// 				},
	// 			});
	// 		}
	// 		if (!error && data) {
	// 			setNotification({
	// 				showToast: true,
	// 				notification: {
	// 					message: 'Appointment created successfully',
	// 					severity: 'success',
	// 				},
	// 			});
	// 		}
	// 	},
	// });

	const formRef = useRef<ReturnType<typeof useForm> | null>(null);

	const isValidNext = useMemo(() => {
		if (activeStep === 0) {
			return Object.values(selectedAppointment).filter(Boolean).length;
		} else if (activeStep === 2) {
			return isConfirmed;
		}
		return true;
	}, [activeStep, selectedAppointment, isConfirmed]);

	const onNext = () => {
		if (!isLastStep) {
			// activeStep === 0;
			// ? createPatientAppointment({
			// 		body: {
			// 			studyId,
			// 			roomId: room?.items?.[0]?.id as string,
			// 			careLocationShiftId: selectedAppointment.id as string,
			// 			date: selectedAppointment.date as string,
			// 		},
			// 	})
			// : formRef.current?.handleSubmit();
		} else {
			// TODO(GK): Appointment confirmation flow goes here
		}
	};

	const onBack = () => {
		startTransition(actions.moveBack);
	};

	const onCloseToast = () => {
		setNotification({
			showToast: false,
			notification: { message: '', severity: 'info' } as NotificationState,
		});
	};
	const isCreating = false;

	const renderActiveStepTitle = () => {
		switch (activeStep) {
			case 0:
				return 'Select Date and Time';
			case 1:
				return 'Instructions & Documents';
			case 2:
				return 'Review Appointment Details';
			default:
				return 'Create an appointment';
		}
	};

	const renderActiveStep = () => {
		switch (activeStep) {
			case 0:
				return (
					<SelectAppointmentDate
						orderId={orderId}
						studyId={studyId}
					/>
				);
			case 1:
				return <Instructions formRef={formRef} />;
			case 2:
				return <Review />;
			default:
				return null;
		}
	};

	return (
		<CollapsibleMainCardContainer
			mainContainerProps={{
				title: `Create an appointment (${activeStep + 1}/${totalStep})`,
				emphasis: 'high',
				icon: <SmartButtonOutlinedIcon />,
				descriptionSubheader: renderActiveStepTitle(),
				footerProps: {
					primaryButton1: {
						label: !isLastStep ? (isCreating ? 'Creating...' : 'Next') : 'Create & Exit',
						'data-testid': 'create-appointment-step-next',
						disabled: !isValidNext,
						onClick: onNext,
					},
					...(activeStep
						? {
								primaryButton2: {
									label: 'Back',
									'data-testid': 'create-appointment-step-back',
									onClick: onBack,
								},
							}
						: {}),
					secondaryButton1: {
						label: 'Cancel',
						'data-testid': 'create-appointment-step-cancel',
						onClick: onCancelCreate,
					},
				},
			}}
			defaultCollapse>
			{isPending ? (
				<Box sx={styles.centered}>
					<LoaderIcon />
				</Box>
			) : (
				renderActiveStep()
			)}
			<NotificationSnackbar
				notification={notificationState.notification}
				open={notificationState.showToast}
				onCloseToast={onCloseToast}
			/>
		</CollapsibleMainCardContainer>
	);
};

const styles = {
	centered: {
		display: 'flex',
		justifyContent: 'center',
		alignItems: 'center',
	},
};

export default CreateStep;
