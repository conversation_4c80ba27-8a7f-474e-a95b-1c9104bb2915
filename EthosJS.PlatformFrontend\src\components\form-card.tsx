import { CardContent, } from "@mui/material";
import <PERSON><PERSON>eader from "./card-header";
import { SvgIconComponent } from "@mui/icons-material";
import { PropsWithChildren, ReactNode } from "react";
import Card from "./card";

interface FormCardProps extends PropsWithChildren {
    title: string;
    icon: SvgIconComponent;
    action?: ReactNode;
    emphasis?: 'dark' | 'light';
}

export default function FormCard({ title, icon: Icon, action, emphasis, children }: FormCardProps) {

    return (
        <Card emphasis={emphasis} sx={{ flex: 1, minHeight: 0, }}>
            <CardHeader
                title={title}
                avatar={
                    <Icon fontSize="large" />
                }
                slotProps={{
                    title: { variant: 'h6' },
                }}
                action={action}
                emphasis={emphasis}
            />
            <CardContent>
                {children}
            </CardContent>
        </Card>
    )
}