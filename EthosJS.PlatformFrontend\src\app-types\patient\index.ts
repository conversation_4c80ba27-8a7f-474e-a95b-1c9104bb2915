import {
  EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_Committed,
  EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddAddresses,
  EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddBasicInformation,
  EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddClinicalInformation,
  EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddContacts,
  EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddGuardians,
  EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddInsurances,
  EthosWorkflowsWorkflowAddNewPatientAddNewPatientState
} from '@client/workflows';

export type AddBasicInformation =
  EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddBasicInformation;

export type AddContacts = EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddContacts;

export type AddAddresses = EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddAddresses;

export type AddInsurances = EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddInsurances;

export type AddGuardians = EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddGuardians;

export type AddClinicalInformation =
  EthosWorkflowsWorkflowAddNewPatientIAddNewPatientRequest_AddClinicalInformation;

export type Committed = EthosWorkflowsWorkflowAddNewPatientAddNewPatientState_Committed;

export type AddNewPatientState = EthosWorkflowsWorkflowAddNewPatientAddNewPatientState;
