import { PatientState, StepPropsNew } from '../../types/state-types';
import usePatient from '@features/patient-create/hooks/use-patient';
import usePatientValidation from '@features/patient-create/hooks/use-patient-validation';
import PatientInformation from '../../forms/patient-information.form';
import { useMemo, useState } from 'react';
import { PatientBasicInformationData } from '@features/patient-create/forms';
import NotificationBanner from '@components/notification-banner';
import { getNewPatientState } from '@features/patient-create/utils';
import dayjs from 'dayjs';
import NotificationSnackbar, { NotificationState } from '@components/notification-snackbar';

export default function AddBasicInformationStepNew({ patientId, successCallback }: StepPropsNew) {
	const { patientData, updatePatient, updatePatientError, resetUpdatePatientMutation, saveDraft } =
		usePatient({
			patientId,
		});

	const { validateFormatted } = usePatientValidation();

	const [notification, setNotification] = useState<NotificationState>({
		message: '',
		severity: 'info',
	});

	const { data } = patientData ?? {};

	const patientState = (data?._state as unknown as PatientState) ?? {};
	const stepStatus = patientState.stepState?.BasicInformation ?? 'NotStarted';
	const patientInformation = data?.patientInformation;
	const demographics = data?.demographics;
	const physicalMeasurements = data?.physicalMeasurements;

	const savedData = useMemo(() => {
		return {
			patientInformation,
			demographics,
			physicalMeasurements,
		} as PatientBasicInformationData;
	}, [patientInformation, demographics, physicalMeasurements]);

	const resetToast = () => {
		setNotification({
			message: '',
			severity: 'info',
		});
	};

	return (
		<>
			<NotificationSnackbar
				notification={notification}
				onCloseToast={resetToast}
			/>
			<NotificationBanner
				message={updatePatientError?.message}
				severity={updatePatientError ? 'error' : 'success'}
				scrollIntoView
				onClose={resetUpdatePatientMutation}
			/>
			<PatientInformation
				savedData={savedData}
				onSubmit={(newData) => {
					updatePatient(
						{
							...data,
							...newData,
						},
						getNewPatientState(patientState, 'BasicInformation', 'Contacts'),
						successCallback
					);
				}}
				onSaveDraft={(newData) => {
					saveDraft(
						{
							...data,
							...newData,
						},
						{
							flowState: {
								...patientState.flowState,
								lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
							},
							stepState: {
								...patientState.stepState,
							},
						},
						() => {
							setNotification({
								message: 'Draft saved successfully',
								severity: 'success',
							});
						}
					);
				}}
				onValidate={validateFormatted}
				isUpdate={stepStatus === 'Complete'}
			/>
		</>
	);
}
