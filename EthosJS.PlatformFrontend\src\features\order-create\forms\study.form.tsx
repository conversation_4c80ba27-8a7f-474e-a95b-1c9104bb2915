import { <PERSON><PERSON>, Button, Box } from '@mui/material';
import StepCardControl from '@components/step-card-control';
import { useAppForm } from '@hooks/app-form';
import LoadingComponent from '@components/loading-component';
import MainCardContainer from '@components/main-container/main-card-container';
import { EthosWorkflowsApiCreateStudyDto } from '@client/workflows';
import { find } from 'lodash';
import { formOptions, useStore } from '@tanstack/react-form';
import { useEffect, useMemo, useState } from 'react';
import AssociatedInsurances from '@features/order-create/forms/associated-insurance-card';
import { StudyFormData, StudyFormProps } from '../types/form-types';
import { studyFormDataToDto, studyDtoToFormData } from './transformers';
import { useRefDataOptions } from '@hooks/use-ref-data-options';
const defaultValues: StudyFormData = {
	encounterType: null,
	studyType: null,
	studyAttributes: [],
	insurances: [],
};

function studyFormOptions(savedData?: EthosWorkflowsApiCreateStudyDto) {
	return formOptions({
		defaultValues: {
			...defaultValues,
			...(savedData ? studyDtoToFormData(savedData) : {}),
		},
	});
}

export default function StudyForm({
	orderId,
	onSubmit,
	savedData,
	onSaveDraft,
	insurances,
}: StudyFormProps) {
	const options = useMemo(() => studyFormOptions(savedData), [savedData]);
	const form = useAppForm({
		...options,
		onSubmit: async ({ value }) => {
			onSubmit({
				...studyFormDataToDto(value),
				orderId,
			} as EthosWorkflowsApiCreateStudyDto);
		},
	});

	const [encounterTypeName, setEncounterTypeName] = useState<string | null>(null);
	const [studyTypeName, setStudyTypeName] = useState<string | null>(null);

	const { values } = useStore(form.store, (state) => ({ values: state.values }));

	const { options: encounterTypeOptions, isFetching: isFetchingEncounterTypeOptions } =
		useRefDataOptions({ setName: 'encounterType' });

	const { options: studyTypeOptions, isFetching: isFetchingStudyTypeOptions } = useRefDataOptions({
		setName: 'studyType',
		filter: `encounterType eq ${encounterTypeName}`,
	});
	const { options: studyAttributesOptions, isFetching: isFetchingStudyAttributesOptions } =
		useRefDataOptions({
			setName: 'studyAttributes',
			filter: `studyType eq ${studyTypeName}`,
		});

	useEffect(() => {
		const savedEncId = form.getFieldValue('encounterType');
		if (encounterTypeOptions && savedEncId) {
			const encItem = find(encounterTypeOptions, { value: savedEncId.toString() });
			if (encItem) {
				setEncounterTypeName(encItem.title);
			}
		}
	}, [encounterTypeOptions, form]);

	useEffect(() => {
		const savedStudId = form.getFieldValue('studyType');
		if (studyTypeOptions && savedStudId) {
			const stItem = find(studyTypeOptions, { value: savedStudId.toString() });
			if (stItem) {
				setStudyTypeName(stItem.title);
			}
		}
	}, [studyTypeOptions, form]);

	return (
		<Box
			sx={{ height: '100%' }}
			component="form"
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<Stack gap={3}>
				<MainCardContainer
					title="Study Preferences"
					color="primary"
					emphasis="low"
					descriptionSubheader="Select the appropriate study and attributes based on the physician's order."
					descriptionText="Each type has different protocols and equipment requirements.">
					<Stack sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
						{isFetchingEncounterTypeOptions ? (
							<LoadingComponent />
						) : (
							<form.AppField
								name="encounterType"
								children={(field) => (
									<field.AppRadioField
										label="1. Encounter Type"
										options={encounterTypeOptions ?? []}
										onValueChange={(value) => {
											if (typeof value === 'string') {
												form.setFieldValue('studyType', null);
												form.setFieldValue('studyAttributes', []);
												setStudyTypeName(null);

												const encItem = find(encounterTypeOptions, {
													value,
												});
												if (encItem) {
													setEncounterTypeName(encItem.title);
												} else {
													setEncounterTypeName(null);
												}
											}
										}}
									/>
								)}
							/>
						)}

						{!!encounterTypeName &&
							(isFetchingStudyTypeOptions ? (
								<LoadingComponent />
							) : (
								<form.AppField
									name="studyType"
									children={(field) => (
										<field.AppRadioField
											label="2. Study Type"
											options={studyTypeOptions ?? []}
											onValueChange={(value) => {
												if (typeof value === 'string') {
													form.setFieldValue('studyAttributes', []);
													const stItem = find(studyTypeOptions, {
														value,
													});
													if (stItem) {
														setStudyTypeName(stItem.title);
													} else {
														setStudyTypeName(null);
													}
												}
											}}
										/>
									)}
								/>
							))}

						{!!studyTypeName &&
							(isFetchingStudyAttributesOptions ? (
								<LoadingComponent />
							) : (
								<form.AppField
									name="studyAttributes"
									children={(field) => (
										<field.AppRadioField
											label="3. Study Attributes"
											isMulti
											options={studyAttributesOptions ?? []}
										/>
									)}
								/>
							))}
					</Stack>
				</MainCardContainer>
				<AssociatedInsurances
					//@ts-expect-error type Mismatch
					form={form}
					insurances={insurances}
				/>
				<StepCardControl>
					<Button
						variant="outlined"
						color="primary"
						onClick={() =>
							onSaveDraft({
								...studyFormDataToDto(values),
								orderId,
							} as EthosWorkflowsApiCreateStudyDto)
						}>
						Save Draft
					</Button>
					<form.Subscribe
						selector={({ isDirty, canSubmit, isSubmitting }) => ({
							isDirty,
							canSubmit,
							isSubmitting,
						})}>
						{({ isDirty, canSubmit, isSubmitting }) => (
							<Button
								variant="contained"
								color="primary"
								type="submit"
								loading={isSubmitting}
								disabled={!isDirty || !canSubmit}>
								Next
							</Button>
						)}
					</form.Subscribe>
				</StepCardControl>
			</Stack>
		</Box>
	);
}
