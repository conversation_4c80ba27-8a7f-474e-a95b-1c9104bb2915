import { IExpr } from './expressions';

// Data classes
export interface IFieldName {
  name: string;

  // Properties
  pascal_case: string;
  camel_case: string;
}

// Type system
export interface IType {
  type: 'RefType' | 'NullableOf' | 'ListOf' | 'OneOf' | 'AliasTypeDef' | 'StructTypeDef';
  has_inline_definitions: boolean;
  has_internal_inline_definitions: boolean;
}

export interface ISimpleType extends IType {}

export interface IRefType extends ISimpleType {
  name: string;

  // Properties
  is_external: boolean;
  is_basic_type: boolean;
}

export interface IRefTypeStatic {
  Guid: IRefType;
  String: IRefType;
  Integer: IRefType;
  Floating: IRefType;
  Real: IRefType;
  Date: IRefType;
  TimeOfDay: IRefType;
  DateTime: IRefType;
  Boolean: IRefType;
}

export interface INullableOf extends ISimpleType {
  inner: IType;

  // Properties
  is_basic_type: boolean;
}

export interface IListOf extends ISimpleType {
  inner: IType;
  min_size: number | null;
  max_size: number | null;

  // Properties
  is_basic_type: boolean;
}

export interface IOneOf extends ISimpleType {
  types: IType[];

  // Properties
  is_basic_type: boolean;
}

export interface ITypeDef extends IType {
  name: string;
  type: 'StructTypeDef' | 'AliasTypeDef' | 'RefType';
  description: string | null;
  validation: IValidationRule[];
}

export interface IAliasTypeDef extends ITypeDef {
  target: IType;
  is_categorical: boolean;
  default_values: string[];

  // Properties
  is_basic_type: boolean;
}

export interface IStructTypeDef extends ITypeDef {
  fields: Record<string, IType>;

  // Properties
  is_basic_type: boolean;
}

// Validation
export enum ValidationType {
  ERROR = 'error',
  WARNING = 'warning'
}

export interface IValidationRule<T = any> {
  type: ValidationType;
  name: string;
  require: IExpr<T>;
  message: string;

  toJSON(): {
    level: 'error' | 'warning';
    name: string;
    require: any;
  };
}

export interface IFlow {
  name: string;
  description: string | null;
  types: Record<string, ITypeDef>;
  steps: Record<string, IStep>;
  states: Record<string, IState>;
}

export interface ITypeAnnotation {
  type: ISimpleType;
  base_type: IRefType | null;
}

export interface IStep {
  name: string;
  description: string | null;
  from: string | null;
  to: string;
  data: string; // type name
  is_auto: boolean;
  is_trivial: boolean;
  is_final: boolean;
  validation: IValidationRule<IExpr<ITypeAnnotation>>[];
}

export interface IState {
  name: string;
  description: string | null;
  is_deprecated: boolean;
  is_final: boolean;
  data: string; // type name
}

export interface IConfig {
  types: Record<string, ITypeDef>;
  flows: Record<string, IFlow>;
}

// Helper function to create validation rules with proper toJSON implementation
export function createValidationRule<T>(
  rule: Omit<IValidationRule<T>, 'toJSON'>
): IValidationRule<T> {
  return {
    ...rule,
    toJSON() {
      return {
        level: rule.type === ValidationType.ERROR ? 'error' : 'warning',
        name: rule.name,
        require: rule.require
      };
    }
  };
}
