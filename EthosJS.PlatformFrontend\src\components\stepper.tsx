import { useState } from 'react';
import MainCardContainer, { MainCardContainerProps } from './main-container/main-card-container';
import { Chip } from '@mui/material';

interface StepperProps {
	steps: Array<{
		title: string;
		component: React.ReactNode;
		onNext?: () => void;
		onSaveDraft?: () => void;
		onBack?: () => void;
	}>;
	mainCardContainerProps?: MainCardContainerProps;
}

export default function Stepper({ steps, mainCardContainerProps = {} }: StepperProps) {
	const [activeStep, setActiveStep] = useState(0);

	const activeStepContext = steps[activeStep];

	return (
		<MainCardContainer
			title={activeStepContext.title}
			customAction={
				<Chip
					variant="outlined"
					label={activeStep + 1 + ' of ' + steps.length}
					sx={{ borderRadius: 2 }}
				/>
			}
			footerProps={{
				primaryButton1:
					activeStep < steps.length - 1
						? {
								label: 'Next',
								onClick: () => {
									activeStepContext.onNext?.();
									setActiveStep(activeStep + 1);
								},
							}
						: undefined,
				primaryButton2: activeStepContext.onSaveDraft
					? {
							label: 'Save Draft',
							onClick: () => {},
						}
					: undefined,
				secondaryButton1:
					activeStep > 0
						? {
								label: 'Back',
								onClick: () => {
									activeStepContext.onBack?.();
									setActiveStep(activeStep - 1);
								},
							}
						: undefined,
			}}
			{...mainCardContainerProps}>
			{activeStepContext.component}
		</MainCardContainer>
	);
}
