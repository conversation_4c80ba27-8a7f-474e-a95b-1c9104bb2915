import NotificationBanner from '@components/notification-banner';
import NotificationSnackbar, { NotificationState } from '@components/notification-snackbar';
import SchedulePreferenceForm from '@features/patient-create/forms/schedule-preferences.form';
import usePatient from '@features/patient-create/hooks/use-patient';
import usePatientValidation from '@features/patient-create/hooks/use-patient-validation';
import { PatientState, SchedulingPreferencesData } from '@features/patient-create/types';
import { getNewPatientState } from '@features/patient-create/utils';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';

interface ScheduleStepProps {
	patientId: string;
	successCallback: (patientId: string) => void;
}

export default function SchedulePreferencesStep({ patientId, successCallback }: ScheduleStepProps) {
	const [notification, setNotification] = useState<NotificationState>({
		message: '',
		severity: 'info',
	});

	const resetToast = () => {
		setNotification({
			message: '',
			severity: 'info',
		});
	};

	const {
		patientData,
		updatePatient,
		commitDraft,
		updatePatientError,
		resetUpdatePatientMutation,
		commitDraftError,
		saveDraft,
	} = usePatient({
		patientId,
	});
	const { validateFormatted } = usePatientValidation();

	const { data } = patientData ?? {};
	const patientState = (data?._state as unknown as PatientState) ?? {};
	const stepStatus = patientState.stepState?.ClinicalConsiderations ?? 'NotStarted';

	const savedData = useMemo(() => {
		return {
			specialAccomodations: data?.specialAccomodations,
			mobilityAssistance: data?.mobilityAssistance,
			schedulingPreferences: {
				technicianPreference: data?.schedulingPreferences?.technicianPreference,
				preferredDayOfWeek: data?.schedulingPreferences?.preferredDayOfWeek,
			},
		} as SchedulingPreferencesData;
	}, [
		data?.specialAccomodations,
		data?.mobilityAssistance,
		data?.schedulingPreferences?.technicianPreference,
		data?.schedulingPreferences?.preferredDayOfWeek,
	]);

	const onSuccessSave = () => {
		commitDraft(patientId, successCallback);
	};

	return (
		<>
			<NotificationSnackbar
				notification={notification}
				onCloseToast={resetToast}
			/>
			<NotificationBanner
				message={updatePatientError?.message || commitDraftError?.message}
				severity="error"
				scrollIntoView
				onClose={resetUpdatePatientMutation}
			/>
			<SchedulePreferenceForm
				savedData={savedData}
				onSubmit={(newData) => {
					updatePatient(
						{
							...data,
							...newData,
						},
						getNewPatientState(patientState, 'SchedulePreferences', undefined),
						onSuccessSave
					);
				}}
				onSaveDraft={(newData) => {
					saveDraft(
						{
							...data,
							...newData,
						},
						{
							flowState: {
								...patientState.flowState,
								lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
							},
							stepState: {
								...patientState.stepState,
							},
						},
						() => {
							setNotification({
								message: 'Draft saved successfully',
								severity: 'success',
							});
						}
					);
				}}
				onValidate={validateFormatted}
				isUpdate={stepStatus === 'Complete'}
			/>
		</>
	);
}
