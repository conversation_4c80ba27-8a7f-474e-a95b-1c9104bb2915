import CardHeader from './card-header';
import { SvgIconComponent } from '@mui/icons-material';
import { PropsWithChildren } from 'react';
import Card from './card';
import { LinearProgress } from '@mui/material';

interface StepCardProps extends PropsWithChildren {
    title: string;
    icon: SvgIconComponent
    showHeader?: boolean;
    isLoading?: boolean;
}

export default function StepCard({ title, icon: Icon, showHeader = false, children, isLoading }: StepCardProps) {

    return (
        <Card elevation={0} sx={{ flex: 1, height: '100%', position: 'relative', borderBottomRightRadius: 0, borderBottomLeftRadius: 0 }}>
            {isLoading && <LinearProgress sx={{ width: '100%' }} />}
            {
                showHeader && <CardHeader
                    title={title}
                    avatar={
                        <Icon fontSize="large" />
                    }
                    slotProps={{
                        title: { variant: 'h6' },
                    }}
                />
            }
            {children}
        </Card>
    );
}