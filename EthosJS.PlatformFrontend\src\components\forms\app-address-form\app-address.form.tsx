import MainCardContainer from '@components/main-container/main-card-container';
import { useAppForm } from '@hooks/app-form';
import { Home } from 'lucide-react';
import {
	Grid2 as Grid,
	MenuItem,
	TextField,
	Box,
	Checkbox,
	FormControlLabel,
	lighten,
} from '@mui/material';
import { useStore } from '@tanstack/react-form';
import { useState, useMemo, useEffect } from 'react';
import { addressFormOptions, defaultValues } from './utils';
import { findIndex } from 'lodash';
import { ValidationErrors } from '@app-types/validation';
import { addressesWithUseTypeTransformer } from '@features/patient-create/forms';
import { formHasErrors, formHasValues } from '../utils';
import { EthosWorkflowsApiPersonalAddressDto } from '@client/workflows';

export type AddressUse = 'Billing' | 'Shipping' | 'Physical';

interface AddressFormProps {
	onAdd?: (values: EthosWorkflowsApiPersonalAddressDto) => void;
	onCancel?: (shouldRemove: boolean) => void;
	onDelete?: () => void;
	onValidate?: (data: EthosWorkflowsApiPersonalAddressDto) => Promise<ValidationErrors | undefined>;
	formValues?: EthosWorkflowsApiPersonalAddressDto;
	addressUse: AddressUse;
	addressUseRefValue: number;
	physicalAddresses?: EthosWorkflowsApiPersonalAddressDto[];
}

export default function AddressForm({
	onAdd,
	onCancel,
	onDelete,
	onValidate,
	formValues,
	addressUse,
	addressUseRefValue,
	physicalAddresses,
}: AddressFormProps) {
	const options = useMemo(() => addressFormOptions(formValues), [formValues]);
	// State for managing sameAs functionality outside the form
	const [isSameAsEnabled, setIsSameAsEnabled] = useState(false);
	const [selectedPhysicalAddress, setSelectedPhysicalAddress] = useState<string>('0');

	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const parsed = addressesWithUseTypeTransformer([value])[0];
				parsed.use = addressUseRefValue;
				const res = await onValidate?.(parsed as EthosWorkflowsApiPersonalAddressDto);
				return res;
			},
		},
	});

	const { values, canSubmit, isDirty, fieldMeta } = useStore(
		form.store,
		({ values, canSubmit, isDirty, fieldMeta, errors, errorMap }) => ({
			values,
			canSubmit,
			isDirty,
			fieldMeta,
			errors,
			errorMap,
		})
	);

	const hasValues = formHasValues(formValues, {
		...defaultValues,
		use: addressUseRefValue,
	});

	const hasErrors = formHasErrors(fieldMeta) && isDirty;

	useEffect(() => {
		if (physicalAddresses && physicalAddresses.length) {
			const currentAddressMatch = findIndex(
				physicalAddresses,
				(addr) => JSON.stringify(addr.address) === JSON.stringify(formValues?.address)
			);
			if (currentAddressMatch !== -1 && isSameAsEnabled) {
				setIsSameAsEnabled(true);
				setSelectedPhysicalAddress(currentAddressMatch.toString());
			}
		}
	}, [formValues, physicalAddresses, isSameAsEnabled]);

	// Handle changes to the sameAs checkbox
	const handleSameAsChange = (enabled: boolean) => {
		setIsSameAsEnabled(enabled);

		if (enabled && physicalAddresses && physicalAddresses.length > 0) {
			// If enabling sameAs and we have a selected address, apply it
			const index = parseInt(selectedPhysicalAddress || '0');
			if (!isNaN(index) && index >= 0 && index < physicalAddresses.length) {
				const physicalAddress = physicalAddresses[index];
				if (physicalAddress.type) {
					form.setFieldValue('type', physicalAddress.type);
				}
				form.setFieldValue('address', { ...physicalAddress.address });
				form.validate('change');
			}
		}
		if (!enabled) {
			form.setFieldValue('type', options.defaultValues.type);
			form.setFieldValue('address', { ...options.defaultValues.address });
			form.validate('change');
		}
	};

	// Handle changes to the selected physical address
	const handlePhysicalAddressChange = (addressIndex: string) => {
		setSelectedPhysicalAddress(addressIndex);
		if (isSameAsEnabled && physicalAddresses) {
			const index = parseInt(addressIndex);
			if (!isNaN(index) && index >= 0 && index < physicalAddresses.length) {
				const physicalAddress = physicalAddresses[index];
				if (typeof physicalAddress.type === 'number') {
					form.setFieldValue('type', physicalAddress.type);
				}
				form.setFieldValue('address', { ...physicalAddress.address });
			}
		}
	};

	const shouldDisable = (addressUse: AddressUse) => {
		return addressUse !== 'Physical' && isSameAsEnabled;
	};

	return (
		<MainCardContainer
			emphasis={hasErrors ? 'high' : 'low'}
			title="Add Address"
			color={hasErrors ? 'error' : 'primary'}
			icon={<Home />}
			descriptionSubheader="* Indicates a required field"
			descriptionText="Enter the address details below."
			containerSlot={
				<>
					{addressUse !== 'Physical' && (
						<Box
							sx={(theme) => ({
								display: 'flex',
								flexDirection: 'column',
								gap: 2,
								padding: 2,
								backgroundColor: lighten(theme.palette.primary.light, 0.85),
							})}>
							<Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
								<FormControlLabel
									control={
										<Checkbox
											checked={shouldDisable(addressUse)}
											onChange={(e) => handleSameAsChange(e.target.checked)}
											data-testid="address.sameAsPhysical"
										/>
									}
									label={
										<Box
											component="span"
											sx={(theme) => ({
												textTransform: 'uppercase',
												color: theme.palette.primary.dark,
											})}>
											Same as Physical Address
										</Box>
									}
								/>
							</Box>

							{isSameAsEnabled && physicalAddresses && physicalAddresses.length > 0 && (
								<Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
									<Box sx={{ fontWeight: 'medium' }}>Select Physical Address:</Box>
									<TextField
										select
										size="small"
										value={selectedPhysicalAddress}
										onChange={(e) => handlePhysicalAddressChange(e.target.value as string)}
										sx={{ minWidth: 200 }}
										slotProps={{
											input: {
												inputProps: {
													'data-testid': 'address.selectedPhysicalAddress',
												},
											},
										}}>
										{physicalAddresses.map((address, index) => {
											return (
												<MenuItem
													key={index}
													value={index.toString()}>
													{address.address?.line1}, {address.address?.city}
												</MenuItem>
											);
										})}
									</TextField>
								</Box>
							)}

							{isSameAsEnabled && physicalAddresses && physicalAddresses.length === 0 && (
								<Box sx={{ color: 'error.main' }}>
									No physical addresses available. Please add a physical address first.
								</Box>
							)}
						</Box>
					)}
				</>
			}
			footerProps={{
				primaryButton1: {
					label: hasValues ? 'Update' : 'Add',
					onClick: () => {
						const parsed = addressesWithUseTypeTransformer([values])[0];
						onAdd?.(parsed);
					},
					disabled: !canSubmit || !isDirty,
					'data-testid': 'address.submitButton',
				},
				primaryButton2: {
					label: 'Cancel',
					onClick: () => {
						form.reset();
						onCancel?.(hasValues);
					},
					'data-testid': 'address.cancelButton',
				},
				secondaryButton1: hasValues
					? {
							label: 'Delete',
							onClick: onDelete,
							color: 'error',
							'data-testid': 'address.deleteButton',
						}
					: undefined,
			}}>
			<Grid
				container
				spacing={3}>
				<Grid size={12}>
					<form.AppField name="type">
						{(field) => (
							<field.AppSelectField
								label="Address Type"
								referenceDataSetName="addressType"
								disabled={shouldDisable(addressUse)}
								data-testid="address.type"
							/>
						)}
					</form.AppField>
				</Grid>

				<Grid size={12}>
					<form.AppField name="address.line1">
						{(field) => (
							<field.AppTextField
								label="Address Line 1"
								required
								fullWidth
								disabled={shouldDisable(addressUse)}
								dataTestId="address.line1"
							/>
						)}
					</form.AppField>
				</Grid>

				<Grid size={12}>
					<form.AppField name="address.line2">
						{(field) => (
							<field.AppTextField
								label="Address Line 2"
								fullWidth
								disabled={shouldDisable(addressUse)}
								dataTestId="address.line2"
							/>
						)}
					</form.AppField>
				</Grid>

				<Grid size={4}>
					<form.AppField name="address.city">
						{(field) => (
							<field.AppTextField
								label="City"
								required
								disabled={shouldDisable(addressUse)}
								dataTestId="address.city"
							/>
						)}
					</form.AppField>
				</Grid>

				<Grid size={4}>
					<form.AppField name="address.state">
						{(field) => (
							<field.AppAutocompleteField
								label="State"
								referenceDataSetName="state"
								disabled={shouldDisable(addressUse)}
								dataTestId="address.state"
							/>
						)}
					</form.AppField>
				</Grid>

				<Grid size={4}>
					<form.AppField name="address.postalCode">
						{(field) => (
							<field.AppTextField
								label="Zip Code"
								required
								inputMode="numeric"
								disabled={shouldDisable(addressUse)}
								dataTestId="address.postalCode"
							/>
						)}
					</form.AppField>
				</Grid>
			</Grid>
		</MainCardContainer>
	);
}
