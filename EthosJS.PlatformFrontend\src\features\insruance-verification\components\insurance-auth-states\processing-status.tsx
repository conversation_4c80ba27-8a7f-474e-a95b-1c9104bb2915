import MainCardContainer from '@components/main-container/main-card-container';
import { Chip } from '@mui/material';
import { Clock } from 'lucide-react';

export default function ProcessingStatus() {
	return (
		<MainCardContainer
			title="Insurance Verification"
			headerSize="medium"
			icon={<Clock />}
			color="primary"
			emphasis="high"
			descriptionSubheader="Verifying Insurance with Carrier... This may take a few moments."
			customAction={
				<Chip
					variant="filled"
					label="Processing"
					sx={{ borderRadius: 2, backgroundColor: 'white', color: 'primary.main' }}
				/>
			}
		/>
	);
}
