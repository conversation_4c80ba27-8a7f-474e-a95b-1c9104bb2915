/**
 * QueryDto DSL for TypeScript
 * Provides a type-safe way to build query objects for the backend API
 */

// Base interface for primitive queries (PatientQ, PhysicianQ, etc.)
export interface IPrimitiveQuery {
	$type: string;
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	[key: string]: any;
}

// QueryDto types that match the C# implementation
export type QueryDto<T extends IPrimitiveQuery> =
	| AllQuery
	| LiteralQuery<T>
	| NotQuery<T>
	| AndQuery<T>
	| OrQuery<T>;

export interface AllQuery {
	$type: 'All';
}

export interface LiteralQuery<T extends IPrimitiveQuery> {
	$type: 'Literal';
	Value: T;
}

export interface NotQuery<T extends IPrimitiveQuery> {
	$type: 'Not';
	Expr: QueryDto<T>;
}

export interface AndQuery<T extends IPrimitiveQuery> {
	$type: 'And';
	Exprs: QueryDto<T>[];
}

interface OrQuery<T extends IPrimitiveQuery> {
	$type: 'Or';
	Exprs: QueryDto<T>[];
}

// Builder functions
export const Query = {
	/**
	 * Creates a query that matches all entities
	 */
	all: <T extends IPrimitiveQuery>(): QueryDto<T> => ({
		$type: 'All',
	}),

	/**
	 * Creates a query with a primitive query value
	 * @param value The primitive query value (e.g., WithFirstName, WithId, etc.)
	 */
	literal: <T extends IPrimitiveQuery>(value: T): QueryDto<T> => ({
		$type: 'Literal',
		Value: value,
	}),

	/**
	 * Creates a negated query
	 * @param expr The query expression to negate
	 */
	not: <T extends IPrimitiveQuery>(expr: QueryDto<T>): QueryDto<T> => ({
		$type: 'Not',
		Expr: expr,
	}),

	/**
	 * Creates a query that combines multiple expressions with AND
	 * @param exprs The query expressions to combine
	 */
	and: <T extends IPrimitiveQuery>(exprs: QueryDto<T>[]): QueryDto<T> => ({
		$type: 'And',
		Exprs: exprs,
	}),

	/**
	 * Creates a query that combines multiple expressions with OR
	 * @param exprs The query expressions to combine
	 */
	or: <T extends IPrimitiveQuery>(exprs: QueryDto<T>[]): QueryDto<T> => ({
		$type: 'Or',
		Exprs: exprs,
	}),
};
