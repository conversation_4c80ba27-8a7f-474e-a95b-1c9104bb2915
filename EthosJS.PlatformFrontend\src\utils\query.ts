import { GetApiReferenceSetsValuesData } from '@client/refdata';
import { GridPaginationModel } from '@mui/x-data-grid-pro';

type PageParams = GetApiReferenceSetsValuesData['query'] & { totalCount: number };

export function paginationQueryParams(paginationModel: GridPaginationModel) {
	return {
		offset: paginationModel.page * paginationModel.pageSize,
		limit: paginationModel.pageSize,
	};
}

export function getNextPageParam(lastPage: unknown) {
	// pull out the numbers we need

	const { offset, totalCount, limit } = lastPage as PageParams;

	// ensure all three are valid numbers
	if (typeof offset !== 'number' || typeof totalCount !== 'number' || typeof limit !== 'number') {
		return null;
	}

	const nextOffset = offset + limit;
	if (nextOffset >= totalCount) {
		return null;
	}
	return {
		query: {
			offset: nextOffset,
			limit,
		},
	};
}
