import { InputAdornment, lighten, <PERSON>u<PERSON><PERSON>, <PERSON>ack, StackProps, TextField, Theme, Typography } from "@mui/material"
import { DatePicker } from "@mui/x-date-pickers-pro"
import { Monitor } from "lucide-react"
import { Fragment, ReactNode, useCallback, useState } from "react"

const styles = {
   root: (t: Theme): StackProps['sx'] => {
      return {
         p: 2,
         borderWidth: 1,
         borderStyle: 'solid',
         borderColor: lighten(t.palette.primary.light, .7),
         borderRadius: 1
      }
   },
   titleContainer: (t: Theme): StackProps['sx'] => {
      return {
         color: t.palette.grey[800]
      }
   },
   filterBanner: (t: Theme): StackProps['sx'] => {
      return {
         bgcolor: lighten(t.palette.primary.light, .7),
         p: 1.3,
         borderRadius: 1,
         color: t.palette.primary.dark
      }
   },
}

export enum FieldTypeEnum {
   Text = 'TEXT',
   Select = 'SELECT',
   DatePicker = 'DATE_PICKER',
}

type FilterValue = string | number | null;

interface BaseFilterTypeConfig {
   label: string
   key: string
   icon?: ReactNode
}

interface TextFilterTypeConfig extends BaseFilterTypeConfig {
   type: FieldTypeEnum.Text
}

interface DatePickerFilterTypeConfig extends BaseFilterTypeConfig {
   type: FieldTypeEnum.DatePicker
}

interface SelectFilterTypeConfig extends BaseFilterTypeConfig {
   type: FieldTypeEnum.Select
   options: Array<{ label: string, value: string }>
}

type IFilterTypeConfig = TextFilterTypeConfig | DatePickerFilterTypeConfig | SelectFilterTypeConfig;

interface IFilterCriteria {
   title: string
   icon: ReactNode
   bannerText: string
   filterTypeConfig: Array<IFilterTypeConfig>
   value?: Record<string, FilterValue>
   onFilterValueChange?: (name: string, value: FilterValue) => void
}


const FilterCriteria = ({ title,
   icon,
   bannerText,
   filterTypeConfig = [],
   value: valueProp,
   onFilterValueChange: onFilterValueChangeProp,
}: IFilterCriteria) => {
   type FilterKey = typeof filterTypeConfig[number]['key'];

   const [fieldValuesLocal, setFilterValuesLocal] = useState(() => {
      return filterTypeConfig.reduce((acc: Record<FilterKey, FilterValue>, cur) => {
         acc = { ...acc, [cur.key]: '' }
         return acc;
      }, {})
   });

   const value = valueProp ?? fieldValuesLocal;

   const onFilterValueChange: IFilterCriteria['onFilterValueChange'] = (name, requestedValue) => {
      setFilterValuesLocal((prevState) => ({ ...prevState, [name]: requestedValue }));
      onFilterValueChangeProp?.(name, requestedValue);
   }

   return (
      <Stack sx={styles.root as StackProps['sx']} gap={2}>
         <Stack direction={'row'} gap={1} sx={styles.titleContainer as StackProps['sx']} >
            {icon}
            <Typography>{title}</Typography>
         </Stack>
         <Stack sx={styles.filterBanner as StackProps['sx']}>
            <Typography variant="body2" fontWeight={600}>{bannerText}</Typography>
         </Stack>
         <FormFields {...{ filterTypeConfig, onFilterValueChange, value }} />
      </Stack>
   )
}

const FormFields = ({ filterTypeConfig = [], value, onFilterValueChange }: Partial<IFilterCriteria>) => {

   const getFieldContextByType = useCallback((fieldType: IFilterTypeConfig) => {

      const { label } = fieldType;

      switch (fieldType.type) {
         case FieldTypeEnum.DatePicker:
            return (
               <DatePicker {...{ label }} />
            )
         case FieldTypeEnum.Select:
            const fieldValue = fieldType.options?.find((i) =>
               i.value === value?.[fieldType.key]);
            return (
               <TextField
                  select
                  {...{ label, value: fieldValue }}
                  onChange={(e) => onFilterValueChange?.(fieldType.key, e.target.value)}
                  slotProps={{
                     input: {
                        startAdornment: (
                           <InputAdornment position="start">
                              {fieldType?.icon}
                           </InputAdornment>
                        ),
                     },
                  }}
               >
                  {fieldType.options?.map((option) => (
                     <MenuItem value={option?.value} key={option?.value}>{option?.label}</MenuItem>
                  ))}
               </TextField>
            )
         case FieldTypeEnum.Text:
            return (
               <TextField
                  {...{ label, value }}
                  slotProps={{
                     input: {
                        startAdornment: (
                           <InputAdornment position="start">
                              {fieldType?.icon}
                           </InputAdornment>
                        ),
                     },
                  }}
                  onChange={(e) => onFilterValueChange?.(fieldType.key, e.target.value)}
               />
            )
         default:
            return 'No Context Available';
      }
   }, [filterTypeConfig, value])

   return (
      <>
         {filterTypeConfig.map((config) => {
            return (
               <Fragment key={config.key}>
                  {getFieldContextByType(config)}
               </Fragment>
            )
         })}
      </>
   )
}

export default FilterCriteria;