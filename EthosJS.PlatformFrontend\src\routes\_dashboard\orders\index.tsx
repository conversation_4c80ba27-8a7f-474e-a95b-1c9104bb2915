import { Expand<PERSON>ore, Add, ShoppingCart, PunchClock } from '@mui/icons-material';
import { Autocomplete, Box, Button, Menu, MenuItem, Paper, Stack, Tab, Tabs, TextField, } from '@mui/material'
import { useQuery } from '@tanstack/react-query';
import { createFileRoute } from '@tanstack/react-router'
import { useMemo, useRef, useState } from 'react';
import LoadingComponent from '@components/loading-component';
import PageContainer from '@components/page-container';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { DataGridPro, GridColDef } from '@mui/x-data-grid-pro';
import { paginationQueryParams } from '@utils/query';
import { OrderDto } from '@client/workflows';
import { postApiOrderSearchOptions } from '@client/workflows/@tanstack/react-query.gen';


const columns: GridColDef<OrderDto>[] = [
    {
        field: 'patient.name',
        headerName: 'First Name',
        width: 150,
        flex: 1,
        valueGetter: (_params, row) => row.patientId
    },
    {
        field: 'patient.name.lastName',
        headerName: 'Last Name',
        width: 150,
        flex: 1,
        valueGetter: (_params, row) => row.patientId
    },
    {
        field: 'patient.dateOfBirth',
        headerName: 'Date of Birth',
        width: 120,
        flex: 1,
        // valueGetter: (params, row) => {
        //     const { dateOfBirth } = row.patient ?? {};
        //     if (dateOfBirth) {
        //         return dayjs(dateOfBirth).format("MM/DD/YYYY");
        //     }
        //     return dateOfBirth;
        // }
        valueGetter: (_params, row) => {
            return row.patientId
        }
    },
    {
        field: 'createEvent.timestamp',
        headerName: 'Created Date',
        width: 180,
        flex: 1,
        // valueGetter: (params, row) => {
        //     const { timestamp } = row.createEvent ?? {};
        //     if (timestamp) {
        //         return dayjs(timestamp).format("MM/DD/YYYY");
        //     }
        //     return timestamp;
        // }
        valueGetter: (_params, row) => {
            return row.patientId
        }
    },
    {
        field: 'studies',
        headerName: 'Studies Count',
        width: 120,
        flex: 1,
        valueGetter: (_params, row) => {
            return row.patientId
        }
    },
    // {
    //     field: 'actions',
    //     headerName: 'Actions',
    //     width: 120,
    //     sortable: false,
    //     renderCell: ({ row }) => (
    //         <Button
    //             variant="text"
    //             onClick={() => {
    //                 window.location.href = `/orders/${row.id}`;
    //             }}
    //         >
    //             View Order
    //         </Button>
    //     )
    // },
]

export const Route = createFileRoute('/_dashboard/orders/')({
    component: RouteComponent,
    pendingComponent: () => <LoadingComponent />,
    loader: ({ context: { queryClient } }) => queryClient.ensureQueryData(postApiOrderSearchOptions(
        {
            scopes: [PatientCreate.value, PatientRead.value],
            responseType: 'json'
        }
    )),
})

function RouteComponent() {

    const [open, setOpen] = useState(false);
    const anchorEl = useRef<HTMLButtonElement | null>(null);
    const [selectedItem, setSelectedItem] = useState<string>("All");

    const [value, setValue] = useState(2);

    const [paginationModel, setPaginationModel] = useState({
        pageSize: 100,
        page: 0,
    });

    const { data, isFetching } = useQuery(
        postApiOrderSearchOptions(
            {
                query: paginationQueryParams(paginationModel),
                scopes: [PatientCreate.value, PatientRead.value],
                responseType: 'json'
            }
        )
    )

    const rowCountRef = useRef(data?.totalCount || 0);
    const rowCount = useMemo(() => {
        if (data?.totalCount !== undefined) {
            rowCountRef.current = data?.totalCount;
        }
        return rowCountRef.current;
    }, [data?.totalCount]);

    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };
    const handleClick = (_event: React.MouseEvent<HTMLButtonElement>) => {
        setOpen(!open);
    };
    const handleClose = () => {
        setOpen(false);
    };
    const handleSelect = (item: string) => {
        setSelectedItem(item);
        handleClose();
    }
    return (
        <PageContainer title="Orders" icon={ShoppingCart} actions={
            <Stack direction="row" spacing={2}>
                <Button variant='contained' color="secondary" startIcon={<PunchClock />}>
                    Recent Orders
                </Button>
                <Button variant='contained' color="primary" endIcon={<Add />}>
                    New Order
                </Button>
            </Stack>
        }>
            <Box sx={{ p: 2, flexGrow: 1, overflow: 'auto', minHeight: 0 }}>
                {/* <Box sx={{ display: 'flex', gap: 2, mb: "25px", flexWrap: 'wrap' }}>
                    <StatsCard
                        title="Completed"
                        mainContent="56"
                        subContent="Today"
                        icon={CheckCircle}
                        iconType='success'
                        type="info"
                    />
                    <StatsCard
                        title="Upcoming Appointments"
                        mainContent="5"
                        subContent="+ 2"
                        icon={Timeline}
                        iconType='info'
                        type="info"
                    />
                    <StatsCard
                        title="Pre-verified"
                        mainContent="70%"
                        subContent="Auto-checked"
                        icon={ShieldMoon}
                        iconType='success'
                        type="success"
                    />
                    <StatsCard
                        title="AI Processing"
                        mainContent="78%"
                        subContent="Auto-verified"
                        icon={Person4}
                        iconType='info'
                        type="info"
                    />
                    <StatsCard
                        title="Needs Review"
                        mainContent="8"
                        subContent="Manual check"
                        icon={CalendarMonth}
                        iconType='error'
                        type="error"
                    />
                    <StatsCard
                        title="Avg. Processing"
                        mainContent="2.5m"
                        subContent="Per order"
                        icon={CalendarToday}
                        iconType='success'
                        type="info"
                    />
                    <StatsCard
                        title="Total Orders"
                        mainContent="156"
                        subContent="+12 today"
                        icon={Checklist}
                        iconType='primary'
                        type="success"
                    />
                </Box> */}
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'space-between', mb: '25px' }}>
                    <Box sx={{ display: 'flex', gap: 2, flex: 1, alignItems: 'center' }}>
                        <Button
                            ref={anchorEl}
                            aria-controls={open ? 'basic-menu' : undefined}
                            aria-haspopup="true"
                            aria-expanded={open ? 'true' : undefined}
                            onClick={handleClick}
                            variant='contained'
                            endIcon={
                                <ExpandMore />
                            }
                        >
                            {selectedItem}
                        </Button>
                        <Menu
                            anchorEl={anchorEl.current}
                            open={open}
                            onClose={handleClose}
                            MenuListProps={{
                                'aria-labelledby': 'basic-button',
                            }}
                        >
                            <MenuItem onClick={() => handleSelect("All")}>All</MenuItem>
                            <MenuItem onClick={() => handleSelect("Upcoming")}>Upcoming</MenuItem>
                            <MenuItem onClick={() => handleSelect("Past")}>Past</MenuItem>
                        </Menu>
                        <Autocomplete size='small' disablePortal sx={{ minWidth: 200, backgroundColor: '#fff' }} renderInput={(params) => <TextField {...params} label="Search..." />} options={[]} />
                    </Box>
                    <Box>
                        <Tabs component={Paper} value={value} onChange={handleChange} aria-label="tabs">
                            <Tab label="All" />
                            <Tab label="Pending Review" />
                            <Tab label="In Progress" />
                            <Tab label="Completed" />
                        </Tabs>
                    </Box>
                </Box>
                <Box sx={{ minHeight: 400, maxHeight: "100%", width: '100%', display: 'flex', flexDirection: 'column', mb: 2, }}>
                    <DataGridPro
                        loading={isFetching}
                        rows={data?.items ?? []}
                        rowCount={rowCount}
                        paginationMode="server"
                        paginationModel={paginationModel}
                        onPaginationModelChange={setPaginationModel}
                        pageSizeOptions={[10, 25, 50, 100]}
                        pagination
                        columns={columns}
                    />
                </Box>
            </Box>
        </PageContainer >
    )
}

