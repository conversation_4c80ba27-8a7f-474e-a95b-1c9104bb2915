import { CSSProperties, ReactNode } from 'react';
import { alpha, List, Stack, styled } from '@mui/material';

import MenuHeader, { MenuHeaderProps } from './menu-header';
import MenuItem, { IMenuItem } from './menu-item';

// Type definitions
type DescriptionPanelProps = {
	text: string;
	icon?: ReactNode;
	style?: CSSProperties;
};

interface MenuProps extends MenuHeaderProps {
	descriptionPanelProps?: DescriptionPanelProps;
	topContainer?: ReactNode;
	bottomContainer?: ReactNode;
	items: Array<IMenuItem>;
	color?: 'primary';
	disableIfNotCompletedStep?: boolean;
	headerExtra?: ReactNode;
}

// Styled components
const MenuContainer = styled(Stack, {
	shouldForwardProp: (prop) => prop !== 'color',
})<{ color?: 'primary' }>(({ theme, color }) => {
	const { palette } = theme;

	return {
		padding: theme.spacing(1.2),
		borderRadius: '0.625rem',
		borderWidth: 1.5,
		borderStyle: 'solid',
		...(color === 'primary' && {
			background: palette.common.white,
			borderColor: alpha(palette.primary.light, 0.4),
			...theme.applyStyles('dark', {
				background: palette.background.paper,
				borderColor: alpha(palette.primary.main, 0.6),
			}),
		}),
	};
});

const StyledList = styled(List)(({ theme }) => ({
	gap: theme.spacing(1.2),
	display: 'flex',
	flexDirection: 'column',
	marginBlockEnd: 0,
}));

function Menu(props: MenuProps) {
	const {
		title,
		icon,
		items = [],
		subtitle,
		topContainer,
		bottomContainer,
		color = 'primary',
	} = props;

	return (
		<MenuContainer color={color}>
			<MenuHeader {...{ title, icon, subtitle }} />
			{/* Top Container */}
			{topContainer}
			<StyledList>
				{items?.map((item, index) => (
					<MenuItem
						key={index}
						{...item}
					/>
				))}
			</StyledList>
			{/* Bottom Container */}
			{bottomContainer}
		</MenuContainer>
	);
}

export type { MenuProps };
export default Menu;
