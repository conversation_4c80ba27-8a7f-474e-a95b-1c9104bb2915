import CollapsibleMainCardContainer from "@components/collapsible-main-card-container";
import StatusBanner from "@components/status.banner";
import { Status } from "@config/status";
import { NotesOutlined } from "@mui/icons-material";
import { Stack, Typography } from "@mui/material";
import { useState } from "react";

const TechnicalNotes = () => {
   const [isExpanded, setIsExpanded] = useState<boolean>(false);

   return (
      <CollapsibleMainCardContainer
         mainContainerProps={{
            title: 'Technician Notes',
            icon: <NotesOutlined />,
            emphasis: isExpanded ? 'high' : 'low',
         }}
         onToggleCollapse={setIsExpanded}
         collapse={isExpanded}
      >
         <Stack gap={2}>
            <StatusBanner
               title='Patient bathroom break'
               subTitle='Patient had been holding for 30 minutes. Checked electrode impedances upon return - all within normal limits. C3-A2 slightly loose, readjusted.'
               status={Status.Warning}
               showIcon={false}
               renderContent={(theme) => {
                  return <Typography variant='body2' sx={{ color: theme.title.foregroundColor }}>Added by <PERSON>, May 7, 2025 at 9:15 AM</Typography>
               }}
            />
            <StatusBanner
               title='Patient bathroom break'
               subTitle='Patient had been holding for 30 minutes. Checked electrode impedances upon return - all within normal limits. C3-A2 slightly loose, readjusted.'
               status={Status.Error}
               renderContent={(theme) => {
                  return <Typography variant='body2' sx={{ color: theme.title.foregroundColor }}>Added by John Scott, May 7, 2025 at 9:15 AM</Typography>
               }}
            />
            <StatusBanner
               title='Patient bathroom break'
               subTitle='Patient had been holding for 30 minutes. Checked electrode impedances upon return - all within normal limits. C3-A2 slightly loose, readjusted.'
               status={Status.Process}
               renderContent={(theme) => {
                  return <Typography variant='body2' sx={{ color: theme.title.foregroundColor }}>Added by John Scott, May 7, 2025 at 9:15 AM</Typography>
               }}
            />
         </Stack>
      </CollapsibleMainCardContainer>
   )
}

export default TechnicalNotes;