import { <PERSON><PERSON>, Stack } from '@mui/material';
import MenuHeader, { MenuHeaderProps } from './menu-header';
import { Prettify, ToDiscriminatedUnion } from '@utils/types';
import HalfCircleProgress from '@components/half-circle-progress';
import MenuDescription from './menu-description';

type SecondaryHeaderType = 'Add' | 'Edit' | 'Delete' | 'Workflow' | 'none';

export interface SecondaryMenuHeaderBaseProps extends MenuHeaderProps {
	type: SecondaryHeaderType;
	description?: string;
}

export interface SecondaryMenuHeaderNoneProps extends SecondaryMenuHeaderBaseProps {
	type: 'none';
}

export interface SecondaryMenuHeaderAddProps extends SecondaryMenuHeaderBaseProps {
	type: 'Add';
	onAdd: () => void;
}

export interface SecondaryMenuHeaderEditProps extends SecondaryMenuHeaderBaseProps {
	type: 'Edit';
	onEdit: () => void;
}

export interface SecondaryMenuHeaderDeleteProps extends SecondaryMenuHeaderBaseProps {
	type: 'Delete';
	onDelete: () => void;
}

export interface SecondaryMenuHeaderWorkflowProps extends SecondaryMenuHeaderBaseProps {
	type: 'Workflow';
	progress: number;
	color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
}

export type SecondaryMenuHeaderProps = Prettify<
	ToDiscriminatedUnion<{
		Add: SecondaryMenuHeaderAddProps;
		Edit: SecondaryMenuHeaderEditProps;
		Delete: SecondaryMenuHeaderDeleteProps;
		Workflow: SecondaryMenuHeaderWorkflowProps;
		none: SecondaryMenuHeaderNoneProps;
	}>
>;

export default function SecondaryMenuHeader({
	title,
	subtitle,
	icon,
	type,
	description,
	showIcon = false,
	iconVariant = 'text',
	iconSize = 'medium',
	iconColor = 'gray',
	...rest
}: SecondaryMenuHeaderProps) {
	const renderAction = () => {
		switch (type) {
			case 'Add': {
				const { onAdd } = rest as SecondaryMenuHeaderAddProps;
				return (
					<Button
						onClick={onAdd}
						variant="contained">
						Add
					</Button>
				);
			}
			case 'Edit': {
				const { onEdit } = rest as SecondaryMenuHeaderEditProps;
				return (
					<Button
						onClick={onEdit}
						variant="contained">
						Edit
					</Button>
				);
			}
			case 'Delete': {
				const { onDelete } = rest as SecondaryMenuHeaderDeleteProps;
				return (
					<Button
						onClick={onDelete}
						variant="contained">
						Delete
					</Button>
				);
			}
			case 'Workflow': {
				const { progress, color } = rest as SecondaryMenuHeaderWorkflowProps;
				return (
					<HalfCircleProgress
						value={progress}
						color={color}
					/>
				);
			}
			case 'none':
				return null;
		}
	};

	return (
		<Stack gap={1}>
			<Stack
				direction="row"
				justifyContent="space-between">
				<MenuHeader {...{ title, subtitle, icon, showIcon, iconVariant, iconSize, iconColor }} />
				{renderAction()}
			</Stack>
			{description && <MenuDescription text={description} />}
		</Stack>
	);
}
