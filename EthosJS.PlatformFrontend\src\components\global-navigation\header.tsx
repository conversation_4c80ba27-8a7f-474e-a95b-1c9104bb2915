import { Fragment, ReactNode } from 'react';
import { Box, Stack, Toolbar } from '@mui/material';

// styles
import { getRootStyle } from './styles';
import Profile, { IProfile } from './profile';

interface IHeader {
  profileProps?: IProfile;
  actionItems?: Array<ReactNode>;
  content?: ReactNode
}

const Header: React.FC<IHeader> = (props) => {
  const { profileProps, actionItems = [], content } = props;

  return (
    <Toolbar sx={getRootStyle}>
      <Box>
         {content}
      </Box>

      <Stack direction={'row'} gap={2}>
        {actionItems.length ? (
          <Stack direction={'row'} gap={2} alignItems={'center'}>
            {actionItems.map((i, index) => (
              <Fragment key={index}>{i}</Fragment>
            ))}
          </Stack>
        ) : null}

        <Profile {...profileProps} />
      </Stack>
    </Toolbar>
  );
};

export default Header;
