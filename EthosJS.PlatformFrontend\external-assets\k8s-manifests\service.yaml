apiVersion: v1
kind: Service
metadata:
  name: ethosjs-platformfrontend-nodeport
  namespace: ethos-ns-dev
spec:
  selector:
    app: ethosjs-platformfrontend
  ports:
    - protocol: TCP
      port: 80        # Cluster-wide service port
      targetPort: 3000 # Port inside the container
      nodePort: 30080  # Exposed port on worker nodes
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: ethosjs-platformfrontend-loadbalancer
  namespace: ethos-ns-dev
spec:
  selector:
    app: ethosjs-platformfrontend
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: LoadBalancer
