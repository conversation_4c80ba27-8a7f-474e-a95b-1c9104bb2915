import { Store } from "@tanstack/store";

type ScoringState = {
   filterCriteria: {
      orderId: null | string
      studyId: null | string
      visitDate: null | string
   }
};

const defaultState: ScoringState = {
   filterCriteria: {
      orderId: null,
      studyId: null,
      visitDate: null
   }
};

const store = new Store<ScoringState>(defaultState);

const actions = {
   setFilterCriteria: (fieldName: string, fieldValue: string | number | null) => {
      store.setState(prevState => {
         return { ...prevState, filterCriteria: {
            ...prevState.filterCriteria, [fieldName]: fieldValue } }
      })
   }
}

const useScoringStore = () => ({ store, actions });

export default useScoringStore;