import type React from "react"
import { lighten, <PERSON>, ListItemIcon, ListItemText, Tooltip, type LinkProps } from "@mui/material"
import { styled } from "@mui/material/styles"
import { Headset, ChevronRight } from "@mui/icons-material"

const StyledLink = styled(Link)(({ theme }) => ({
    height: 44,
    minWidth: 44,
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-start",
    borderRadius: theme.spacing(0.75),
    padding: theme.spacing(0.5),
    backgroundColor: lighten(theme.palette.success.light, 0.85),
    fontWeight: 600,
    color: "#027A48",
    textDecoration: "none",
    transition: "background-color 0.2s",
    cursor: "pointer",
    overflow: "hidden",
    "&:hover": {
        backgroundColor: lighten(theme.palette.success.light, 0.75),
        textDecoration: "none",
    },
}))

const StyledListItemIcon = styled(ListItemIcon)(({ theme }) => ({
    color: 'inherit',
    width: theme.spacing(4.25),
    minWidth: theme.spacing(4),
    padding: theme.spacing(0.5),
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    '& svg': {
        fontSize: 24,
    },
}))

const StyledListItemText = styled(ListItemText)(({ theme }) => ({
    marginLeft: theme.spacing(1),
}))

interface LiveSupportLinkProps extends LinkProps {
    children?: React.ReactNode
    sideBarOpen?: boolean
}

export default function LiveSupportLink({ children = "Live Support", sideBarOpen, ...props }: LiveSupportLinkProps) {
    return (
        <Tooltip title="Live Support" placement="right">
            <StyledLink {...props}>
                <StyledListItemIcon>
                    <Headset />
                </StyledListItemIcon>
                {sideBarOpen && (
                    <>
                        <StyledListItemText
                            primary={children}
                        />
                        <ChevronRight sx={{ fontSize: 20, ml: 'auto' }} />
                    </>
                )}
            </StyledLink>
        </Tooltip>
    )
}