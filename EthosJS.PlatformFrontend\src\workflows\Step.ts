import { Store } from '@tanstack/store';
import { IValidationRule, ITypeAnnotation, IStep, ITypeDef } from './TypeDef';
import { IExpr } from './expressions';
import { Status } from '@components/left-menu';
import { WorkflowStateResponse } from '@models/Workflow.model';
import { Flow } from './Flow';
import { pascalToSpacedWords } from '@utils/generator-helper';

interface IStepState {
  status: Status;
  stepMenuDisable: boolean;
  stepData: Record<string, any>;
}

export class Step {
  name: string;
  displayName: string;
  description: string | null;
  from: string | null;
  to: string;
  data: string; // type name
  isAuto: boolean;
  isTrivial: boolean;
  isFinal: boolean;
  validation: Array<IValidationRule<IExpr<ITypeAnnotation>>> = [];
  stepDataFields: Record<string, ITypeDef> = {};

  flowRef: Flow;

  stepStore = new Store<IStepState>({
    status: Status.None,
    stepMenuDisable: true,
    stepData: {}
  });

  constructor(
    {
      name,
      description = null,
      from = null,
      to,
      data,
      is_auto = false,
      is_trivial = false,
      is_final = false,
      validation = []
    }: IStep,
    flowRef: Flow
  ) {
    this.flowRef = flowRef;
    this.name = name;
    this.displayName = pascalToSpacedWords(name).replace(/^Add\s+/, '');
    this.description = description;
    this.from = from;
    this.to = to;
    this.data = data;
    this.isAuto = is_auto;
    this.isTrivial = is_trivial;
    this.isFinal = is_final;
    this.validation = validation;
  }

  getDataStructure() {
    return this.flowRef?.getStepDataStructure(this.data);
  }

  getFieldStructure(key: string) {
    return this.flowRef.getStepDataStructure(key);
  }

  getStore() {
    return this.stepStore;
  }

  getStatus() {
    return this.stepStore.state.status;
  }

  calculateStepStatus(data: WorkflowStateResponse) {
    const { pastTransitions, draftTransitions, stateData } = data;

    const draftTransition = draftTransitions.find((transition) => transition.key === this.name);
    const completedTransition = pastTransitions.find((transition) => transition.key === this.name);
    if (pastTransitions.length === 0 && this.from === null) {
      this.stepStore.setState((state) => ({
        ...state,
        stepData: (draftTransition?.data as Record<string, any>) ?? {},
        status: Status.InProgress,
        stepMenuDisable: false
      }));
      return;
    }

    if (completedTransition) {
      this.stepStore.setState((state) => ({
        ...state,
        stepData: (completedTransition.data as Record<string, any>) ?? {},
        status: Status.Completed,
        stepMenuDisable: false
      }));
      return;
    }
    const InProgress = stateData && stateData.$type === this.from;
    if (InProgress || draftTransition) {
      this.stepStore.setState((state) => ({
        ...state,
        stepData: (draftTransition?.data as Record<string, any>) ?? {},
        status: Status.InProgress,
        stepMenuDisable: false
      }));
      return;
    }
  }
}
