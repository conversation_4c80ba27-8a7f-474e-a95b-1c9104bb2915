export const ENTITY_TYPES = {
	EditRecord: 'EditRecord',
	FileMetadata: 'FileMetadata',
	WorkflowDraftTransition: 'WorkflowDraftTransition',
	WorkflowEntityLink: 'WorkflowEntityLink',
	WorkflowInstance: 'WorkflowInstance',
	WorkflowTransition: 'WorkflowTransition',
	Draft: 'Draft',
	Note: 'Note',
	Address: 'Address',
	Insurance: 'Insurance',
	InsuranceHolderData: 'InsuranceHolderData',
	PhoneNumberWithUseData: 'PhoneNumberWithUseData',
	Patient: 'Patient',
	Patient<PERSON><PERSON>ian: 'PatientGuardian',
	PatientAppointment: 'PatientAppointment',
	PatientAppointmentConfirmation: 'PatientAppointmentConfirmation',
	PersonalContactDetail: 'PersonalContactDetail',
	PersonalEmail: 'PersonalEmail',
	PersonalAddress: 'PersonalAddress',
	PersonalPhoneNumber: 'PersonalPhoneNumber',
	PersonalEmergencyContact: 'PersonalEmergencyContact',
	OrganizationContactDetail: 'OrganizationContactDetail',
	OrganizationEmail: 'OrganizationEmail',
	OrganizationAddress: 'OrganizationAddress',
	OrganizationPhoneNumber: 'OrganizationPhoneNumber',
	Order: 'Order',
	Study: 'Study',
	Provider: 'Provider',
	CareLocation: 'CareLocation',
	Room: 'Room',
	Equipment: 'Equipment',
	Physician: 'Physician',
	PhysicianCareLocationRelation: 'PhysicianCareLocationRelation',
	Technician: 'Technician',
	TechnicianRole: 'TechnicianRole',
	TechnicianShiftPreference: 'TechnicianShiftPreference',
	TechnicianCareLocationRelation: 'TechnicianCareLocationRelation',
	TechnicianAppointment: 'TechnicianAppointment',
	InsuranceVerification: 'InsuranceVerification',
	SchedulingConstraint: 'SchedulingConstraint',
} as const;

export type EntityType = (typeof ENTITY_TYPES)[keyof typeof ENTITY_TYPES];
