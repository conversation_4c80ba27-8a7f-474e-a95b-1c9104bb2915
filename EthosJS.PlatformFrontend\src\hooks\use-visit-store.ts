import { Store } from '@tanstack/react-store';
import { visitSteps } from '../routes/_dashboard/patients/$patientId/visit/route';
import { Status } from '@components/left-menu';

type visitState = {
	steps: typeof visitSteps;
	currentStep: (typeof visitSteps)[number] | null;
	selectedStep: (typeof visitSteps)[number] | null;
	status: 'idle' | 'loading' | 'success' | 'error';
};

const defaultState: visitState = {
	steps: visitSteps,
	currentStep: visitSteps[0],
	selectedStep: null,
	status: 'idle',
};

const store = new Store<visitState>(defaultState);

const actions = {
	setDefaultState: () => {
		const newSteps = visitSteps.map((step, index) =>
			// TODO: Needs to change the login once create appointment done
			index === 0 ? { ...step, status: Status.InProgress } : step
		) as typeof visitSteps;
		store.setState((prevState) => ({ ...prevState, steps: newSteps }));
	},
	updateState: (state: Partial<visitState>) => {
		store.setState((prev) => ({
			...prev,
			...state,
		}));
	},
	updateStepStatus: (key: string, status: Status) => {
		store.setState((prev) => ({
			...prev,
			steps: prev.steps.map((step) => {
				if (step.key === key) {
					return {
						...step,
						status,
					};
				}
				return step;
			}),
		}));
	},
	moveToNextStep: () => {
		store.setState((prev) => {
			const currentIndex = prev.steps.findIndex((step) => step.key === prev.currentStep?.key);
			if (currentIndex < prev.steps.length - 1) {
				return {
					...prev,
					currentStep: prev.steps[currentIndex + 1],
					selectedStep: null,
				};
			}
			return prev;
		});
	},
	resetState: () => {
		store.setState(() => defaultState);
	},
};

const useVisitStore = () => ({ store, actions });

export default useVisitStore;
