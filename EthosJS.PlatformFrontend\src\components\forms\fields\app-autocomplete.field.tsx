import { ReferenceDataSet, ReferenceDataSetKeyValueDto } from '@client/refdata';
import {
	getApiReferenceSetsValuesInfiniteOptions,
	getApiReferenceSetsKeysByIdOptions,
} from '@client/refdata/@tanstack/react-query.gen';
import { useFieldContext } from '@hooks/form-context';
import {
	TextField,
	Autocomplete,
	AutocompleteProps,
	debounce,
	MenuItem,
	Stack,
	Typography,
} from '@mui/material';
import { useStore } from '@tanstack/react-form';
import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { SelectFieldOptionType } from './FieldPropType';
import { filter, flatMap, map } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { extractProblemDetails } from '@utils/errors';
import { getNextPageParam } from '@utils/query';

type BaseAutocompleteFieldProps = Omit<
	AutocompleteProps<SelectFieldOptionType, false, false, false>,
	'renderInput' | 'options'
> & {
	showDescription?: boolean;
	name?: string;
	dataTestId?: string;
};

interface OptionsAutocompleteFieldProps extends BaseAutocompleteFieldProps {
	options: SelectFieldOptionType[];
	referenceDataSetName?: never;
	version?: never;
	label: string;
	required?: boolean;
}

interface ReferenceDataAutocompleteFieldProps extends BaseAutocompleteFieldProps {
	options?: never;
	referenceDataSetName: string;
	version?: string;
	label: string;
	required?: boolean;
}

type AppAutocompleteFieldProps =
	| OptionsAutocompleteFieldProps
	| ReferenceDataAutocompleteFieldProps;

export default function AppAutocompleteField({
	options,
	referenceDataSetName,
	// TODO: Crosswalk v2 version is required -> default to ='latest'
	version,
	label,
	required,
	slotProps,
	showDescription = false,
	name,
	dataTestId,
	...props
}: AppAutocompleteFieldProps) {
	const field = useFieldContext<SelectFieldOptionType['value'] | null>();
	const value = useStore(field.store, (state) => state.value);
	const [searchValue, setSearchValue] = useState('');
	const [debouncedSearchValue, setDebouncedSearchValue] = useState('');
	const [selectedOption, setSelectedOption] = useState<SelectFieldOptionType | null>(null);

	// Fetch the reference data value by ID if we have a value
	// TODO: Crosswalk v2 endpoint api/crosswalks/{name}/{version}/values/{id}
	const { data: valueData } = useQuery({
		...getApiReferenceSetsKeysByIdOptions({
			path: { id: value as unknown as number },
			responseType: 'json',
		}),
		enabled: !!referenceDataSetName && !!value && !selectedOption && Number(value) > -1,
	});

	useEffect(() => {
		if (valueData) {
			const {
				value: { id, values },
			} = valueData as {
				set: ReferenceDataSet;
				value: ReferenceDataSetKeyValueDto;
			};
			const option: SelectFieldOptionType = {
				//@ts-expect-error Type Mismatch
				label: values?.name ?? 'Unk',
				//@ts-expect-error Type Mismatch
				description: values?.code ?? 'Unk',
				value: id ? String(id) : '',
			};
			setSelectedOption(option);
			setSearchValue(option.label);
		}
	}, [valueData]);

	const { data, isFetching, fetchNextPage, hasNextPage, error } = useInfiniteQuery({
		//TODO: Crosswalk v2 endpoint api/crosswalks/{name}/{version}/values
		...getApiReferenceSetsValuesInfiniteOptions({
			query: {
				offset: 0,
				limit: 100,
				//TODO: Crosswalk v2 referenceDataSetName -> crosswalName
				setName: referenceDataSetName,
				version,
				// TODO: Crosswalk v2 filter changes required
				filter: debouncedSearchValue ? `name sw ${debouncedSearchValue}` : undefined,
			},
			responseType: 'json',
		}),
		enabled: !!referenceDataSetName,
		getNextPageParam: getNextPageParam,
	});

	const debouncedSearch = useMemo(
		() =>
			debounce((searchTerm: string) => {
				setDebouncedSearchValue(searchTerm);
			}, 300),
		[]
	);

	useEffect(() => {
		if (searchValue !== debouncedSearchValue) {
			debouncedSearch(searchValue);
		}
	}, [searchValue, debouncedSearchValue, debouncedSearch]);

	const items = flatMap(data?.pages, 'items') ?? [];
	const autocompleteOptions =
		options ||
		map(
			filter(items, (item): item is ReferenceDataSetKeyValueDto & { id: string } => !!item.id),
			(item) => ({
				label: item.values?.name ?? 'Unk',
				description: item.values?.code,
				value: item.id,
			})
		);

	const shouldShowError = field.getMeta().isTouched || field.getMeta().isDirty;
	const errors = shouldShowError ? field.getMeta().errors.map((error) => error.message) : [];

	return (
		<Autocomplete
			value={selectedOption}
			onChange={(_, newValue) => {
				field.setValue(newValue?.value || null);
				setSelectedOption(newValue);
				setSearchValue(newValue?.label ?? '');
			}}
			inputValue={searchValue}
			onInputChange={(_, newValue, reason) => {
				if (reason === 'input') {
					setSearchValue(newValue);
				}
				if (reason === 'reset' || reason === 'clear') {
					setSearchValue('');
				}
				if (reason === 'blur') {
					field.handleBlur();
					setSearchValue('');
				}
			}}
			options={autocompleteOptions as SelectFieldOptionType[]}
			getOptionLabel={(option) => option.label}
			fullWidth
			loading={isFetching}
			filterOptions={(x) => x}
			loadingText="Loading..."
			noOptionsText={error ? extractProblemDetails(error).title : 'No options'}
			renderInput={(params) => (
				<TextField
					{...params}
					data-testid={dataTestId}
					name={name}
					label={label}
					required={required}
					error={shouldShowError && errors.length > 0}
					helperText={shouldShowError ? errors.join(', ') : undefined}
				/>
			)}
			renderOption={({ key, ...params }, option) => (
				<MenuItem
					key={`${key}-${option.description}`}
					{...params}>
					<Stack>
						<Typography
							variant="body1"
							color="text.secondary">
							{option.label}
						</Typography>
						{showDescription && (
							<Typography
								variant="caption"
								color="text.secondary">
								{option.description}
							</Typography>
						)}
					</Stack>
				</MenuItem>
			)}
			slotProps={{
				listbox: {
					onScroll: (event) => {
						const listbox = event.currentTarget;
						const scrollPosition = listbox.scrollTop + listbox.clientHeight;
						const scrollThreshold = listbox.scrollHeight - 50; // Within 50px of bottom
						if (scrollPosition >= scrollThreshold && hasNextPage && !isFetching) {
							fetchNextPage();
						}
					},
					style: { maxHeight: 300, overflow: 'auto' },
				},
				...slotProps,
			}}
			{...props}
		/>
	);
}
