import StepCardControl from '@components/step-card-control';
import { Box, Button, CardContent } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-form';
import { Receipt } from 'lucide-react';
import { InsurancesData } from '@features/patient-create/types';
import { FormProps } from '@components/forms/predefined-form-props';
import {
	formatInsuranceSummary,
	InsuranceForm,
	insuranceFormOptions,
} from '@components/forms/app-insurance-form';
import { useMemo } from 'react';
import { getErrorsForIndexField } from '@utils/forms';
import ArrayFormContainer from '@components/forms/app-array-form-container';

function insurancesOptions(savedData?: InsurancesData, isUpdate?: boolean) {
	const hasSavedData = savedData && savedData.insurances && savedData.insurances.length > 0;
	return {
		defaultValues: {
			insurances: !isUpdate && !hasSavedData ? [insuranceFormOptions().defaultValues] : [],
			...(hasSavedData ? savedData : {}),
		},
	};
}

export default function InsurancesForm({
	onSubmit,
	onSaveDraft,
	onValidate,
	savedData,
	patientName,
	dateOfBirth,
	isUpdate,
}: FormProps<InsurancesData> & { patientName: string; dateOfBirth: string }) {
	const options = useMemo(() => insurancesOptions(savedData, isUpdate), [savedData, isUpdate]);

	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(value);
				return res;
			},
		},
		onSubmit: async ({ value }) => {
			onSubmit(value);
		},
	});

	const values = useStore(form.store, (state) => state.values);

	return (
		<Box
			component="form"
			sx={{ height: '100%' }}
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}>
			<CardContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
				<form.AppField
					name="insurances"
					mode="array">
					{({ pushValue, removeValue, state, replaceValue }) => {
						return (
							<ArrayFormContainer
								pushValue={pushValue}
								removeValue={removeValue}
								replaceValue={replaceValue}
								dataTestId="insurances"
								state={{
									value: state.value ?? [],
								}}
								onValidate={async (data, index) => {
									const insurances =
										state.value?.map((_, j) => {
											if (j === index) {
												return data;
											}
											return state.value?.[j];
										}) ?? [];
									const vals = {
										insurances,
									} as InsurancesData;
									const res = await onValidate(vals);
									return getErrorsForIndexField(`insurances[${index}]`, res);
								}}
								isUpdate={isUpdate}
								renderForm={function (props) {
									return (
										<InsuranceForm
											patientName={patientName}
											dateOfBirth={dateOfBirth}
											{...props}
										/>
									);
								}}
								defaultValues={insuranceFormOptions().defaultValues}
								formatSummary={formatInsuranceSummary}
								mainCardContainerProps={{
									title: 'Insurance Information',
									icon: <Receipt />,
									color: 'primary',
									emphasis: isUpdate ? 'high' : 'low',
									primaryActionType: 'Add',
								}}
								initialOpen={true}
							/>
						);
					}}
				</form.AppField>
			</CardContent>
			<StepCardControl>
				{!isUpdate && (
					<Button
						variant="outlined"
						color="primary"
						onClick={() => onSaveDraft(values)}>
						Save Draft
					</Button>
				)}
				<form.Subscribe
					selector={({ isDirty, canSubmit, isSubmitting }) => ({
						isDirty,
						canSubmit,
						isSubmitting,
					})}>
					{({ isDirty, canSubmit, isSubmitting }) => (
						<Button
							variant="contained"
							color="primary"
							type="submit"
							loading={isSubmitting}
							disabled={!isDirty || !canSubmit}>
							{isUpdate ? 'Update' : 'Next'}
						</Button>
					)}
				</form.Subscribe>
			</StepCardControl>
		</Box>
	);
}
