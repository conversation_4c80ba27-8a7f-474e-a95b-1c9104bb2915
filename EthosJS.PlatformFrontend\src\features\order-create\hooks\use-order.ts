import { PatientCreate, PatientRead } from '@auth/scopes';
import {
	EthosWorkflowsApiCreateOrderDto,
	EthosWorkflowsApiDraftDto,
	SystemTextJsonNodesJsonNode,
} from '@client/workflows';
import {
	getApiOrderDraftByEntityIdOptions,
	postApiOrderDraftByEntityIdCommitMutation,
	putApiOrderDraftByEntityIdMutation,
} from '@client/workflows/@tanstack/react-query.gen';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { OrderState } from '../types';

export default function useOrder({ orderId }: { orderId: string }) {
	const queryClient = useQueryClient();
	const queryKey = getApiOrderDraftByEntityIdOptions({
		path: { entityId: orderId! },
		scopes: [PatientCreate.value, PatientRead.value],
		responseType: 'json',
	});

	const {
		data: orderData,
		isFetching: isFetchingOrderData,
		error: fetchOrderError,
	} = useQuery({
		...queryKey,
		enabled: !!orderId,
	});

	const {
		mutate: updateOrderMutation,
		isPending: isUpdatingOrder,
		error: updateOrderError,
	} = useMutation({
		...putApiOrderDraftByEntityIdMutation({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		}),
		onError(error) {
			console.error(error);
		},
		onSuccess(mutationData) {
			queryClient.setQueryData([queryKey], mutationData);
		},
	});

	const {
		mutate: commitDraftMutation,
		isPending: isCommittingDraft,
		error: commitDraftError,
	} = useMutation({
		...postApiOrderDraftByEntityIdCommitMutation({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		}),
		onError(error) {
			console.error(error);
		},
		onSuccess(mutationData) {
			queryClient.setQueryData([queryKey], mutationData);
		},
	});

	const commitOrderDraft = useCallback(
		(orderId: string, successCallback?: (orderId: string) => void) => {
			commitDraftMutation(
				{
					path: { entityId: orderId },
				},
				{
					onSuccess(mutationData) {
						const { id } = mutationData;
						if (successCallback && id) {
							successCallback(id);
						}
					},
				}
			);
		},
		[commitDraftMutation]
	);

	const updateOrder = useCallback(
		async (
			data: Partial<EthosWorkflowsApiCreateOrderDto>,
			state?: Partial<OrderState>,
			successCallback?: () => void
		) => {
			updateOrderMutation(
				{
					path: { entityId: orderId! },
					body: {
						...data,
						_state: state,
					} as unknown as { [key: string]: SystemTextJsonNodesJsonNode },
				},
				{
					onSuccess() {
						if (successCallback) {
							successCallback();
						}
					},
				}
			);
		},
		[updateOrderMutation, orderId]
	);

	return {
		orderData: orderData as EthosWorkflowsApiDraftDto & {
			data: EthosWorkflowsApiCreateOrderDto & OrderState;
		},
		isFetchingOrderData,
		fetchOrderError,
		updateOrder,
		isUpdatingOrder,
		updateOrderError,
		commitOrderDraft,
		isCommittingDraft,
		commitDraftError,
	};
}
