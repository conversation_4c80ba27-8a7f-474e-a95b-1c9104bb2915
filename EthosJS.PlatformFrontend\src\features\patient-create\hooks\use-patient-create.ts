import { PatientCreate, PatientRead } from '@auth/scopes';
import {
	EthosWorkflowsApiValidatedDraftDto,
	MicrosoftAspNetCoreMvcProblemDetails,
} from '@client/workflows';
import { postApiPatientDraftMutation } from '@client/workflows/@tanstack/react-query.gen';
import { useMutation } from '@tanstack/react-query';
import { extractProblemDetails } from '@utils/errors';
import { useCallback } from 'react';
import { PatientState } from '../types';

export default function usePatientCreate() {
	const {
		mutate: createPatientMutation,
		isPending: isCreatingPatient,
		error: createPatientError,
		reset,
	} = useMutation({
		...postApiPatientDraftMutation({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		}),
		onError(error) {
			console.error(error);
		},
	});

	const createNewPatient = useCallback(
		async (
			state?: Partial<PatientState>,
			successCallback?: (data: EthosWorkflowsApiValidatedDraftDto) => void,
			errorCallback?: (error: MicrosoftAspNetCoreMvcProblemDetails) => void
		) => {
			createPatientMutation(
				{
					body: {
						...(state ? { _state: { ...state } as unknown as PatientState } : {}),
					},
				},
				{
					onSuccess(data) {
						if (successCallback) {
							successCallback(data);
						}
					},
					onError(error) {
						if (errorCallback) {
							const problemDetails = extractProblemDetails(error);
							errorCallback(problemDetails);
						}
					},
				}
			);
		},
		[createPatientMutation]
	);

	const resetCreatePatientMutation = useCallback(() => {
		reset();
	}, [reset]);

	return {
		createNewPatient,
		isCreatingPatient,
		createPatientError,
		resetCreatePatientMutation,
	};
}
