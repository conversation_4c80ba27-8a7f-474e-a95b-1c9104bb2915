import React, { useState } from 'react';
import { Box, Typography, Stack } from '@mui/material';
import MainCardContainer from '@components/main-container/main-card-container';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-form';
import { NoteItemProps } from './types';
import { NotebookPen } from 'lucide-react';

interface EditFormData {
  content: string;
}

export const NoteItem: React.FC<NoteItemProps> = ({
  note,
  allowEdit = true,
  onEdit,
  onDelete,
  size = 'medium',
  footerProps,
  icon,
  onStartEdit,
  onCancelEdit,
  isEditing
}) => {

  const editForm = useAppForm({
    defaultValues: {
      content: note.content
    } as EditFormData
  });

  const { values: editValues } = useStore(editForm.store, ({ values }) => ({ values }));

  const handlePrimaryAction = () => {
    if (allowEdit) {
      onStartEdit?.(note.id);
      editForm.setFieldValue('content', note.content);
    }
  };

  const handleSave = () => {
    if (editValues.content?.trim()) {
      onEdit?.({
        header: note.header,
        subHeader: note.subHeader,
        descriptionSubheader: note.descriptionSubheader,
        descriptionText: note.descriptionText,
        content: editValues.content.trim()
      });
      onCancelEdit?.(); // close edit mode
    }
  };

  const handleCancel = () => {
    onCancelEdit?.();
    editForm.setFieldValue('content', note.content);
  };

  const handleDeleteClick = () => {
    onDelete?.();
    onCancelEdit?.();
  };

  if (isEditing) {
    return (
      <MainCardContainer
        title="Edit Note"
        icon={<NotebookPen />}
        primaryActionType='Delete'
        onPrimaryAction={handleCancel}
        emphasis="high"
        color="primary"
        headerSize={size}
        footerProps={{
          primaryButton1: {
            label: 'Save',
            onClick: handleSave,
            disabled: !editValues.content?.trim()
          },
          primaryButton2: {
            label: 'Cancel',
            onClick: handleCancel
          },
          ...(onDelete && {
            secondaryButton1: {
              label: 'Delete',
              onClick: handleDeleteClick
            }
          })
        }}>
        <Stack sx={{ p: "16px" }}>
          <editForm.AppField
            name="content"
            children={(field) => (
              <field.AppTextField
                label=""
                multiline
                placeholder="Type here..."
                size="medium"
                minRows={4}
                required
              />
            )}
          />
        </Stack>
      </MainCardContainer>
    );
  }

  return (
    <MainCardContainer
      title={note.header}
      icon={icon}
      emphasis="low"
      color="primary"
      headerSize={size}
      primaryActionType={allowEdit ? 'Edit' : 'none'}
      onPrimaryAction={handlePrimaryAction}
      descriptionSubheader={note.subHeader}
      descriptionText={note.descriptionSubheader}
      footerProps={{
        ...footerProps,
      }}>
      <Box sx={{ p: "16px" }}>
        <Typography
          variant="body1"
          sx={{
            mb: 1,
            whiteSpace: 'pre-wrap',
            lineHeight: 1.5
          }}>
          {note.content}
        </Typography>
      </Box>
    </MainCardContainer>
  );
};
