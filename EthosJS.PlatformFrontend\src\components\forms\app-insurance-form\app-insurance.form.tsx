import MainCardContainer from '@components/main-container/main-card-container';
import { AppFormType, useAppForm } from '@hooks/app-form';
import { ReceiptOutlined } from '@mui/icons-material';
import { Grid2 as Grid, Box, InputAdornment } from '@mui/material';
import { useStore } from '@tanstack/react-form';
import { Lock } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { getApiReferenceSetsValuesOptions } from '@client/refdata/@tanstack/react-query.gen';
import { useQuery } from '@tanstack/react-query';
import { find } from 'lodash';
import { ReferenceDataSetKeyValueDto } from '@client/refdata';
import { EthosWorkflowsApiInsuranceDto } from '@client/workflows';
import { defaultValues, insuranceFormOptions } from './utils';
import { ValidationErrors } from '@app-types/validation';
import { formHasErrors } from '@utils/forms';
import { formHasValues } from '../utils';

interface InsuranceFormProps {
	onAdd?: (values: EthosWorkflowsApiInsuranceDto) => void;
	onCancel?: (shouldRemove: boolean) => void;
	onDelete?: () => void;
	onValidate: (data: EthosWorkflowsApiInsuranceDto) => Promise<ValidationErrors | undefined>;
	formValues: EthosWorkflowsApiInsuranceDto;
}

export default function InsuranceForm({
	onAdd,
	onCancel,
	onDelete,
	onValidate,
	formValues,
	patientName,
	dateOfBirth,
}: InsuranceFormProps & { patientName: string; dateOfBirth: string }) {
	const options = useMemo(() => insuranceFormOptions(formValues), [formValues]);
	const hasValues = formHasValues(formValues, defaultValues);

	const form = useAppForm({
		...options,
		validators: {
			onChangeAsyncDebounceMs: 500,
			onChangeAsync: async ({ value }) => {
				const res = await onValidate(value);
				return res;
			},
		},
	});

	const [isSelf, setIsSelf] = useState(false);

	const { values, isDirty, canSubmit } = useStore(form.store, ({ values, isDirty, canSubmit }) => ({
		values,
		isDirty,
		canSubmit,
	}));

	const { data: relationshipOptions } = useQuery({
		...getApiReferenceSetsValuesOptions({
			query: {
				setName: 'relationship',
				filter: 'relationshipType eq Insurance',
			},
			responseType: 'json',
		}),
	});

	useEffect(() => {
		if (relationshipOptions) {
			const reletionshipResponse = relationshipOptions as { items: ReferenceDataSetKeyValueDto[] };
			const selfOption = find(reletionshipResponse.items, (item) => item.values?.name === 'Self');
			if (selfOption) {
				form.setFieldValue('insuranceHolder.name', patientName);
				form.setFieldValue('insuranceHolder.dateOfBirth', dateOfBirth);
				setIsSelf(selfOption.id === values.insuranceHolder?.relationship);
			} else {
				setIsSelf(false);
			}
		}
	}, [dateOfBirth, form, patientName, relationshipOptions, values.insuranceHolder?.relationship]);

	return (
		<MainCardContainer
			emphasis="high"
			color={formHasErrors(form as AppFormType) && isDirty ? 'error' : 'primary'}
			title="Add Insurance"
			icon={<ReceiptOutlined />}
			descriptionSubheader="* Indicates a required field"
			descriptionText="Enter the policy holder information and insurance details."
			footerProps={{
				primaryButton1: {
					label: hasValues ? 'Update' : 'Add',
					onClick: () => onAdd?.(values),
					disabled: !canSubmit || !isDirty,
					'data-testid': 'insurance.submitButton',
				},
				primaryButton2: {
					label: 'Cancel',
					onClick: () => {
						form.reset();
						onCancel?.(hasValues);
					},
					'data-testid': 'insurance.cancelButton',
				},
				secondaryButton1: hasValues
					? {
							label: 'Delete',
							onClick: onDelete,
							color: 'error',
							'data-testid': 'insurance.deleteButton',
						}
					: undefined,
			}}>
			<Grid
				container
				spacing={2}>
				{/* Insurance Holder Section */}
				<Grid size={12}>
					<Box sx={{ typography: 'subtitle1', mb: 1, color: 'primary.dark' }}>
						Insurance Holder Information
					</Box>
				</Grid>
				<Grid size={4}>
					<form.AppField name="insuranceHolder.relationship">
						{(field) => (
							<field.AppSelectField
								label="Relationship"
								required
								referenceDataSetName="relationship"
								referenceDataFilter="relationshipType eq Insurance"
								defaultReferenceValue="Self"
								dataTestId="insurances.relationship"
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={4}>
					<form.AppField
						name="insuranceHolder.name"
						children={(field) => (
							<field.AppTextField
								label="Full Name"
								required
								disabled={isSelf}
								dataTestId="insurances.holderName"
							/>
						)}
					/>
				</Grid>
				<Grid size={4}>
					<form.AppField
						name="insuranceHolder.dateOfBirth"
						children={(field) => (
							<field.AppDateField
								label="Date of Birth"
								required
								disabled={isSelf}
							/>
						)}
					/>
				</Grid>

				{/* Insurance Information Section */}
				<Grid size={12}>
					<Box sx={{ typography: 'subtitle1', mb: 1, mt: 2, color: 'primary.dark' }}>
						Insurance Information
					</Box>
				</Grid>
				<Grid size={6}>
					<form.AppField name="insuranceCarrier">
						{(field) => (
							<field.AppAutocompleteField
								label="Insurance Carrier"
								referenceDataSetName="insuranceCarrier"
								required
								dataTestId="insurances.carrier"
							/>
						)}
					</form.AppField>
				</Grid>
				{/* <Grid size={6}>
					<form.AppField name="planType">
						{(field) => (
							<field.AppAutocompleteField
								label="Plan Type"
								referenceDataSetName="insurancePlanType"
								required
								dataTestId="insurances.planType"
							/>
						)}
					</form.AppField>
				</Grid> */}
				<Grid size={6}>
					<form.AppField
						name="insuranceId"
						children={(field) => (
							<field.AppTextField
								label="Insurance ID"
								required
								dataTestId="insurances.insuranceId"
							/>
						)}
					/>
				</Grid>
				<Grid size={6}>
					<form.AppField
						name="policyId"
						children={(field) => (
							<field.AppTextField
								label="Policy ID"
								required
								dataTestId="insurances.policyId"
							/>
						)}
					/>
				</Grid>
				<Grid size={6}>
					<form.AppField
						name="memberId"
						children={(field) => (
							<field.AppTextField
								label="Member ID"
								required
								dataTestId="insurances.memberId"
							/>
						)}
					/>
				</Grid>
				<Grid size={6}>
					<form.AppField
						name="groupNumber"
						children={(field) => (
							<field.AppTextField
								label="Group Number"
								dataTestId="insurances.groupNumber"
							/>
						)}
					/>
				</Grid>
				{/* Contact Information Section */}
				<Grid size={12}>
					<Box sx={{ typography: 'subtitle1', mb: 1, mt: 2, color: 'primary.dark' }}>
						Contact Information
					</Box>
				</Grid>
				<Grid size={3}>
					<form.AppField name="phoneNumber.use">
						{(field) => (
							<field.AppSelectField
								label="Phone Type"
								referenceDataSetName="phoneUse"
								defaultReferenceValue="Work"
								disabled
								required
								dataTestId="insurances.phoneType"
								slotProps={{
									input: {
										endAdornment: (
											<InputAdornment position="end">
												<Lock />
											</InputAdornment>
										),
									},
								}}
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={9}>
					<form.AppField
						name="phoneNumber.phoneNumber"
						children={(field) => (
							<field.AppPhoneNumberField
								label="Phone Number"
								required
								dataTestId="insurances.phoneNumber"
							/>
						)}
					/>
				</Grid>
				<Grid size={3}>
					<form.AppField name="email.use">
						{(field) => (
							<field.AppSelectField
								label="Email Type"
								referenceDataSetName="emailUse"
								defaultReferenceValue="Work"
								dataTestId="insurances.emailType"
								slotProps={{
									input: {
										endAdornment: (
											<InputAdornment position="end">
												<Lock />
											</InputAdornment>
										),
									},
								}}
								disabled
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={9}>
					<form.AppField
						name="email.email"
						children={(field) => (
							<field.AppEmailField
								label="Email"
								required
								dataTestId="insurances.email"
							/>
						)}
					/>
				</Grid>

				{/* Address Section - Moved to bottom */}
				<Grid size={12}>
					<Box sx={{ typography: 'subtitle1', mb: 1, mt: 2, color: 'primary.dark' }}>
						Address Information
					</Box>
				</Grid>

				<Grid size={4}>
					<form.AppField name="address.use">
						{(field) => (
							<field.AppSelectField
								label="Address Type"
								referenceDataSetName="addressType"
								dataTestId="insurances.addressType"
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={12}>
					<form.AppField
						name="address.address.line1"
						children={(field) => (
							<field.AppTextField
								label="Address Line 1"
								required
								fullWidth
								dataTestId="insurances.addressLine1"
							/>
						)}
					/>
				</Grid>
				<Grid size={12}>
					<form.AppField
						name="address.address.line2"
						children={(field) => (
							<field.AppTextField
								label="Address Line 2"
								fullWidth
								dataTestId="insurances.addressLine2"
							/>
						)}
					/>
				</Grid>
				<Grid size={4}>
					<form.AppField
						name="address.address.city"
						children={(field) => (
							<field.AppTextField
								label="City"
								required
								dataTestId="insurances.city"
							/>
						)}
					/>
				</Grid>
				<Grid size={4}>
					<form.AppField name="address.address.state">
						{(field) => (
							<field.AppAutocompleteField
								label="State"
								referenceDataSetName="state"
								required
								dataTestId="insurances.state"
							/>
						)}
					</form.AppField>
				</Grid>
				<Grid size={4}>
					<form.AppField
						name="address.address.postalCode"
						children={(field) => (
							<field.AppTextField
								label="Postal Code"
								required
								dataTestId="insurances.postalCode"
							/>
						)}
					/>
				</Grid>
			</Grid>
		</MainCardContainer>
	);
}
