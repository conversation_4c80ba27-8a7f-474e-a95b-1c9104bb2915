import { Store } from '@tanstack/react-store';
import { useEffect } from 'react';
import { useStore } from '@tanstack/react-store';

type PreStudyState = {
  totalStep: number;
  activeStep: number;
  isLastStep: boolean;
  values: {
    // Step 1: Patient Chart & Study Details (already implemented)
    step1Completed: boolean;

    // Step 2: Room Setup & Assignment
    roomSetup: {
      assignedRoom: string;
      technicianAssignment: string;
    };

    // Step 3: Patient Check-in
    patientCheckin: {
      actualArrivalTime: string;
      patientIdentityVerified: boolean;
      insuranceCardVerified: boolean;
      requiredFormsUploaded: {
        preStudyQuestionnaire: boolean;
        sleepStudyConsent: boolean;
        hipaaAcknowledgment: boolean;
        videoRecordingConsent: boolean;
      };
    };

    // Step 4: Manual Vital Signs Entry
    vitalSigns: {
      unit: string;
      bodyTemperature: string;
      systolicPressure: string;
      diastolicPressure: string;
      pulseRate: string;
      respiratoryRate: string;
      oxygenSaturation: string;
    };

    // Step 5: Pre-Study Checklist
    preStudyChecklist: {
      insuranceVerified: boolean;
      documentsReceived: boolean;
      patientContactConfirmed: boolean;
      equipmentPrepared: boolean;
    };
    
    // Step 3: Patient Instructions
    patientInstructions: {
      instructionsSent: boolean;
      deliveryMethod: string;
      confirmationReceived: boolean;
    };
    
    // Step 4: Equipment & Room Setup
    equipmentSetup: {
      roomAssigned: string;
      equipmentChecked: boolean;
      calibrationCompleted: boolean;
      backupEquipmentReady: boolean;
    };
    
    // Step 5: Final Review & Confirmation
    finalReview: {
      allStepsCompleted: boolean;
      technicianAssigned: string;
      studyReadyToStart: boolean;
    };
  };
};

const defaultState: PreStudyState = {
  totalStep: 4,
  activeStep: 0,
  isLastStep: false,
  values: {
    step1Completed: false,
    roomSetup: {
      assignedRoom: '',
      technicianAssignment: ''
    },
    patientCheckin: {
      actualArrivalTime: '',
      patientIdentityVerified: false,
      insuranceCardVerified: false,
      requiredFormsUploaded: {
        preStudyQuestionnaire: false,
        sleepStudyConsent: false,
        hipaaAcknowledgment: false,
        videoRecordingConsent: false
      }
    },
    vitalSigns: {
      unit: '',
      bodyTemperature: '',
      systolicPressure: '',
      diastolicPressure: '',
      pulseRate: '',
      respiratoryRate: '',
      oxygenSaturation: ''
    },
    preStudyChecklist: {
      insuranceVerified: false,
      documentsReceived: false,
      patientContactConfirmed: false,
      equipmentPrepared: false,
    },
    patientInstructions: {
      instructionsSent: false,
      deliveryMethod: '',
      confirmationReceived: false,
    },
    equipmentSetup: {
      roomAssigned: '',
      equipmentChecked: false,
      calibrationCompleted: false,
      backupEquipmentReady: false,
    },
    finalReview: {
      allStepsCompleted: false,
      technicianAssigned: '',
      studyReadyToStart: false,
    },
  },
};

const store = new Store<PreStudyState>(defaultState);

const actions = {
  setValues: (values: Partial<PreStudyState['values']>) => {
    store.setState((prevState) => ({ 
      ...prevState, 
      values: { ...prevState.values, ...values } 
    }));
  },
  moveNext: () => {
    const totalStep = store.state.totalStep;
    if (store.state.activeStep < totalStep - 1) {
      store.setState((prevState) => ({ 
        ...prevState, 
        activeStep: prevState.activeStep + 1 
      }));
    }
  },
  moveBack: () => {
    store.setState((prevState) => ({ 
      ...prevState, 
      activeStep: prevState.activeStep - 1 
    }));
  },
  resetState: () => {
    store.setState(() => defaultState);
  },
};

const usePreStudyStore = () => {
  const { activeStep, totalStep, isLastStep } = useStore(store, ({ activeStep, totalStep, isLastStep }) => ({ 
    totalStep, 
    activeStep, 
    isLastStep 
  }));

  useEffect(() => {
    if (activeStep >= totalStep - 1) {
      store.setState((prev) => ({ ...prev, isLastStep: true }));
    } else {
      if (isLastStep) {
        store.setState((prev) => ({ ...prev, isLastStep: false }));
      }
    }
  }, [activeStep, totalStep, isLastStep]);

  return { store, actions };
};

export default usePreStudyStore;
