import dayjs from 'dayjs';
import { PatientState, StepNames } from '../types';
import { ChipData } from '@components/chip-summary';

function progressCalculation(currentProgress: number, totalSteps: number) {
	return currentProgress + (1 / totalSteps) * 100;
}

function getNewPatientState(
	patientState: PatientState,
	currentStep: StepNames,
	nextStep: StepNames | undefined
): PatientState {
	const { stepState } = patientState;

	const currentStepState = stepState[currentStep];
	const nextStepState = nextStep ? stepState[nextStep] : undefined;
	if (currentStepState === 'Complete') {
		return {
			...patientState,
			flowState: {
				...patientState.flowState,
				status:
					nextStepState === undefined
						? 'Complete'
						: nextStepState !== 'Complete'
							? 'InProgress'
							: patientState.flowState.status,
				lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
			},
			stepState: {
				...patientState.stepState,
				...(nextStep
					? { [nextStep]: nextStepState === 'NotStarted' ? 'InProgress' : nextStepState }
					: {}),
			},
		};
	}

	return {
		flowState: {
			...patientState.flowState,
			status:
				nextStepState === 'Complete' || nextStepState === undefined ? 'Complete' : 'InProgress',
			progress: progressCalculation(patientState.flowState.progress, 7),
			lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
		},
		stepState: {
			...patientState.stepState,
			[currentStep]: 'Complete',
			...(nextStep ? { [nextStep]: 'InProgress' } : {}),
		},
	};
}

export const filterChipData = (items: ChipData[]) => items?.filter((i) => !!i?.value);

export { getNewPatientState, progressCalculation };
