import {
	EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
	EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
	EthosWorkflowsApiPhysicianDto,
} from '@client/workflows';
import {
	getApiPhysicianByIdOptions,
	postApiOrderSearchOptions,
	postApiPhysicianSearchOptions,
} from '@client/workflows/@tanstack/react-query.gen';
import { useQueries, useQuery } from '@tanstack/react-query';
import { OrderQuery, PhysicianQuery, Query } from '@utils/query-dsl';
import { find, map, some } from 'lodash';

export default function useSelectionControlOrders({ patientId }: { patientId: string }) {
	// const { data: refData, isPending: isRefDataPending } = useQueries({
	// 	queries: [
	// 		getApiReferenceSetsValuesOptions({
	// 			responseType: 'json',
	// 			query: {
	// 				setName: 'prefix',
	// 			},
	// 		}),
	//         getApiReferenceSetsValuesOptions({
	// 			responseType: 'json',
	// 			query: {
	// 				setName: 'suffix',
	// 			},
	// 		}),
	// 	],
	// 	combine: (results) => {
	// 		return {
	// 			data: results.map((result) => result.data),
	// 			isPending: some(results, (result) => result.isPending),
	// 		};
	// 	},
	// });

	const { data: orders } = useQuery({
		...postApiOrderSearchOptions({
			responseType: 'json',
			body: Query.literal(
				OrderQuery.withPatientId(patientId)
			) as unknown as EthosModelQueryDto1EthosModelOrderQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!patientId,
		select: (data) => data?.items ?? [],
		initialData: {
			items: [],
		},
	});

	// const { data: physicianData, isPending: isPhysiciansPending } = useQueries({
	// 	queries: orders?.length
	// 		? orders.map((order) => {
	// 				const { interpretingPhysicianId } = order;
	// 				return getApiPhysicianByIdOptions({
	// 					path: { id: interpretingPhysicianId! },
	// 					responseType: 'json',
	// 				});
	// 			})
	// 		: [],
	// 	combine: (results) => {
	// 		return {
	// 			data: results.map((result) => result.data),
	// 			isPending: some(results, (result) => result.isPending),
	// 		};
	// 	},
	// });

	const { data: physicianData, isPending: isPhysiciansPending } = useQuery({
		...postApiPhysicianSearchOptions({
			responseType: 'json',
			body: Query.or(
				orders?.map((order) => {
					return Query.literal(PhysicianQuery.withId(order.interpretingPhysicianId!));
				})
			) as unknown as EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!orders?.[0].interpretingPhysicianId,
		select: (data) => data?.items ?? [],
	});

	const combinedOrders =
		orders?.length && physicianData?.length
			? map(orders, (order) => {
					const physician = find(physicianData, {
						id: order.interpretingPhysicianId,
					}) as EthosWorkflowsApiPhysicianDto;
					return {
						...order,
						interpretingPhysician: physician
							? `${physician.names?.[0].firstName} ${physician.names?.[0].lastName}`.trim()
							: '',
					};
				})
			: [];

	return {
		orders: combinedOrders,
		isPhysiciansPending,
	};
}
