import { GetApiInsuranceVerificationStatusByJobIdResponse } from '@client/workflows';
import ProcessingStatus from './processing-status';
import VerifiedWithReferralStatus from './verified-with-referral-status';

export default function PendingState({
	verificationStatus,
}: {
	verificationStatus: GetApiInsuranceVerificationStatusByJobIdResponse;
}) {
	const { currentFineGrainedStateName } = verificationStatus;

	if (currentFineGrainedStateName === 'VerificationSuccessfulAuthRequired') {
		return (
			<VerifiedWithReferralStatus
				verificationStatus={verificationStatus}
				onSubmitReferral={() => {}}
			/>
		);
	}

	if (currentFineGrainedStateName === 'AuthorizationStart') {
		return <ProcessingStatus />;
	}

	if (currentFineGrainedStateName === 'AuthorizationWebhookReceivedNeedsProcessing') {
		return <ProcessingStatus />;
	}

	return <ProcessingStatus />;
}
