import { Card, CardContent, Skeleton, Box, Stack } from "@mui/material"

export default function WorkflowProgressCardSkeleton() {
    return (
        <Card sx={{ maxWidth: 400, width: "100%" }}>
            <CardContent>
                <Stack spacing={2}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Skeleton variant="text" width="70%" height={32} />
                        <Skeleton variant="circular" width={20} height={20} />
                    </Box>

                    <Box>
                        <Box display="flex" justifyContent="space-between" mb={0.5}>
                            <Skeleton variant="text" width="30%" height={20} />
                            <Skeleton variant="text" width="15%" height={20} />
                        </Box>
                        <Skeleton variant="rectangular" height={8} width="100%" sx={{ borderRadius: 4 }} />
                    </Box>

                    <Skeleton variant="text" width="50%" height={20} />
                </Stack>
            </CardContent>
        </Card>
    )
}