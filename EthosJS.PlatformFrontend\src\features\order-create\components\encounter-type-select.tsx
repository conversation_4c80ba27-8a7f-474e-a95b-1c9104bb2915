import { EthosWorkflowsApiCreateStudyDto } from '@client/workflows';
import LoadingComponent from '@components/loading-component';
import { useAppForm } from '@hooks/app-form';
import { useMemo } from 'react';
import { formOptions } from '@tanstack/react-form';
import { useRefDataOptions } from '@hooks/use-ref-data-options';

const studyFormOptions = (savedData?: EthosWorkflowsApiCreateStudyDto) => {
	return formOptions({
		defaultValues: {
			encounterType: savedData?.encounterType ?? null,
		},
	});
};

export default function EncounterTypeSelect({
	onSubmit,
	savedData,
}: {
	onSubmit: (data: Partial<EthosWorkflowsApiCreateStudyDto>) => void;
	savedData?: EthosWorkflowsApiCreateStudyDto;
}) {
	const options = useMemo(() => studyFormOptions(savedData), [savedData]);

	const form = useAppForm({
		...options,
		onSubmit: async ({ value }) => {
			onSubmit(value);
		},
	});

	const { options: encounterTypeOptions, isFetching: isFetchingEncounterTypeOptions } =
		useRefDataOptions({
			setName: 'encounterType',
		});

	return (
		<>
			{isFetchingEncounterTypeOptions ? (
				<LoadingComponent />
			) : (
				<form.AppField
					name="encounterType"
					children={(field) => (
						<field.AppRadioField
							label="1. Encounter Type"
							options={encounterTypeOptions ?? []}
						/>
					)}
				/>
			)}
		</>
	);
}
