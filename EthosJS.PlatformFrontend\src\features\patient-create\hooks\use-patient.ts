import { PatientCreate, PatientRead } from '@auth/scopes';
import {
	EthosWorkflowsApiCreatePatientInputDto,
	EthosWorkflowsApiDraftDto,
	EthosWorkflowsApiPatientDto,
	SystemTextJsonNodesJsonNode,
} from '@client/workflows';
import {
	getApiPatientDraftByEntityIdOptions,
	postApiPatientDraftByEntityIdCommitMutation,
	putApiPatientDraftByEntityIdMutation,
} from '@client/workflows/@tanstack/react-query.gen';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import { PatientState } from '../types/state-types';

export default function usePatient({ patientId }: { patientId: string }) {
	const queryClient = useQueryClient();

	const queryKey = getApiPatientDraftByEntityIdOptions({
		scopes: [PatientCreate.value, PatientRead.value],
		responseType: 'json',
		path: { entityId: patientId! },
	});

	const {
		data: patientData,
		isFetching: isFetchingPatientData,
		error: fetchPatientError,
	} = useQuery({
		...queryKey,
		enabled: !!patientId,
	});

	const {
		mutate,
		isPending: isUpdatingPatient,
		error: updatePatientError,
		data: updatePatientResponseData,
		reset: resetUpdatePatientMutation,
	} = useMutation({
		...putApiPatientDraftByEntityIdMutation({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		}),
		onError(error) {
			console.error(error);
		},
		onSuccess(mutationData) {
			// queryClient.invalidateQueries({ queryKey: queryKey.queryKey })
			queryClient.setQueryData(queryKey.queryKey, (prevState) => {
				if (mutationData?.data && prevState?.data) {
					prevState.data = mutationData.data;
				}
				return prevState;
			});
		},
	});

	const updatePatient = useCallback(
		async (
			data: Partial<EthosWorkflowsApiCreatePatientInputDto>,
			state?: Partial<PatientState>,
			successCallback?: () => void
		) => {
			mutate(
				{
					path: { entityId: patientId! },
					body: { ...data, _state: state } as unknown as {
						[key: string]: SystemTextJsonNodesJsonNode;
					},
				},
				{
					onSuccess(mutationData) {
						const { errors } = mutationData;
						if (successCallback && !errors) {
							successCallback();
						}
					},
				}
			);
		},
		[mutate, patientId]
	);

	const saveDraft = useCallback(
		async (
			data: Partial<EthosWorkflowsApiCreatePatientInputDto>,
			state?: Partial<PatientState>,
			successCallback?: () => void
		) => {
			mutate(
				{
					path: { entityId: patientId! },
					body: { ...data, _state: state } as unknown as {
						[key: string]: SystemTextJsonNodesJsonNode;
					},
				},
				{
					onSuccess() {
						if (successCallback) {
							successCallback();
						}
					},
				}
			);
		},
		[mutate, patientId]
	);

	const {
		mutate: commitDraftMutation,
		isPending: isCommittingDraft,
		error: commitDraftError,
	} = useMutation({
		...postApiPatientDraftByEntityIdCommitMutation({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		}),
		onError(error) {
			console.error(error);
		},
		onSuccess(mutationData) {
			queryClient.setQueryData([queryKey], mutationData);
		},
	});

	const commitDraft = useCallback(
		(patientId: string, successCallback?: (id: string) => void) => {
			commitDraftMutation(
				{
					path: { entityId: patientId },
				},
				{
					onSuccess(mutationData) {
						const { id } = mutationData;
						if (successCallback && id) {
							successCallback(id);
						}
					},
				}
			);
		},
		[commitDraftMutation]
	);

	return {
		patientData: patientData as unknown as EthosWorkflowsApiDraftDto & {
			data: EthosWorkflowsApiPatientDto & PatientState;
		},
		isFetchingPatientData,
		fetchPatientError,
		updatePatient,
		updatePatientResponseData,
		isUpdatingPatient,
		updatePatientError,
		resetUpdatePatientMutation,
		saveDraft,
		commitDraft,
		isCommittingDraft,
		commitDraftError,
	};
}
