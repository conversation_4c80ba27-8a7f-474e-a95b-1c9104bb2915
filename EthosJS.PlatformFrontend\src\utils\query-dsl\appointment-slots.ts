import { IPrimitiveQuery } from './core';

// CareLocation primitive query types
export interface AppointmentQ extends IPrimitiveQuery {
  $type: 'WithStudyId' | 'WithCareLocationId' | 'WithStartDate' | 'WithEndDate';
}

// Helper functions for CareLocationQ
export const appointmentQuery = {
  withStudyId: (studyId: string): AppointmentQ => ({
    $type: 'WithStudyId',
    StudyId: studyId
  }),

  withCareLocationId: (careLocationId: string): AppointmentQ => ({
    $type: 'WithCareLocationId',
    CareLocationId: careLocationId
  }),

  withStartDate: (startDate: string): AppointmentQ => ({
    $type: 'WithStartDate',
    StartDate: startDate
  }),

  withEndDate: (endDate: string): AppointmentQ => ({
    $type: 'WithEndDate',
    EndDate: endDate
  })
};
