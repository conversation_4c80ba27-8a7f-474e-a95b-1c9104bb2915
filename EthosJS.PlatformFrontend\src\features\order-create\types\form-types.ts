import {
	EthosWorkflowsApiCreateOrderDto,
	EthosWorkflowsApiInsuranceOutputDto,
	EthosWorkflowsApiCreateStudyDto,
} from '@client/workflows';
import { FormProps } from '@components/forms/predefined-form-props';

type StudyFormData = Omit<EthosWorkflowsApiCreateStudyDto, 'orderId' | 'studyAttributes'> & {
	studyAttributes: Array<{ key: string; value: number }>;
};

type StudyFormProps = Omit<FormProps<EthosWorkflowsApiCreateStudyDto>, 'onValidate'> & {
	orderId: string;
	insurances: Array<EthosWorkflowsApiInsuranceOutputDto>;
	isInsuranceLoading: boolean;
	insuranceError: boolean;
};

type AddPhysiciansFormValues = Omit<
	EthosWorkflowsApiCreateOrderDto,
	'careLocationId' | 'patientId'
>;
export type { StudyFormProps, StudyFormData, AddPhysiciansFormValues };
