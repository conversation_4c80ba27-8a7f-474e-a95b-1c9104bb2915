import { useState, ReactNode } from "react";
import ChipSummary from "./chip-summary";
import { CardHeader, IconButton } from "@mui/material";
import { Edit } from "@mui/icons-material";
import DetailsReviewContainer, { type IDetailsReviewContainer } from "./details-review-container";
import { type MainCardContainerProps } from "./main-container/main-card-container";

export type ChipData = { label: string; value: string }

export interface ArrayFieldContainerProps {
    initialEditState: boolean;
    children: ({ setEdit, edit }: { edit: boolean, setEdit: (edit: boolean) => void }) => ReactNode;
    title: string;
    showHeader?: boolean;
    items: ChipData[]
    displayMode?: 'tab' | 'default'
    mainCardContainerProps?: MainCardContainerProps
    tabItems?: IDetailsReviewContainer['tabItems']
    defaultTabValue?: string | null
}
export default function ArrayFieldContainer({
    items,
    children,
    title,
    showHeader = true,
    initialEditState,
    displayMode = 'default',
    mainCardContainerProps,
    tabItems = [],
    defaultTabValue = null
}: ArrayFieldContainerProps) {
	const [edit, setEdit] = useState(initialEditState);

    if (!edit) {

        if (displayMode === 'tab') {
            return (
                <DetailsReviewContainer
                    mode='tab'
                    mainCardContainerProps={{
                        onPrimaryAction: () => setEdit(true),
                        ...mainCardContainerProps
                    }}
                    tabItems={tabItems}
                    defaultValue={defaultTabValue as string}
                />
            )
        }

        if (items.length === 0) {
            if (showHeader) {
                return (
                    <CardHeader
                        title={title}
                        sx={{ borderRadius: 1 }}
                        action={
                            <IconButton onClick={() => setEdit(true)}>
                                <Edit />
                            </IconButton>
                        }
                    />
                );
            }
            return null;
        }
        return (
            <ChipSummary
                items={items}
                onEdit={() => setEdit(true)}
            />
        );
    }

    return children({ setEdit, edit })
}
