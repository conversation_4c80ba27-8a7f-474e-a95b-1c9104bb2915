import Card from '@components/card';
import CardHeader, { CardHeaderProps } from '../../components/card-header';

import { Status } from '../../routes/_dashboard/patients/$patientId/order/insurance/constant';
import { Chip, styled, lighten, CardContent } from '@mui/material';

interface IStatusCard {
	status: Status;
	headerProps: CardHeaderProps & {
		titleColor?: string;
		subheaderColor?: string;
	};
	borderColor?: string;
	children?: React.ReactNode;
}

const StyledCardHeader = styled(CardHeader, {
	shouldForwardProp: (prop) => !['status', 'titleColor', 'subheaderColor'].includes(prop as string),
})<{ status: Status; titleColor?: string; subheaderColor?: string }>(
	({ theme, status, titleColor, subheaderColor }) => ({
		backgroundColor:
			status === Status.Completed
				? theme.palette.success.main
				: status === Status.Failed
					? theme.palette.error.main
					: status === Status.Warning
						? theme.palette.warning.main
						: status === Status.InProgress
							? lighten(theme.palette.primary.main, 0.9)
							: lighten(theme.palette.warning.main, 0.9),
		color:
			status === Status.Failed || status === Status.Completed || status === Status.Warning
				? theme.palette.common.white
				: 'inherit',
		'& .MuiCardHeader-title': {
			color: titleColor || 'inherit',
		},
		'& .MuiCardHeader-subheader': {
			color: subheaderColor || 'inherit',
		},
	})
);

const StyledCard = styled(Card, {
	shouldForwardProp: (prop) => !['status', 'borderColor'].includes(prop as string),
})<{ status: Status; borderColor?: string }>(({ status, theme }) => ({
	//  border: borderColor ? `1px solid ${borderColor}` : 'none',
	border: status === Status.InProgress ? 'none' : 1,
	borderStyle: 'solid',
	borderColor:
		status === Status.Completed
			? theme.palette.success.main
			: status === Status.Failed
				? theme.palette.error.main
				: status === Status.Warning
					? theme.palette.warning.main
					: status === Status.InProgress
						? lighten(theme.palette.primary.main, 0.9)
						: lighten(theme.palette.warning.main, 0.9),
}));

const StatusChip = styled(Chip, {
	shouldForwardProp: (prop) => prop !== 'status',
})<{ status: Status }>(({ theme, status }) => ({
	backgroundColor:
		status === Status.Completed || status === Status.Failed || status === Status.Warning
			? theme.palette.common.white
			: status === Status.InProgress
				? theme.palette.primary.main
				: theme.palette.warning.light,
	color:
		status === Status.Completed
			? theme.palette.success.main
			: status === Status.Failed
				? theme.palette.error.main
				: status === Status.Warning
					? theme.palette.warning.main
					: status === Status.InProgress
						? theme.palette.common.white
						: theme.palette.warning.main,
	borderRadius: '16px',
	'& .MudChip-label': {
		fontWeight: 500,
	},
}));

const getStatusLabel = (status: Status) => {
	switch (status) {
		case Status.Completed:
			return 'Verified';
		case Status.Failed:
			return 'Failed';
		case Status.Warning:
			return 'Denied';
		case Status.InProgress:
			return 'Processing';
		default:
			return 'Declined';
	}
};

const StatusCard: React.FC<IStatusCard> = ({
	status = Status.InProgress,
	headerProps,
	borderColor,
	children,
}) => {
	const { titleColor, subheaderColor, ...restHeaderProps } = headerProps ?? {};

	return (
		<StyledCard
			status={status}
			borderColor={borderColor}>
			<StyledCardHeader
				status={status}
				titleColor={titleColor}
				subheaderColor={subheaderColor}
				{...restHeaderProps}
				action={
					<StatusChip
						status={status}
						label={getStatusLabel(status)}
						size="small"
						sx={{}}
					/>
				}
			/>
			<CardContent>{children}</CardContent>
		</StyledCard>
	);
};

export default StatusCard;
