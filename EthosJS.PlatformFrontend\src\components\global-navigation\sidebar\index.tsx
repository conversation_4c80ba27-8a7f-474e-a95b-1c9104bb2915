import { Box, CSSObject, darken, IconButton, lighten, List, Stack, styled, Theme, Typography, useTheme } from '@mui/material';
import { ReactNode } from '@tanstack/react-router';
import { ChevronRight, Menu } from 'lucide-react';
import { CSSProperties, useState } from 'react';
import { MUILinkProps, NavLink } from '@components/nav-link';

import MuiDrawer from '@mui/material/Drawer'

interface ISidebar {
   header?: ReactNode
   onToggleCollapse? : (collapsed: boolean) => void
   collapsed?: boolean;
   footer?: {
      collapsed: ReactNode;
      expanded: ReactNode;
   };
   menuProps: {
      items: Partial<MUILinkProps & { path: string }>[];
   };
   brandingLabelProps: IBrandingLabelProps;
}

const getRootStyles = (collapsed: boolean) => {
   let defaultStyles = {
      height: '100%',
      maxWidth: '17.8125rem',
      border: '1px solid',
      borderColor: 'divider',
      gap: 2,
   } as CSSProperties;

   if (collapsed) {
      return {
         ...defaultStyles,
         width: '5.2rem'
      };
   }

   return {
      ...defaultStyles
   };
};

const openedMixin = (theme: Theme): CSSObject => ({
   width: drawerWidth,
   transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
   }),
   overflowX: 'hidden',
})

const drawerWidth = 253
const collapsedDrawerWidth = 82

const closedMixin = (theme: Theme): CSSObject => ({
   width: collapsedDrawerWidth,
   transition: theme.transitions.create('width', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
   }),
   overflowX: 'hidden',
})

const Drawer = styled(MuiDrawer, {
   shouldForwardProp: (prop) => prop !== 'open',
})(({ theme, open }) => ({
   width: drawerWidth,
   flexShrink: 0,
   whiteSpace: 'nowrap',
   boxSizing: 'border-box',
   ...(open && {
      ...openedMixin(theme),
      '& .MuiDrawer-paper': openedMixin(theme),
   }),
   ...(!open && {
      ...closedMixin(theme),
      '& .MuiDrawer-paper': closedMixin(theme),
   }),
}))

const SideBar = ({
   footer,
   header,
   menuProps,
   collapsed: collapsedProp,
   onToggleCollapse: onToggleCollapseProp,
   brandingLabelProps
}: ISidebar) => {
   const menuItems = menuProps?.items ?? [];

   const [collapsedLocal, setCollapsedLocal] = useState<boolean>(false);

   const collapased = collapsedProp ?? collapsedLocal;

   const onToggleCollapse = () => {
      setCollapsedLocal(!collapased);
      onToggleCollapseProp?.(!collapased);
   };

   return (
      <Drawer
         sx={getRootStyles(collapased)}
         component={Stack}
         variant={'permanent'}
         open={!collapased}
         elevation={2}
      >
         <Stack
            sx={{
               p: 2,
               flex: 1,
               gap: 2
            }}
         >
            <Stack
               flexDirection="row"
               justifyContent="space-between"
               alignItems="center"
               paddingBottom={0}
            >
               {header && header}
               {!collapased && (
                  <IconButton onClick={onToggleCollapse}>
                     <Menu />
                  </IconButton>
               )}
            </Stack>
            <Stack
               gap={1}
               flex={1}
               sx={{
                  overflow: 'auto',
                  overflowX: 'hidden',
                  overflowY: 'auto',
                  '&::-webkit-scrollbar': {
                     display: 'none'
                  }
               }}>
               <List sx={{ flexGrow: 1, }} component="nav">
                  {menuItems.map((item) => {
                     return (
                        <NavLink
                           key={item.text}
                           text={item.text as string}
                           to={item.path}
                           icon={item.icon}
                           open={collapased}
                        />
                     )
                  })}
               </List>
            </Stack>
         </Stack>

         <Stack
            gap={1}
         >
            <Box p={1}>
               {footer && (collapased ? footer.collapsed : footer.expanded)}
            </Box>
            {collapased && (
               <Box
                  sx={{
                     display: 'flex',
                     flexDirection: 'column',
                     justifyContent: 'center',
                     alignItems: 'center',
                     gap: 2,
                     pb: 1
                  }}>

                  <IconButton onClick={onToggleCollapse}>
                     <ChevronRight />
                  </IconButton>
               </Box>
            )}

            {!collapased && (
               <Box sx={{ width: '100%' }}>
                  <BrandingLabel {...brandingLabelProps} />
               </Box>
            )}
         </Stack>
      </Drawer>
   );
};

interface IBrandingLabelProps {
   label: string;
}

const BrandingLabel = ({ label }: IBrandingLabelProps) => {
   const { palette } = useTheme();
   return (
      <Stack
         flexDirection="row"
         gap={1}
         alignItems="center"
         justifyContent="center"
         sx={{
            backgroundColor: lighten(palette.primary.light, 0.85)
         }}
         padding={1.5}
         width="100%">
         <img src={'/images/branding-label.png'} />
         <Typography color={darken(palette.primary.dark, 0.5)} variant="body2">
            {label}
         </Typography>
      </Stack>
   );
};

export default SideBar;