import { EthosWorkflowsApiPersonalEmergencyContactDto } from '@client/workflows';
import { z } from 'zod';

const defaultValues: EthosWorkflowsApiPersonalEmergencyContactDto = {
	relationship: null!,
	prefix: null,
	firstName: '',
	lastName: '',
	middleName: '',
	suffix: null,
	contactInformation: '',
};

const emergencyContactSchema = z.any().transform((raw) => {
	return {
		...raw,
		middleName: raw.middleName === '' ? null : raw.middleName,
	};
});

function emergencyContactFormOptions(savedData?: EthosWorkflowsApiPersonalEmergencyContactDto) {
	return {
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	};
}

function emergencyContactTransformer(
	data: EthosWorkflowsApiPersonalEmergencyContactDto
): EthosWorkflowsApiPersonalEmergencyContactDto {
	const { data: parsed } = emergencyContactSchema.safeParse(data);
	return parsed as EthosWorkflowsApiPersonalEmergencyContactDto;
}

export { defaultValues, emergencyContactFormOptions, emergencyContactTransformer };
