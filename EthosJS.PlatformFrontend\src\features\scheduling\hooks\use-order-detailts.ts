import { EthosWorkflowsApiCareLocationDto, EthosWorkflowsApiOrderDto } from '@client/workflows';
import {
	getApiCareLocationByIdOptions,
	getApiOrderByIdOptions,
} from '@client/workflows/@tanstack/react-query.gen';
import { useQuery } from '@tanstack/react-query';

export default function useOrderDetails({ orderId }: { orderId: string }) {
	const { data: orderData, isFetching: isFetchingOrderData } = useQuery({
		...getApiOrderByIdOptions({
			path: { id: orderId! },
			responseType: 'json',
		}),
		enabled: !!orderId,
	});

	const { careLocationId } = (orderData as unknown as EthosWorkflowsApiOrderDto) ?? {};

	const { data: careLocationData, isFetching: isFetchingCareLocationData } = useQuery({
		...getApiCareLocationByIdOptions({
			path: { id: careLocationId! },
			responseType: 'json',
		}),
		enabled: !!careLocationId,
	});

	return {
		orderData: orderData as unknown as EthosWorkflowsApiOrderDto,
		careLocationData: careLocationData as unknown as EthosWorkflowsApiCareLocationDto,
		isFetchingOrderData,
		isFetchingCareLocationData,
	};
}
