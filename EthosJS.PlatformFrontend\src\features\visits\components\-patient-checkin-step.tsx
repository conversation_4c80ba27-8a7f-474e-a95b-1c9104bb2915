import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON>, Typography, Button, Box } from '@mui/material';
import {
  Clock,
  CreditCard,
  IdCard,
  UploadCloud,
  StickyNote,
} from 'lucide-react';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { FactCheckOutlined, NoteAdd } from '@mui/icons-material';
import dayjs, { Dayjs } from 'dayjs';
import MainCardContainer from '@components/main-container/main-card-container';
import FileUploadContainer from '@components/file-upload-container';
import type { FileFormData } from '@components/file-upload-container/types';
import ChipSummary, { ChipData } from '@components/chip-summary';
import NotesContainer, { Note, NoteItem } from '@components/notes-container';

import usePreStudyStore from '@hooks/use-pre-study-store';
import { useStore } from '@tanstack/react-store';
import { TimePicker } from '@mui/x-date-pickers-pro';
import { FileItem } from '@components/file-upload-container/file-item';

interface PatientCheckinStepProps {
  formRef: React.RefObject<any>;
  studyId: string;
  patientId: string;
  patientWfId: string;
}

interface VerificationState {
  patientIdentity: 'unverified' | 'verified';
  insuranceCard: 'unverified' | 'verified';
}

interface UploadedFileItem {
  id: string;
  title: string;
  fileName: string;
  uploadedBy: string;
  uploadedTime: string;
  category?: string;
  documentType?: string;
  issueDate?: string;
  expiryDate?: string;
  fileId?: string;
  fileSize?: string;
  status?: 'uploading' | 'completed' | 'failed';
}

const PatientCheckinStep = ({
  formRef,
  studyId,
  patientId,
  patientWfId
}: PatientCheckinStepProps) => {
  const { store, actions } = usePreStudyStore();
  const { values } = useStore(store, (state) => state);

  const [actualArrivalTime, setActualArrivalTime] = useState<Dayjs | null>(
    values.patientCheckin?.actualArrivalTime ? dayjs(values.patientCheckin.actualArrivalTime) : null
  );
  const [verificationState, setVerificationState] = useState<VerificationState>({
    patientIdentity: values.patientCheckin?.patientIdentityVerified ? 'verified' : 'unverified',
    insuranceCard: values.patientCheckin?.insuranceCardVerified ? 'verified' : 'unverified'
  });
  const [notes, setNotes] = useState<Note[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<Record<string, boolean>>({
    'preStudyQuestionnaire':
      values.patientCheckin?.requiredFormsUploaded?.preStudyQuestionnaire || false,
    'sleepStudyConsent': values.patientCheckin?.requiredFormsUploaded?.sleepStudyConsent || false,
    'hipaaAcknowledgment':
      values.patientCheckin?.requiredFormsUploaded?.hipaaAcknowledgment || false,
    'videoRecordingConsent':
      values.patientCheckin?.requiredFormsUploaded?.videoRecordingConsent || false,
    'photo-id': false,
    'insurance-card': false
  });

  const [uploadedFileItems, setUploadedFileItems] = useState<Record<string, UploadedFileItem[]>>({
    'preStudyQuestionnaire': [],
    'sleepStudyConsent': [],
    'hipaaAcknowledgment': [],
    'videoRecordingConsent': [],
    'photo-id': [],
    'insurance-card': []
  });

  const scheduledTime = '11:00 PM'; // This would come from props or API

  const handleVerificationToggle = useCallback(
    (type: keyof VerificationState) => {
      setVerificationState((prev) => {
        const newState = {
          ...prev,
          [type]: prev[type] === 'unverified' ? 'verified' : 'unverified'
        };

        // Update store immediately using current values from the store
        const currentCheckinData = store.state.values.patientCheckin;
        actions.setValues({
          patientCheckin: {
            ...currentCheckinData,
            patientIdentityVerified: newState.patientIdentity === 'verified',
            insuranceCardVerified: newState.insuranceCard === 'verified'
          }
        });

        return newState;
      });
    },
    [actions, store]
  );

  const handleTimeChange = useCallback(
    (newValue: Dayjs | null) => {
      setActualArrivalTime(newValue);
      if (newValue) {
        const currentCheckinData = store.state.values.patientCheckin;
        actions.setValues({
          patientCheckin: {
            ...currentCheckinData,
            actualArrivalTime: newValue.toISOString()
          }
        });
      }
    },
    [actions, store]
  );

  const handleAddNote = (content: string) => {
    const newNote: Note = {
      id: Date.now().toString(),
      header: 'Added by John Scott, May 7, 2025 at 9:15 AM',
      content
    };
    setNotes((prev) => [...prev, newNote]);
  };

  const handleEditNote = (id: string, updatedNote: Omit<Note, 'id'>) => {
    setNotes((prev) => prev.map((note) => (note.id === id ? { ...updatedNote, id } : note)));
  };

  const handleDeleteNote = (id: string) => {
    setNotes((prev) => prev.filter((note) => note.id !== id));
  };

  const handleAddFile = (formType: string, fileData: FileFormData) => {
    const newFile: UploadedFileItem = {
      id: fileData.fileId || `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      title: fileData.documentType,
      fileName: fileData.fileName || 'document.pdf',
      uploadedBy: 'John Scott',
      uploadedTime: 'Just now',
      category: fileData.category,
      documentType: fileData.documentType,
      issueDate: fileData.issueDate,
      expiryDate: fileData.expiryDate,
      fileId: fileData.fileId,
      fileSize: '2.4 MB',
      status: 'completed'
    };

    setUploadedFileItems((prev) => ({
      ...prev,
      [formType]: [...prev[formType], newFile]
    }));

    setUploadedFiles((prev) => ({
      ...prev,
      [formType]: true
    }));

    // Update store with required forms upload state
    const formTypeMapping: Record<string, string> = {
      'preStudyQuestionnaire': 'preStudyQuestionnaire',
      'sleepStudyConsent': 'sleepStudyConsent',
      'hipaaAcknowledgment': 'hipaaAcknowledgment',
      'videoRecordingConsent': 'videoRecordingConsent'
    };

    // Only update store for required forms (not photo-id or insurance-card)
    if (formTypeMapping[formType]) {
      const currentCheckinData = store.state.values.patientCheckin;
      actions.setValues({
        patientCheckin: {
          ...currentCheckinData,
          requiredFormsUploaded: {
            ...currentCheckinData.requiredFormsUploaded,
            [formTypeMapping[formType]]: true
          }
        }
      });
    }
  };

  const handleEditFile = (formType: string, fileId: string, fileData: FileFormData) => {
    setUploadedFileItems((prev) => ({
      ...prev,
      [formType]: prev[formType].map((file) =>
        file.id === fileId
          ? {
              ...file,
              title: fileData.documentType, // Update title to match document type
              category: fileData.category,
              documentType: fileData.documentType,
              issueDate: fileData.issueDate,
              expiryDate: fileData.expiryDate,
              fileName: fileData.fileName || file.fileName,
              fileId: fileData.fileId || file.fileId,
              uploadedTime: 'Just now' // Update timestamp to show it was recently edited
            }
          : file
      )
    }));
  };

  const handleDeleteFile = (formType: string, fileId: string) => {
    setUploadedFileItems((prev) => ({
      ...prev,
      [formType]: prev[formType].filter((file) => file.id !== fileId)
    }));

    // If no files left, set uploaded state to false
    const remainingFiles = uploadedFileItems[formType].filter((file) => file.id !== fileId);
    if (remainingFiles.length === 0) {
      setUploadedFiles((prev) => ({
        ...prev,
        [formType]: false
      }));

      // Update store with required forms upload state
      const formTypeMapping: Record<string, string> = {
        'preStudyQuestionnaire': 'preStudyQuestionnaire',
        'sleepStudyConsent': 'sleepStudyConsent',
        'hipaaAcknowledgment': 'hipaaAcknowledgment',
        'videoRecordingConsent': 'videoRecordingConsent'
      };

      // Only update store for required forms (not photo-id or insurance-card)
      if (formTypeMapping[formType]) {
        const currentCheckinData = store.state.values.patientCheckin;
        actions.setValues({
          patientCheckin: {
            ...currentCheckinData,
            requiredFormsUploaded: {
              ...currentCheckinData.requiredFormsUploaded,
              [formTypeMapping[formType]]: false
            }
          }
        });
      }
    }
  };

  const getUploadedCount = () => {
    const requiredFormsKeys = [
      'preStudyQuestionnaire',
      'sleepStudyConsent',
      'hipaaAcknowledgment',
      'videoRecordingConsent'
    ];
    return requiredFormsKeys.filter((key) => uploadedFiles[key]).length;
  };

  const getVerificationButton = (type: keyof VerificationState) => {
    const isVerified = verificationState[type] === 'verified';
    return (
      <Button
        variant={'outlined'}
        color={isVerified ? 'inherit' : 'primary'}
        size="medium"
        onClick={() => handleVerificationToggle(type)}
        startIcon={isVerified ? <CheckCircleIcon color="success" fontSize="small" /> : undefined}
        sx={{
          minWidth: '100px',
          textTransform: 'none',
          borderRadius: 2,
          ...(isVerified && {
            backgroundColor: 'white',
            color: 'success.main',
            border: '1px solid',
            borderColor: 'divider',
            '& .MuiButton-startIcon': {
              color: 'success.main'
            }
          })
        }}>
        {isVerified ? 'Verified' : 'Verify'}
      </Button>
    );
  };

  // Mock patient data - this would come from props or API
  const patientData: ChipData[] = [
    {
      label: 'Full Name',
      value: 'John Smith',
      color: 'primary',
    },
    {
      label: 'DOB',
      value: '06/01/1969',
      color: 'primary',
    }
  ];

  // Mock insurance data - this would come from props or API
  const insuranceData: ChipData[] = [
    {
      label: 'Insurance Carrier',
      value: 'Blue Cross Blue Shield',
      color: 'primary',
    },
    {
      label: 'Plan Type',
      value: 'PPO',
      color: 'primary',
    },
    {
      label: 'Insurance ID',
      value: 'XHP123456789',
      color: 'primary',
    },
    {
      label: 'Policy ID',
      value: 'BC123456789',
      color: 'primary',
    }
  ];

  return (
    <Stack spacing="12px">
      {/* Arrival Information */}
      <MainCardContainer title="Arrival Information" icon={<Clock />} color="gray" emphasis="low">
        <Stack gap="3px">
          <Box>
            <TimePicker
              label="Actual Arrival Time"
              value={actualArrivalTime}
              onChange={handleTimeChange}
              slotProps={{
                textField: {
                  fullWidth: true
                }
              }}
            />
          </Box>
          <Typography fontWeight={'regular'} fontSize={'0.75rem'} color="text.secondary" sx={{ paddingLeft: "14px", paddingRight: "14px" }}>
            Scheduled: {scheduledTime}
          </Typography>
        </Stack>
      </MainCardContainer>

      {/* Patient Identity Verification */}
      <MainCardContainer
        title="Patient Identity Verification"
        descriptionSubheader="Check the patient name, DOB and SSN matches with system records"
        icon={<IdCard />}
        color={verificationState.patientIdentity === 'verified' ? 'success' : 'primary'}
        emphasis="low"
        customAction={getVerificationButton('patientIdentity')}>
        <Stack gap="12px">
          <ChipSummary items={patientData} variant="outlined" />
          <FileUploadContainer
            title="Upload Photo ID"
            icon={<UploadCloud />}
            emphasis={uploadedFiles['photo-id'] ? 'medium' : 'low'}
            color="primary"
            workflowId={patientWfId}
            onAddFile={(fileData: FileFormData) => handleAddFile('photo-id', fileData)}
            onEditFile={(fileId: string, fileData: FileFormData) =>
              handleEditFile('photo-id', fileId, fileData)
            }
            onDeleteFile={(fileId: string) => handleDeleteFile('photo-id', fileId)}>
            {uploadedFileItems['photo-id'].map((file) => (
              <FileItem key={file.id} file={file} icon={<UploadCloud />} allowEdit={true} />
            ))}
          </FileUploadContainer>
        </Stack>
      </MainCardContainer>

      {/* Insurance Card Verification */}
      <MainCardContainer
        title="Insurance Card Verification"
        descriptionSubheader="Check the insurance details match with system records"
        icon={<CreditCard />}
        color={verificationState.insuranceCard === 'verified' ? 'success' : 'primary'}
        emphasis="low"
        customAction={getVerificationButton('insuranceCard')}>
        <Stack gap="12px">
          <ChipSummary items={insuranceData} variant="outlined" />
          <FileUploadContainer
            title="Upload Insurance Card"
            icon={<UploadCloud />}
            emphasis={uploadedFileItems['insurance-card']?.length > 0 ? 'medium' : 'low'}
            color="primary"
            workflowId={patientWfId}
            onAddFile={(fileData: FileFormData) => handleAddFile('insurance-card', fileData)}
            onEditFile={(fileId: string, fileData: FileFormData) =>
              handleEditFile('insurance-card', fileId, fileData)
            }
            onDeleteFile={(fileId: string) => handleDeleteFile('insurance-card', fileId)}>
            {uploadedFileItems['insurance-card']?.map((file) => (
              <FileItem key={file.id} file={file} />
            ))}
          </FileUploadContainer>
        </Stack>
      </MainCardContainer>

      {/* Required Forms */}
      <MainCardContainer
        title="Required Forms"
        icon={<FactCheckOutlined />}
        color="primary"
        emphasis="low"
        customAction={
          <Typography
            variant="body2"
            sx={{
              fontWeight: 'medium',
              border: `1px solid`,
              borderRadius: 2,
              px: 1,
              py: 0.5
            }}>
            Files Uploaded: {getUploadedCount()}/4
          </Typography>
        }>
        <Stack gap="12px">
          <FileUploadContainer
            title="Pre-Study Questionnaire"
            icon={<UploadCloud />}
            emphasis={uploadedFileItems['preStudyQuestionnaire']?.length > 0 ? 'medium' : 'low'}
            color="primary"
            workflowId={patientWfId}
            onAddFile={(fileData: FileFormData) =>
              handleAddFile('preStudyQuestionnaire', fileData)
            }
            onEditFile={(fileId: string, fileData: FileFormData) =>
              handleEditFile('preStudyQuestionnaire', fileId, fileData)
            }
            onDeleteFile={(fileId: string) => handleDeleteFile('preStudyQuestionnaire', fileId)}>
            {uploadedFileItems['preStudyQuestionnaire']?.map((file) => (
              <FileItem key={file.id} file={file} />
            ))}
          </FileUploadContainer>

          <FileUploadContainer
            title="Sleep Study Consent Form"
            icon={<UploadCloud />}
            emphasis={uploadedFileItems['sleepStudyConsent']?.length > 0 ? 'medium' : 'low'}
            color="primary"
            workflowId={patientWfId}
            onAddFile={(fileData: FileFormData) => handleAddFile('sleepStudyConsent', fileData)}
            onEditFile={(fileId: string, fileData: FileFormData) =>
              handleEditFile('sleepStudyConsent', fileId, fileData)
            }
            onDeleteFile={(fileId: string) => handleDeleteFile('sleepStudyConsent', fileId)}>
            {uploadedFileItems['sleepStudyConsent']?.map((file) => (
              <FileItem key={file.id} file={file} />
            ))}
          </FileUploadContainer>

          <FileUploadContainer
            title="HIPAA Acknowledgment"
            icon={<UploadCloud />}
            emphasis={uploadedFileItems['hipaaAcknowledgment']?.length > 0 ? 'medium' : 'low'}
            color="primary"
            workflowId={patientWfId}
            onAddFile={(fileData: FileFormData) => handleAddFile('hipaaAcknowledgment', fileData)}
            onEditFile={(fileId: string, fileData: FileFormData) =>
              handleEditFile('hipaaAcknowledgment', fileId, fileData)
            }
            onDeleteFile={(fileId: string) => handleDeleteFile('hipaaAcknowledgment', fileId)}>
            {uploadedFileItems['hipaaAcknowledgment']?.map((file) => (
              <FileItem key={file.id} file={file} />
            ))}
          </FileUploadContainer>

          <FileUploadContainer
            title="Video Recording Consent"
            icon={<UploadCloud />}
            emphasis={uploadedFileItems['videoRecordingConsent']?.length > 0 ? 'medium' : 'low'}
            color="primary"
            workflowId={patientWfId}
            onAddFile={(fileData: FileFormData) =>
              handleAddFile('videoRecordingConsent', fileData)
            }
            onEditFile={(fileId: string, fileData: FileFormData) =>
              handleEditFile('videoRecordingConsent', fileId, fileData)
            }
            onDeleteFile={(fileId: string) => handleDeleteFile('videoRecordingConsent', fileId)}>
            {uploadedFileItems['videoRecordingConsent']?.map((file) => (
              <FileItem key={file.id} file={file} />
            ))}
          </FileUploadContainer>
        </Stack>
      </MainCardContainer>

      {/* Check-in Notes */}
      <NotesContainer
        title="Check-in Notes"
        icon={<NoteAdd />}
        onAddNote={handleAddNote}
        onDeleteNote={handleDeleteNote}
        allowAdd={true}
        allowDelete={true}
        placeholder=""
        maxLength={500}>
        {notes.map((note) => (
          <NoteItem
            icon={<StickyNote />}
            key={note.id}
            note={note}
            allowEdit={true}
            onEdit={(updatedNote) => handleEditNote(note.id, updatedNote)}
          />
        ))}
      </NotesContainer>
    </Stack>
  );
};

export default PatientCheckinStep;
