import { Box, styled, Typography } from '@mui/material';
import { Info } from 'lucide-react';

const Container = styled(Box)(() => ({
  padding: '12px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  backgroundColor: '#FFFAEB',
  color: '#7A2E0E',
  position: 'relative',
  '&:not(:last-child)': {
    borderBottom: '1px solid #FEF3C7'
  }
}));

const IconContainer = styled(Box)(() => ({
  position: 'absolute',
  top: '16px',
  right: '16px',
  display: 'flex',
  alignItems: 'center',
  color: '#7A2E0E'
}));

interface CardWarningProps {
  title: string;
  subtitle?: string;
}

export default function CardWarning({ title, subtitle }: CardWarningProps) {
  return (
    <Container>
      <Typography fontWeight={500} fontSize={'16px'} color="inherit">
        {title}
      </Typography>
      {subtitle && (
        <Typography fontSize={'13px'} color="inherit">
          {subtitle}
        </Typography>
      )}
      <IconContainer>
        <Info fontSize="medium" color="#DC6803" />
      </IconContainer>
    </Container>
  );
}
