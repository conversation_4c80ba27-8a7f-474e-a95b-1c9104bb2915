import { EthosWorkflowsApiPersonalEmailDto } from '@client/workflows';
import { formOptions } from '@tanstack/react-form';
import { z } from 'zod';

const emailSchema = z.any().transform((raw: EthosWorkflowsApiPersonalEmailDto) => {
	return {
		...raw,
	};
});

const defaultValues: EthosWorkflowsApiPersonalEmailDto = {
	use: null!,
	value: '',
	isPreferred: false,
};

function emailFormOptions(savedData?: EthosWorkflowsApiPersonalEmailDto) {
	return formOptions({
		defaultValues: {
			...defaultValues,
			...(savedData ? savedData : {}),
		},
	});
}

export { emailFormOptions, defaultValues, emailSchema };
