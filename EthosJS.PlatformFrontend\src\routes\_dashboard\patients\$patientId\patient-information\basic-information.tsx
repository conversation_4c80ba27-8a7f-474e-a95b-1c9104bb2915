import AddBasicInformationStep from '@features/patient-create/components/steps/basic-information.step';
import { createFileRoute } from '@tanstack/react-router';

export const Route = createFileRoute(
	'/_dashboard/patients/$patientId/patient-information/basic-information'
)({
	component: RouteComponent,
});

function RouteComponent() {
	const { patientId } = Route.useParams();
	const navigate = Route.useNavigate();

	return (
		<AddBasicInformationStep
			patientId={patientId}
			successCallback={() =>
				navigate({
					to: '/patients/$patientId/patient-information/contact-information',
					params: { patientId },
				})
			}
		/>
	);
}
