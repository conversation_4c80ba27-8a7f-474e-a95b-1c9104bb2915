import { postApiInsuranceVerificationStartMutation } from '@client/workflows/@tanstack/react-query.gen';
import { useMutation } from '@tanstack/react-query';
import { useCallback } from 'react';

export default function useInsuranceVerificationStart() {
	const {
		mutate: startVerification,
		isPending: isStartingVerification,
		error: startVerificationError,
	} = useMutation({
		...postApiInsuranceVerificationStartMutation({
			responseType: 'json',
		}),
		onError(error) {
			console.error(error);
		},
		onSuccess(mutationData, variables) {
			const { jobId } = mutationData as { jobId: string; message: string };
			const { body } = variables;
			const { studyId } = body as { studyId: string; serviceId: string };
			if (jobId) {
				// Store jobId in localStorage
				localStorage.setItem(`insurance_verification_job_${studyId}`, jobId);
			}
		},
	});
	const startInsuranceVerification = useCallback(
		({ studyId, serviceId }: { studyId: string; serviceId: string }) => {
			startVerification({
				body: { studyId, serviceId },
			});
		},
		[startVerification]
	);

	return {
		startInsuranceVerification,
		isStartingVerification,
		startVerificationError,
	};
}
