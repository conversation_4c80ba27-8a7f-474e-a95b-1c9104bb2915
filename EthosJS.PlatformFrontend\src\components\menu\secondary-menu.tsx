import { MenuItem } from '@mui/material';
import { ReactElement } from 'react';
import MenuContainer from './menu-container';
import SecondaryMenuHeader, { SecondaryMenuHeaderProps } from './secondary-menu-header';
import StyledList from './menu-list';

export interface SecondaryMenuProps {
	headerProps?: SecondaryMenuHeaderProps;
	topContainer?: ReactElement;
	bottomContainer?: ReactElement;
	children: ReactElement<typeof MenuItem> | ReactElement<typeof MenuItem>[];
}

export default function SecondaryMenu({
	headerProps,
	topContainer,
	bottomContainer,
	children,
}: SecondaryMenuProps) {
	return (
		<MenuContainer color="primary">
			{headerProps && <SecondaryMenuHeader {...headerProps} />}
			{topContainer}
			<StyledList>{children}</StyledList>
			{bottomContainer}
		</MenuContainer>
	);
}
