import MainCardContainer from '@components/main-container/main-card-container';
import { Stack } from '@mui/material';
import InsuranceCard from './insurance-card';

interface AssociatedInsuranceReviewProps {
	insurances: Array<string>;
}

export default function AssociatedInsuranceReview({ insurances }: AssociatedInsuranceReviewProps) {
	return (
		<MainCardContainer
			title="Insurance Information"
			color="primary"
			emphasis="low">
			<Stack gap={2}>
				{insurances.map((insuranceId, index) => (
					<InsuranceCard
						key={index}
						insuranceId={insuranceId}
						slot={index + 1}
					/>
				))}
			</Stack>
		</MainCardContainer>
	);
}
