import React, { cloneElement, CSSProperties, isValidElement, JSX, ReactNode } from 'react';
import { Stack, Box, Typography, useTheme, ListItem as ListItemPrimitive, ListItemProps as ListItemPrimitiveProps } from '@mui/material';

type Size = keyof typeof ICON_SIZE_S;

type ListItemProps = {
  icon?: React.ReactNode;
  title: String;
  subTitle?: String;
  extra?: React.ReactNode;
  size: Size
  leading?: ReactNode
  renderTitle?: () => JSX.Element
  renderActions?: () => JSX.Element
} & ListItemPrimitiveProps;

const ICON_SIZE_S: Record<string, CSSProperties> = {
  large: {
    width: '1.875rem',
    height: '1.875rem'
  },
  small: {
    width: '1.5rem',
    height: '1.5rem'
  }
}

const ListItem = ({ icon, renderTitle, renderActions, title, subTitle, extra, size, leading, ...rest }: ListItemProps) => {
  const { palette } = useTheme();

  return (
    <ListItemPrimitive
      sx={{
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        userSelect: 'none',
        px: 0
      }}
      {...rest}
    >
      {/* leading */}
      {leading && <Stack direction={'row'} gap={1} alignItems={'center'}>{leading}</Stack>}

      <Stack flexDirection="row" alignItems="center" gap='12px'>
        <Box
          sx={{
            color: palette.primary.dark,
          }}
        >
          {isValidElement(icon) && cloneElement(icon, { style: (ICON_SIZE_S[size]) } as any)}
        </Box>
        <Stack >
          {renderTitle?.() ?? <Typography variant={size == 'large' ? 'h6' : 'body1'} color='primary.dark'>{title} </Typography>}
          {subTitle && (
            <Typography
              variant={'asapCondensed'}
              color='primary.dark'
            >
              {subTitle}
            </Typography>
          )}
        </Stack>
      </Stack>

      {/* extra */}
      {typeof renderActions === 'function' && <Stack direction={'row'} gap={1} alignItems={'center'}>{renderActions()}</Stack>}
      {extra && <Stack direction={'row'} gap={1} alignItems={'center'}>{extra}</Stack>}
    </ListItemPrimitive>
  );
};

export default ListItem;
