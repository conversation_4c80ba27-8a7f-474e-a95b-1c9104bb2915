import React from 'react';
import { Box, Typography, Paper, Theme, SxProps } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

interface CompletedWorkflowProps {
    text?: string;
    sx?: SxProps<Theme>;
    iconSize?: 'small' | 'medium' | 'large';
}

const CompletedWorkflow: React.FC<CompletedWorkflowProps> = ({
    text = 'Workflow Complete',
    sx,
    iconSize = 'large'
}) => {
    return (
        <Paper
            elevation={0}
            sx={{
                display: 'flex',
                alignItems: 'center',
                flexDirection: 'column',
                justifyContent: 'center',
                height: '100%',
                width: '100%',
                gap: 1.5,
                p: 1.5,
                borderRadius: 1,
                color: 'success.dark',
                ...sx
            }}
        >
            <CheckCircleIcon
                color="success"
                sx={{ color: 'success.main', fontSize: '6rem' }}
            />
            <Typography
                variant="h2"
                fontWeight="medium"
                sx={{ color: 'success.dark' }}
            >
                {text}
            </Typography>
        </Paper>
    );
};

export default CompletedWorkflow;
