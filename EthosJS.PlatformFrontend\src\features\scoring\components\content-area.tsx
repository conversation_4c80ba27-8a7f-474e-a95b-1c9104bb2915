import { Stack } from "@mui/material"
import * as containers from "./containers";
import Card from "@components/card";
import ScoringForm from "@features/scoring/forms/scoring-form";

const { StudySummary, PatientChartDocuments, TechnicalNotes, ScoringNotesObservation, FinalNotesPhysicians } = containers;

const ContenrArea = () => {
   return (
      <Card>
         <Stack gap={1}>
            <StudySummary />
            <PatientChartDocuments />
            <TechnicalNotes />
            <ScoringNotesObservation />
            <FinalNotesPhysicians />
            <ScoringForm
               onSubmit={() => { }}
            />
         </Stack>
      </Card>
   )
}

export default ContenrArea;