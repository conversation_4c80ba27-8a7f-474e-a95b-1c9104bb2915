import { PatientCreate, PatientRead } from '@auth/scopes';
import { EthosWorkflowsApiPatientDto } from '@client/workflows';
import { getApiPatientByIdOptions } from '@client/workflows/@tanstack/react-query.gen';
import { useQuery } from '@tanstack/react-query';
import { ChipData } from '@components/chip-summary';
import { maskPhoneNumber } from '@utils/maskers';
import dayjs from 'dayjs';
import { map } from 'lodash';

export default function useDetails({ patientId }: { patientId: string }) {
	const {
		data: patientDetails,
		isFetching: isFetchingPatientDetails,
		error: fetchPatientDetailsError,
	} = useQuery({
		...getApiPatientByIdOptions({
			responseType: 'json',
			scopes: [PatientCreate.value, PatientRead.value],
			path: { id: patientId },
		}),
		select: (data: EthosWorkflowsApiPatientDto) => {
			const patient = data as EthosWorkflowsApiPatientDto;

			const {
				names,
				demographics,
				contactInformation: patientContactInformation,
				schedulingPreferences,
				clinicalConsiderations,
			} = patient;

			// Patient Information
			const firstName = names?.[0]?.firstName || '';
			const lastName = names?.[0]?.lastName || '';
			const fullName = `${firstName} ${lastName}`.trim();
			const dob = demographics?.dateOfBirth
				? dayjs(demographics.dateOfBirth).format('MM/DD/YYYY')
				: '';

			const patientInformation: ChipData[] = [
				{ label: 'Full Name', value: fullName },
				{ label: 'Patient ID', value: patient.id?.toString() || '' },
				{ label: 'DOB', value: dob },
			].filter((item) => item.value);

			// Contact Information
			const contactInformation: ChipData[] = [
				...(patientContactInformation?.phoneNumbers?.map((phone, index) => ({
					label: `Phone ${index + 1}`,
					value: maskPhoneNumber(phone.value),
				})) || []),
				...(patientContactInformation?.emails?.map((email, index) => ({
					label: `Email ${index + 1}`,
					value: email.value,
				})) || []),
			].filter((item) => item.value);

			// Preferences & Special Accommodations
			const preferences: ChipData[] = [
				{
					label: 'Technician Preference',
					value: schedulingPreferences?.technicianPreference?.toString() || '',
				},
				{
					label: 'Preferred Day of Week',
					value: schedulingPreferences?.preferredDayOfWeek?.toString() || '',
				},
				...map(clinicalConsiderations, (item) => ({
					label: 'Special Accommodations',
					value: item.toString(),
				})),
			].filter((item) => item);

			return {
				patientInformation,
				contactInformation,
				preferences,
			};
		},
	});

	const defaultPatientDetails = {
		patientInformation: [],
		contactInformation: [],
		preferences: [],
	};

	return {
		patientDetails: patientDetails ?? defaultPatientDetails,
		isFetchingPatientDetails,
		fetchPatientDetailsError,
	};
}
