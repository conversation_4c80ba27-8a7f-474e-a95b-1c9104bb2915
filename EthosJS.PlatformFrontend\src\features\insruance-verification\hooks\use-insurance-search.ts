import { EthosModelQueryDto1EthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null } from '@client/workflows';
import { postApiInsuranceSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import { useQuery } from '@tanstack/react-query';
import { InsuranceQuery, Query } from '@utils/query-dsl';

export default function useInsuranceSearch({ insuranceId }: { insuranceId?: string }) {
	const {
		data: insuranceData,
		isFetching: isFetchingInsuranceData,
		error: fetchInsuranceError,
	} = useQuery({
		...postApiInsuranceSearchOptions({
			responseType: 'json',
			body: Query.literal(
				InsuranceQuery.withId(Number(insuranceId))
			) as unknown as EthosModelQueryDto1EthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!insuranceId,
	});
	return {
		insuranceData,
		isFetchingInsuranceData,
		fetchInsuranceError,
	};
}
