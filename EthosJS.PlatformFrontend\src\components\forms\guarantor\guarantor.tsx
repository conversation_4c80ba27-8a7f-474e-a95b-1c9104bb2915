import AddInfoCard from '@components/add-info-card'
import ChipSummary from '@components/chip-summary';
import { UserCircle, Pencil } from 'lucide-react';
import  { useState } from 'react'

const Guarantor = () => {
    const [showHeader, setShowHeader] = useState<boolean>(false)
    const values = [
        {
            label: "Policy Holder",
            value: "Walter Bishop"
        },
        {
            label: "Policy Id",
            value: "7445571"
        },
        {
            label: "Member Id",
            value: "874185478751"
        },
        {
            label: "Plan Type",
            value: "PPO"
        },
        {
            label: "Group Number",
            value: "874185478751"
        },
        {
            label: "Insurrance Id",
            value: "XHP123456789"
        },
        {
            label: "Phone",
            value: "(*************"
        },
        {
            label: "Email",
            value: "XHP123456789"
        },
        {
            label: "Address",
            value: "dfghjkl  fghjku"
        },
        {
            label: "Address Type",
            value: "asdfghjkrty fghj"
        },
    ]

    return (
        <AddInfoCard
            title="Guarantor"
            icon={<UserCircle />}
            onClick={() => { setShowHeader(!showHeader) }}
            showHeader={showHeader}
        >
            <AddInfoCard
                title="Peter Bishop, Son"
                icon={<UserCircle />}
                actionIcon={<Pencil />}
                onClick={function (): void {
                    throw new Error('Function not implemented.');
                }} showHeader={false}
            >
                <ChipSummary
                    items={values.map((item) => ({
                        label: item.label,
                        value: item.value,
                    }))}
                />
            </AddInfoCard>
        </AddInfoCard>
    )
}

export default Guarantor
