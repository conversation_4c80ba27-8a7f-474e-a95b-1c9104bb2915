import AddInfoCard from '@components/add-info-card';
import SectionHeader from '@components/section-header';
import { Add } from '@mui/icons-material';
import { CircleAlert, File } from 'lucide-react';
import Card from '@components/card';
import React, { useState } from 'react';
import CardHeader from '@components/card-header';
import { CardContent, FormControl, IconButton, InputLabel, Stack } from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import { Pencil } from 'lucide-react';
import ChipSummary from '@components/chip-summary';
import { withForm } from '@hooks/app-form';

const values = [
	{
		label: 'Policy Holder',
		value: '<PERSON> Bishop',
	},
	{
		label: 'Policy Id',
		value: '7445571',
	},
	{
		label: 'Member Id',
		value: '874185478751',
	},
	{
		label: 'Plan Type',
		value: 'PPO',
	},
	{
		label: 'Group Number',
		value: '874185478751',
	},
	{
		label: 'Insurrance Id',
		value: 'XHP123456789',
	},
	{
		label: 'Phone',
		value: '(*************',
	},
	{
		label: 'Email',
		value: 'XHP123456789',
	},
	{
		label: 'Address',
		value: 'dfghjkl  fghjku',
	},
	{
		label: 'Address Type',
		value: 'asdfghjkrty fghj',
	},
];

const AssociatedInsurance = withForm({
	defaultValues: {},
	render: function Render({ form }) {
		const [showHeader, setShowHeader] = useState<boolean>(false);
		const [data] = useState('');

		const handleChange = (e: any) => {
			console.log('successful.', e.target.value);
		};

		return (
			<AddInfoCard
				title="Associated Insurance"
				icon={<File />}
				onClick={() => {
					setShowHeader(!showHeader);
				}}
				sx={{ mb: 0 }}
				showHeader={showHeader}>
				<SectionHeader title="Select the insurance priority and add a guarantor." />

				<Card>
					<CardHeader
						title="Blue Cross Blue Shield"
						avatar={<CircleAlert />}
						action={
							<Stack flexDirection="row">
								<FormControl
									sx={{ m: 1, minWidth: 120 }}
									size="small">
									<InputLabel id="select-label">Select</InputLabel>
									<Select
										labelId="slect-label"
										label="Select"
										onChange={handleChange}
										value={data}>
										<MenuItem value="First">First</MenuItem>
										<MenuItem value="Second">Second</MenuItem>
										<MenuItem value="Third">Third</MenuItem>
									</Select>
								</FormControl>
								<IconButton aria-label="Edit">
									<Pencil />
								</IconButton>
							</Stack>
						}
					/>
					<CardContent>
						<ChipSummary
							items={values.map((item) => ({
								label: item.label,
								value: item.value,
							}))}
						/>
					</CardContent>
				</Card>
			</AddInfoCard>
		);
	},
});

export default AssociatedInsurance;
