import { PatientCreate, PatientRead } from '@auth/scopes';
import { EthosWorkflowsApiCreateOrderDto, SystemTextJsonNodesJsonNode } from '@client/workflows';
import { postApiOrderDraftMutation } from '@client/workflows/@tanstack/react-query.gen';
import { useMutation } from '@tanstack/react-query';
import { useCallback } from 'react';
import { OrderState } from '../types';

export default function useOrderCreate() {
	const {
		mutate: createOrderMutation,
		isPending: isCreatingOrder,
		error: createOrderError,
		reset: resetCreateOrderMutation,
	} = useMutation({
		...postApiOrderDraftMutation({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
		}),
		onError(error) {
			console.error(error);
		},
	});

	const createOrder = useCallback(
		async (
			data: Partial<EthosWorkflowsApiCreateOrderDto>,
			_state?: Partial<OrderState>,
			successCallback?: (orderId: string) => void
		) => {
			createOrderMutation(
				{
					body: {
						...data,
						_state,
					} as unknown as { [key: string]: SystemTextJsonNodesJsonNode },
				},
				{
					onSuccess(mutationData) {
						const { entityId } = mutationData;
						if (successCallback && entityId) {
							successCallback(entityId);
						}
					},
				}
			);
		},
		[createOrderMutation]
	);

	return {
		createOrder,
		isCreatingOrder,
		createOrderError,
		resetCreateOrderMutation,
	};
}
