import MainCardContainer from '@components/main-container/main-card-container';
import { ArrowForward } from '@mui/icons-material';
import { Button, ButtonProps, Stack } from '@mui/material';

import SmartButtonOutlinedIcon from '@mui/icons-material/SmartButtonOutlined';
import StudyDetails from './study-details';
import PatientDetails from './patient-details';

interface IDashboard {
	onStartCreateAppoinment?: ButtonProps['onClick'];
	studyId: string;
	patientId: string;
}

const Dashboard = ({ studyId, patientId, onStartCreateAppoinment }: IDashboard) => {
	return (
		<Stack gap={2}>
			<MainCardContainer
				title="Create an appointment"
				icon={<SmartButtonOutlinedIcon />}
				customAction={
					<Button
						color="warning"
						variant="contained"
						startIcon={<ArrowForward />}
						onClick={onStartCreateAppoinment}>
						Start Here
					</Button>
				}
			/>
			<StudyDetails studyId={studyId} />
			<PatientDetails patientId={patientId} />
		</Stack>
	);
};

export default Dashboard;
