import { PatientCreate, PatientRead } from '@auth/scopes';
import { EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null } from '@client/workflows';
import { postApiPhysicianSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import ChipSummary from '@components/chip-summary';
import LoadingComponent from '@components/loading-component';
import MainCardContainer from '@components/main-container/main-card-container';
import { Stack } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { Query, PhysicianQuery } from '@utils/query-dsl';
import { IdCard, Stethoscope } from 'lucide-react';

interface PhysicianReviewProps {
	orderingPhysicianId: string | null;
	interpretingPhysicianId: string | null;
	referringPhysicianId: string | null;
	primaryCarePhysicianId: string | null;
}

export default function PhysicianReview({
	orderingPhysicianId,
	interpretingPhysicianId,
	referringPhysicianId,
	primaryCarePhysicianId,
}: PhysicianReviewProps) {
	return (
		<MainCardContainer
			icon={<Stethoscope />}
			title="Physicians"
			emphasis="low"
			color="primary">
			<Stack gap={2}>
				{orderingPhysicianId && (
					<PhysicianReviewItem
						title="Ordering Physician"
						physicianId={orderingPhysicianId}
					/>
				)}
				{interpretingPhysicianId && (
					<PhysicianReviewItem
						title="Interpreting Physician"
						physicianId={interpretingPhysicianId}
					/>
				)}
				{referringPhysicianId && (
					<PhysicianReviewItem
						title="Referring Physician"
						physicianId={referringPhysicianId}
					/>
				)}
				{primaryCarePhysicianId && (
					<PhysicianReviewItem
						title="Primary Care Physician"
						physicianId={primaryCarePhysicianId}
					/>
				)}
			</Stack>
		</MainCardContainer>
	);
}

function PhysicianReviewItem({ title, physicianId }: { title: string; physicianId: string }) {
	const { data: physicianData, isFetching: isPhysicianFetching } = useQuery({
		...postApiPhysicianSearchOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			body: Query.literal(
				PhysicianQuery.withId(physicianId)
			) as unknown as EthosModelQueryDto1EthosModelPhysicianQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!physicianId,
	});

	const physician = physicianData?.items?.[0];

	return (
		<MainCardContainer
			icon={<IdCard />}
			headerSize="small"
			title={title}
			color="gray"
			emphasis="low">
			{isPhysicianFetching ? (
				<LoadingComponent />
			) : (
				physician && (
					<ChipSummary
						variant="outlined"
						items={[
							{
								label: 'Name',
								value: `${physician?.names?.[0]?.firstName} ${physician?.names?.[0]?.lastName}`,
							},
							{ label: 'NPI', value: physician?.identifiers?.[0]?.value ?? '' },
							{ label: 'Specialty', value: 'N/A' },
						]}
					/>
				)
			)}
		</MainCardContainer>
	);
}
