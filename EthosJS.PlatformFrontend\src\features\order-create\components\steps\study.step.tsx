import CreateStudyForm from '@features/order-create/forms/study.form';
import { useQuery } from '@tanstack/react-query';
import { postApiInsuranceSearchOptions } from '@client/workflows/@tanstack/react-query.gen';
import { InsuranceQuery, Query } from '@utils/query-dsl';
import { PatientCreate, PatientRead } from '@auth/scopes';
import { OrderState, StepProps } from '@features/order-create/types';
import useOrder from '@features/order-create/hooks/use-order';
import useStudy from '@features/order-create/hooks/use-study';
import { EthosModelQueryDto1EthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null } from '@client/workflows';
import dayjs from 'dayjs';
import NotificationBanner from '@components/notification-banner';
import NotificationSnackbar from '@components/notification-snackbar';
import { NotificationState } from '@components/notification-snackbar';
import { useState } from 'react';

interface StudyStepProps extends Omit<StepProps, 'successCallback'> {
	studyId?: string;
	successCallback: (studyId: string) => void;
}

export default function StudyStep({
	patientId,
	orderId,
	studyId,
	successCallback,
}: StudyStepProps) {
	const { orderData, updateOrder } = useOrder({ orderId });

	const { data } = orderData ?? {};
	const orderState = (data?._state as unknown as OrderState) ?? {};

	const { studyData, updateStudy, updateStudyError, resetUpdateStudyMutation } = useStudy({
		studyId,
	});
	const savedData = studyData?.data;

	const [notification, setNotification] = useState<NotificationState>({
		message: '',
		severity: 'info',
	});

	const resetToast = () => {
		setNotification({
			message: '',
			severity: 'info',
		});
	};

	const {
		data: insuranceData,
		isFetching: isFetchingInsuranceData,
		error: fetchInsuranceError,
	} = useQuery({
		...postApiInsuranceSearchOptions({
			scopes: [PatientCreate.value, PatientRead.value],
			responseType: 'json',
			body: Query.literal(
				InsuranceQuery.withPatientId(patientId!)
			) as unknown as EthosModelQueryDto1EthosModelInsuranceQ_EthosModel_Version_1000_Culture_neutral_PublicKeyToken_null,
		}),
		enabled: !!patientId,
	});

	const onStudyUpdateSuccess = (newStudyId?: string) => {
		const { flowState, stepState } = orderState;
		updateOrder(
			{
				...orderData?.data,
			},
			{
				flowState: {
					...flowState,
					status: 'InProgress',
					progress: 25,
					lastUpdate: dayjs().format('MMM D, YYYY h:mm A'),
				},
				stepState: {
					...stepState,
					AddStudy: 'Complete',
					//AddCareLocation: 'InProgress',
				},
			},
			() => {
				if (newStudyId) {
					successCallback(newStudyId);
				}
			}
		);
	};

	return (
		<>
			<NotificationSnackbar
				notification={notification}
				onCloseToast={resetToast}
			/>
			<NotificationBanner
				message={updateStudyError?.message}
				severity="error"
				scrollIntoView
				onClose={resetUpdateStudyMutation}
			/>
			<CreateStudyForm
				insurances={insuranceData?.items ?? []}
				isInsuranceLoading={isFetchingInsuranceData}
				insuranceError={!!fetchInsuranceError}
				orderId={orderId!}
				savedData={savedData}
				onSubmit={(data) => {
					updateStudy(
						{
							...data,
							orderId,
						},
						onStudyUpdateSuccess
					);
				}}
				onSaveDraft={(newData) => {
					updateStudy({ ...newData }, () => {
						setNotification({
							message: 'Draft saved successfully',
							severity: 'success',
						});
					});
				}}
			/>
		</>
	);
}
