import { ValidationErrors } from '@app-types/validation';
import { AnyFieldMeta } from '@tanstack/react-form';
import { isEqual } from 'lodash';

function formHasValues<T>(formValues: T, defaultValues: T) {
	return typeof formValues !== 'undefined' && !isEqual(formValues, defaultValues);
}

function formHasErrors(fieldMeta: Record<string, AnyFieldMeta>) {
	return Object.keys(fieldMeta).some((key) => fieldMeta[key].errors.length > 0);
}

function getErrorsForIndexField(fieldName: string, errorMap?: ValidationErrors): ValidationErrors {
	if (!errorMap) return;
	const { fields } = errorMap;
	if (!fields) return;
	const fieldErrors = Object.keys(fields).reduce(
		(acc, cur) => {
			if (cur.includes(fieldName)) {
				const newKey = cur.replace(`${fieldName}.`, '');
				if (acc && !acc.fields[newKey]) {
					acc.fields[newKey] = [];
					acc.fields[newKey].push(...fields[cur]);
				}
				return acc;
			}
			return acc;
		},
		{
			fields: {},
		} as ValidationErrors
	);
	return fieldErrors;
}

export { formHasValues, formHasErrors, getErrorsForIndexField };
