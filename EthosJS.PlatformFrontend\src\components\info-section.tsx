import { Typography, Stack } from '@mui/material';
import { Info } from 'lucide-react';

interface infoSectionProps {
  title: string;
}

export default function InfoSection({ title }: infoSectionProps) {
  return (
    <Stack
      color="primary.dark"
      direction="row"
      justifyContent="flex-start"
      alignItems="center"
      spacing={1}>
      <Info />
      <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', lineHeight: 1 }}>
        {title}
      </Typography>
    </Stack>
  );
}
