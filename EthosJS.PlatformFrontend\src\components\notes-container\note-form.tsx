import React from 'react';
import { Stack } from '@mui/material';
import { useAppForm } from '@hooks/app-form';
import { useStore } from '@tanstack/react-form';
import { NoteFormProps } from './types';
import MainCardContainer from '@components/main-container/main-card-container';
import { NotebookPen } from 'lucide-react';

interface NoteFormData {
  content: string;
}

export const NoteForm: React.FC<NoteFormProps> = ({
  onSave,
  onCancel,
}) => {
  const form = useAppForm({
    defaultValues: {
      content: ''
    } as NoteFormData
  });

  const { values, canSubmit } = useStore(form.store, ({ values, canSubmit }) => ({
    values,
    canSubmit
  }));

  const handleSave = () => {
    if (values.content?.trim()) {
      onSave(values.content.trim());
      form.reset();
    }
  };

  const handleCancel = () => {
    form.reset();
    onCancel();
  };

  const isValid = values.content?.trim().length > 0;

  return (
    <MainCardContainer
      title="Add New Note"
      descriptionSubheader="Add any additional notes, special instructions, or patient-specific requirements"
      emphasis="high"
      color="primary"
      icon={<NotebookPen />}
      primaryActionType='Delete'
      onPrimaryAction={handleCancel}
      footerProps={{
        primaryButton1: {
          label: 'Add',
          onClick: handleSave,
          disabled: !isValid
        },
        primaryButton2: {
          label: 'Cancel',
          onClick: handleCancel
        }
      }}>
      <Stack sx={{ p: '16px' }}>
        <form.AppField
          name="content"
          children={(field) => (
            <field.AppTextField
              label=""
              size="medium"
              minRows={4}
              multiline
              variant="outlined"
              placeholder="Type here..."
              required
            />
          )}
        />
      </Stack>
    </MainCardContainer>
  );
};
