import { IPrimitiveQuery } from './core';

// Example primitive query types (to be defined in separate files)
export interface PatientQ extends IPrimitiveQuery {
  $type: 'WithId' | 'WithGivenName' | 'WithLastName' | 'WithDateOfBirth';
}
// Helper functions for PatientQ
export const PatientQuery = {
  withId: (id: string): PatientQ => ({
    $type: 'WithId',
    Id: id
  }),

  withGivenName: (firstName: string): PatientQ => ({
    $type: 'WithGivenName',
    FirstName: firstName
  }),

  withLastName: (lastName: string): PatientQ => ({
    $type: 'WithLastName',
    LastName: lastName
  }),

  withDateOfBirth: (dob: string): PatientQ => ({
    $type: 'WithDateOfBirth',
    Dob: dob
  })
};
