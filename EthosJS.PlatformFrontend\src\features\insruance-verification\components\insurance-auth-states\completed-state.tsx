import { GetApiInsuranceVerificationStatusByJobIdResponse } from '@client/workflows';
import VerifiedStatus from './verified-status';
import DeniedStatus from './denied-status';
import VerifiedNoAuthStatus from './verified-noauth-status';

export default function CompletedState({
	verificationStatus,
	onEditInsurance,
	onRetryVerification,
}: {
	verificationStatus: GetApiInsuranceVerificationStatusByJobIdResponse;
	onEditInsurance?: () => void;
	onRetryVerification?: () => void;
}) {
	const { currentFineGrainedStateName } = verificationStatus;

	if (currentFineGrainedStateName === 'VerificationSuccessfulAuthNotRequired') {
		return <VerifiedNoAuthStatus />;
	}

	if (currentFineGrainedStateName === 'AuthorizationSuccessful') {
		return <VerifiedStatus />;
	}

	if (
		currentFineGrainedStateName === 'VerificationFailedNoCoverageOrDenied' ||
		currentFineGrainedStateName === 'AuthorizationFailedDenied'
	) {
		return (
			<DeniedStatus
				verificationStatus={verificationStatus}
				onEditInsurance={onEditInsurance || (() => {})}
				onRetryVerification={onRetryVerification || (() => {})}
			/>
		);
	}

	return <VerifiedStatus />;
}
