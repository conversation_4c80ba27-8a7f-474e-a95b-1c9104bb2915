// // Export components
// export { default as PatientCreationWorkflow } from './components/patient-creation-workflow';

// Export step components
export { default as BasicInformationStep } from './components/steps/basic-information.step';
export { default as ContactsStep } from './components/steps/contacts.step';
export { default as AddressesStep } from './components/steps/addresses.step';
export { default as InsurancesStep } from './components/steps/insurances.step';
export { default as GuardiansStep } from './components/steps/guardians.step';
export { default as ClinicalConsiderationsStep } from './components/steps/clinical-considerations.step';

// Export types
export type { StepProps } from './types/state-types';
