
import { styled, CardHeader as <PERSON><PERSON><PERSON><PERSON><PERSON>eader, type CardHeaderProps, darken, lighten } from "@mui/material";

interface StyledCardHeaderProps extends CardHeaderProps {
    emphasis?: 'dark' | 'light';
    color?: 'primary' | 'secondary' | 'tertiary'
    backgroundColor?: string;
    size?: 'medium' | 'small'
}

const StyledCardHeader = styled(MuiCardHeader, {
    shouldForwardProp: (prop) =>
        !['emphasis', 'backgroundColor', 'size'].includes(prop as string)
})<StyledCardHeaderProps>(({ theme, size }) => {
    // Size-based styles
    const sizeStyles = {
        padding: size === 'medium'
            ? theme.spacing(1.5)
            : theme.spacing(0.75, 1.5),
        '& .MuiCardHeader-title': {
            fontSize: size === 'medium'
                ? theme.typography.h6.fontSize
                : theme.typography.body1.fontSize,
            fontWeight: theme.typography.fontWeightMedium
        },
        '& .MuiCardHeader-subheader': {
            fontSize: size === 'medium'
                ? theme.typography.body1.fontSize
                : theme.typography.body2.fontSize
        },
        '& .MuiCardHeader-avatar': {
            marginRight: size === 'medium' ? theme.spacing(1.5) : theme.spacing(1.25), // 12px for medium, 10px for small
            '& .MuiSvgIcon-root, & svg': {
                fontSize: size === 'medium' ? '2rem' : '1.5rem'
            },
            '& svg': {
                width: size === 'medium' ? 32 : 24,
                height: size === 'medium' ? 32 : 24
            }
        },
        '& .MuiCardHeader-action': {
            height: '100%',
            margin: 0,
            alignSelf: 'stretch',
            display: 'flex',
            alignItems: 'center'
        }
    };

    // Theme variants for light and dark emphasis
    const variantStyles = [
        {
            props: { emphasis: 'light' },
            style: {
                backgroundColor: lighten(theme.palette.primary.main, 0.85),
                color: theme.palette.getContrastText(
                    lighten(theme.palette.primary.main, 0.85)
                ),
                ...theme.applyStyles('dark', {
                    backgroundColor: lighten(theme.palette.primary.main, 0.2),
                    color: theme.palette.getContrastText(
                        lighten(theme.palette.primary.main, 0.2)
                    )
                })
            }
        },
        {
            props: { emphasis: 'dark' },
            style: {
                backgroundColor: darken(theme.palette.primary.main, 0.35),
                color: theme.palette.getContrastText(
                    darken(theme.palette.primary.main, 0.35)
                ),
                ...theme.applyStyles('dark', {
                    backgroundColor: darken(theme.palette.primary.main, 0.6),
                    color: theme.palette.getContrastText(
                        darken(theme.palette.primary.main, 0.6)
                    )
                })
            }
        }
    ];

    return {
        ...sizeStyles,
        variants: variantStyles
    };
});


export default function CardHeader({ emphasis = 'light', size = 'medium', ...props }: StyledCardHeaderProps) {
    return <StyledCardHeader emphasis={emphasis} size={size}{...props} />;
}

export type { StyledCardHeaderProps as CardHeaderProps }
