import ChipSummary from '@components/chip-summary'
import MainCardContainer from '@components/main-container/main-card-container'
import StatusBanner from '@components/status.banner'
import { Status } from '@config/status'
import { Stack, Typography } from '@mui/material'
import { createFileRoute } from '@tanstack/react-router'
import { CalendarCheck, Timer } from 'lucide-react'

export const Route = createFileRoute(
  '/_dashboard/patients/$patientId/schedule/appointment-creation/dashboard/preview',
)({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <MainCardContainer
      title='Appointment Created'
      color='success'
      primaryActionType='Edit'
      emphasis='high'
      icon={<CalendarCheck />}
    >

      <Stack gap={2}>
        <StatusBanner
          title='Created by Patient: May 7, 2025 at 10:36 AM'
          subTitle='The patient has been successfully scheduled for their sleep study on October 22, 2025.'
          status={Status.Success}
        />

        <MainCardContainer title='Confirmed '>
          <ChipSummary
            hideSeperator
            items={[
              {
                value: 'Female technician preferred'
              },
              {
                value: 'Patient needs interpreter (Specify: Spanish)'
              },
              {
                value: 'Equipment Reserved'
              },
              {
                value: 'Room Reserved'
              }
            ]}
          />
        </MainCardContainer>
        <MainCardContainer title='Appointment Details' >
          <ChipSummary
            items={[
              {
                label: 'Date',
                value: ' October 22, 2025'
              },
              {
                label: 'Time',
                value: '11:00 AM - 5:00 PM'
              },
              {
                label: 'Study Type',
                value: 'PSG (Polysomnography)'
              },
              {
                label: 'Location',
                value: ' Northwest Sleep Center'
              },
              {
                label: 'Scheduled By',
                value: 'John Scott'
              }
            ]}
          />
        </MainCardContainer>
        <MainCardContainer title='Appointment Notes' primaryActionType='Add'>
          <MainCardContainer title='Added by John Scott, May 7, 2025 at 9:15 AM' primaryActionType='Edit'>
            <Typography>
              Attempted to call patient at primary number (541) 600-8741. No answer after 6 rings. This is the second attempt to reach the patient. Left no message as previous voicemail was already sent. Will attempt again tomorrow afternoon at 2:30 PM. If no contact by May 9, will need to escalate to physician.
            </Typography>
          </MainCardContainer>
        </MainCardContainer>
        <StatusBanner
          title='Created by Patient: May 7, 2025 at 10:36 AM'
          subTitle='The patient has been successfully scheduled for their sleep study on October 22, 2025.'
          status={Status.Warning}
          renderStatusIcon={({ statusColor }) => <Timer color={statusColor.dark} />}
        />
      </Stack>
    </MainCardContainer>
  )
}
