import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { TanStackRouterVite } from '@tanstack/router-plugin/vite';
import * as path from 'path';

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
	const env = loadEnv(mode, process.cwd(), 'VITE_');

	return {
		plugins: [TanStackRouterVite({ autoCodeSplitting: true }), react()],
		resolve: {
			alias: {
				'@components': path.resolve(__dirname, 'src/components'),
				'@hooks': path.resolve(__dirname, 'src/hooks'),
				'@config': path.resolve(__dirname, 'src/config'),
				'@auth': path.resolve(__dirname, 'src/auth'),
				'@utils': path.resolve(__dirname, 'src/utils'),
				'@schemas': path.resolve(__dirname, 'src/schemas'),
				'@contexts': path.resolve(__dirname, 'src/contexts'),
				'@workflows': path.resolve(__dirname, 'src/workflows'),
				'@client': path.resolve(__dirname, 'src/client'),
				'@app-types': path.resolve(__dirname, 'src/app-types'),
				'@features': path.resolve(__dirname, 'src/features'),
			},
		},
		server: {
			proxy: {
				'/api': {
					target: 'http://localhost:4000',
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/api/, ''),
				},
			},
			cors: true,
		},
		define: {
			'import.meta.env.VITE_APP_VERSION': JSON.stringify(env.VITE_APP_VERSION),
			'import.meta.env.VITE_TFVC_CHANGESET': JSON.stringify(env.VITE_TFVC_CHANGESET),
		},
	};
});
