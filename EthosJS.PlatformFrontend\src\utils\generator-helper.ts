import { Field } from '@components/workflow(deprecated)/step-form-generator';

export const getFieldDefaultValue = (field: Field): Record<string, any> => {
	const { name } = field;
	if (field.type === 'Array') {
		const { innerType } = field;
		return {
			[name]: [getFieldDefaultValue(innerType)],
		};
	}
	if (field.type === 'Form') {
		return {
			[name]: field.fields.reduce((acc, field) => {
				return {
					...acc,
					[field.name]: getFieldDefaultValue(field),
				};
			}, {}),
		};
	}
	if (field.type === 'Options') {
		return {
			[name]: null,
		};
	}
	if (field.type === 'Checkbox') {
		return {
			[name]: false,
		};
	}
	return {
		[name]: '',
	};
};

/**
 * Converts a PascalCase string to kebab-case
 */
export function pascalToKebabCase(str: string): string {
	return str
		.replace(/([A-Z])/g, '-$1') // Insert a hyphen before each capital letter
		.toLowerCase() // Convert to lowercase
		.replace(/^-/, ''); // Remove hyphen at the beginning (if exists)
}

/**
 * Converts a PascalCase string to Space Separated Capitalized Words
 * Example: "HelloWorldTest" -> "Hello World Test"
 */
/**
 * Converts a PascalCase string to Space Separated Capitalized Words
 * Examples:
 * "HelloWorld" -> "Hello World"
 * "MyAPIComponent" -> "My API Component"
 * "UUIDGenerator" -> "UUID Generator"
 */
export function pascalToSpacedWords(str: string): string {
	return (
		str
			// Insert a space before capital letters, but only if the next character is lowercase
			.replace(/([A-Z])([A-Z][a-z])/g, '$1 $2')
			// Insert a space between a lowercase and a capital letter
			.replace(/([a-z])([A-Z])/g, '$1 $2')
			// Remove space at the beginning (if exists)
			.replace(/^\s/, '')
			// Ensure first letter is capitalized
			.replace(/^[a-z]/, (c) => c.toUpperCase())
	);
}

/**
 * Capitalizes the first letter of a string
 * Examples:
 * "hello" -> "Hello"
 * "world test" -> "World test"
 * "" -> ""
 */
export function capitalizeFirstLetter(str: string): string {
	if (!str) return str;
	return str.charAt(0).toUpperCase() + str.slice(1);
}
