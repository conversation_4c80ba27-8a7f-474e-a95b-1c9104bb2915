import { SystemTextJsonNodesJsonNode } from '@client/workflows';

type StateTypes = 'Complete' | 'NotStarted' | 'InProgress' | 'Error' | 'Warning';
type StepNames = 'InsuranceInformation' | 'VerificationStatus';

type InsuranceVerificationState = {
	flowState: {
		status: StateTypes;
		progress: number;
		lastUpdate: string;
	};
	stepState: Record<StepNames, StateTypes>;
} & SystemTextJsonNodesJsonNode;

interface StepPropsNew {
	studyId: string;
	successCallback: () => void;
}

export type { InsuranceVerificationState, StateTypes, StepNames, StepPropsNew };
